﻿<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<StiSerializer version="1.02" type="Net" application="StiReport">
  <Dictionary Ref="1" type="Dictionary" isKey="true">
    <BusinessObjects isList="true" count="0" />
    <Databases isList="true" count="0" />
    <DataSources isList="true" count="0" />
    <Relations isList="true" count="0" />
    <Report isRef="0" />
    <Variables isList="true" count="50">
      <value>,西药费,西药费,System.Double,,False,False</value>
      <value>,化验费,化验费,System.Double,,False,False</value>
      <value>,手术费,手术费,System.Double,,False,False</value>
      <value>,中成药,中成药,System.Double,,False,False</value>
      <value>,蒙药费,蒙药费,System.Double,,False,False</value>
      <value>,放射费,放射费,System.Double,,False,False</value>
      <value>,中草药,中草药,System.Double,,False,False</value>
      <value>,检查费,检查费,System.Double,,False,False</value>
      <value>,处置费,处置费,System.Double,,False,False</value>
      <value>,其他费,其他费,System.Double,,False,False</value>
      <value>,金额合计,金额合计,System.Double,,False,False</value>
      <value>,人民币大写,人民币大写,System.String,,False,False</value>
      <value>,日期,日期,System.String,,False,False</value>
      <value>,收款员,收款员,System.String,,False,False</value>
      <value>,姓名1,姓名1,System.String,,False,False</value>
      <value>,项目1,项目1,System.String,,False,False</value>
      <value>,金额1,金额1,System.Double,,False,False</value>
      <value>,日期1,日期1,System.String,,False,False</value>
      <value>,收款员1,收款员1,System.String,,False,False</value>
      <value>,微机号1,微机号1,System.String,,False,False</value>
      <value>,姓名2,姓名2,System.String,,False,False</value>
      <value>,项目2,项目2,System.String,,False,False</value>
      <value>,金额2,金额2,System.Double,,False,False</value>
      <value>,日期2,日期2,System.String,,False,False</value>
      <value>,收款员2,收款员2,System.String,,False,False</value>
      <value>,微机号2,微机号2,System.String,,False,False</value>
      <value>,姓名3,姓名3,System.String,,False,False</value>
      <value>,项目3,项目3,System.String,,False,False</value>
      <value>,金额3,金额3,System.Double,,False,False</value>
      <value>,日期3,日期3,System.String,,False,False</value>
      <value>,收款员3,收款员3,System.String,,False,False</value>
      <value>,微机号3,微机号3,System.String,,False,False</value>
      <value>,项目4,项目4,System.String,,False,False</value>
      <value>,金额4,金额4,System.Double,,False,False</value>
      <value>,日期4,日期4,System.String,,False,False</value>
      <value>,收款员4,收款员4,System.String,,False,False</value>
      <value>,微机号4,微机号4,System.String,,False,False</value>
      <value>,医院名称,医院名称,System.String,,False,False</value>
      <value>,卫材费,卫材费,System.Double,,False,False</value>
      <value>,变量1,变量1,System.String,,False,False</value>
      <value>,变量1金额,变量1金额,System.Double,,False,False</value>
      <value>,变量2,变量2,System.String,,False,False</value>
      <value>,变量2金额,变量2金额,System.Double,,False,False</value>
      <value>,变量3,变量3,System.String,,False,False</value>
      <value>,变量3金额,变量3金额,System.Double,,False,False</value>
      <value>,变量4,变量4,System.String,,False,False</value>
      <value>,变量4金额,变量4金额,System.Double,,False,False</value>
      <value>,微机号,微机号,System.String,,False,False</value>
      <value>,姓名4,姓名4,System.String,,False,False</value>
      <value>,姓名,姓名,姓名,System.String,,False,False,False</value>
    </Variables>
  </Dictionary>
  <EngineVersion>EngineV2</EngineVersion>
  <GlobalizationStrings isList="true" count="0" />
  <MetaTags isList="true" count="0" />
  <Pages isList="true" count="1">
    <Page1 Ref="2" type="Page" isKey="true">
      <Border>None;Black;2;Solid;False;4;Black</Border>
      <Brush>Transparent</Brush>
      <Components isList="true" count="8">
        <Text1 Ref="3" type="Text" isKey="true">
          <Border>None;[105:105:105];1;Solid;False;4;[105:105:105]</Border>
          <Brush>Transparent</Brush>
          <ClientRectangle>2.4,0.6,3.6,0.6</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,9</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text1</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>内 蒙 古 通 辽 市</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text1>
        <Panel1 Ref="4" type="Stimulsoft.Report.Components.StiPanel" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0.2,2.2,12.6,6.2</ClientRectangle>
          <Components isList="true" count="5">
            <Text8 Ref="5" type="Text" isKey="true">
              <Border>None;[105:105:105];1;Solid;False;4;[105:105:105]</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>1.2,5.4,5.49,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,9</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text8</Name>
              <Page isRef="2" />
              <Parent isRef="4" />
              <Text>{日期}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text8>
            <Text9 Ref="6" type="Text" isKey="true">
              <Border>None;[105:105:105];1;Solid;False;4;[105:105:105]</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0.4,5.4,0.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,9</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text9</Name>
              <Page isRef="2" />
              <Parent isRef="4" />
              <Text>日期</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text9>
            <Text7 Ref="7" type="Text" isKey="true">
              <Border>None;[105:105:105];1;Solid;False;4;[105:105:105]</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>8,5.4,1.6,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,9</Font>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text7</Name>
              <Page isRef="2" />
              <Parent isRef="4" />
              <Text>{收款员}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text7>
            <Text10 Ref="8" type="Text" isKey="true">
              <Border>None;[105:105:105];1;Solid;False;4;[105:105:105]</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>6.8,5.4,1.2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,9</Font>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text10</Name>
              <Page isRef="2" />
              <Parent isRef="4" />
              <Text>收款员</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text10>
            <Table1 Ref="9" type="Stimulsoft.Report.Components.Table.StiTable" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0.4,12.6,5</ClientRectangle>
              <ColumnCount>6</ColumnCount>
              <Components isList="true" count="48">
                <Table1_Cell1 Ref="10" type="Stimulsoft.Report.Components.Table.StiTableCell" isKey="true">
                  <Border>All;DimGray;1;Solid;False;4;DimGray</Border>
                  <Brush>Transparent</Brush>
                  <ClientRectangle>0,0,1.6,0.6</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Font>Arial,9</Font>
                  <HorAlignment>Center</HorAlignment>
                  <ID>0</ID>
                  <JoinCells isList="true" count="0" />
                  <Margins>0,0,0,0</Margins>
                  <Name>Table1_Cell1</Name>
                  <Page isRef="2" />
                  <Parent isRef="9" />
                  <Restrictions>AllowMove, AllowSelect, AllowChange</Restrictions>
                  <Text>项目</Text>
                  <TextBrush>Black</TextBrush>
                  <Type>Expression</Type>
                  <VertAlignment>Center</VertAlignment>
                </Table1_Cell1>
                <Table1_Cell2 Ref="11" type="Stimulsoft.Report.Components.Table.StiTableCell" isKey="true">
                  <Border>All;[105:105:105];1;Solid;False;4;[105:105:105]</Border>
                  <Brush>Transparent</Brush>
                  <ClientRectangle>1.6,0,2.4,0.6</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Font>Arial,9</Font>
                  <HorAlignment>Center</HorAlignment>
                  <ID>1</ID>
                  <JoinCells isList="true" count="0" />
                  <Margins>0,0,0,0</Margins>
                  <Name>Table1_Cell2</Name>
                  <Page isRef="2" />
                  <Parent isRef="9" />
                  <Restrictions>AllowMove, AllowSelect, AllowChange</Restrictions>
                  <Text>金额</Text>
                  <TextBrush>Black</TextBrush>
                  <Type>Expression</Type>
                  <VertAlignment>Center</VertAlignment>
                </Table1_Cell2>
                <Table1_Cell3 Ref="12" type="Stimulsoft.Report.Components.Table.StiTableCell" isKey="true">
                  <Border>All;[105:105:105];1;Solid;False;4;[105:105:105]</Border>
                  <Brush>Transparent</Brush>
                  <ClientRectangle>4,0,1.6,0.6</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Font>Arial,9</Font>
                  <HorAlignment>Center</HorAlignment>
                  <ID>2</ID>
                  <JoinCells isList="true" count="0" />
                  <Margins>0,0,0,0</Margins>
                  <Name>Table1_Cell3</Name>
                  <Page isRef="2" />
                  <Parent isRef="9" />
                  <Restrictions>AllowMove, AllowSelect, AllowChange</Restrictions>
                  <Text>项目</Text>
                  <TextBrush>Black</TextBrush>
                  <Type>Expression</Type>
                  <VertAlignment>Center</VertAlignment>
                </Table1_Cell3>
                <Table1_Cell4 Ref="13" type="Stimulsoft.Report.Components.Table.StiTableCell" isKey="true">
                  <Border>All;[105:105:105];1;Solid;False;4;[105:105:105]</Border>
                  <Brush>Transparent</Brush>
                  <ClientRectangle>5.6,0,2.8,0.6</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Font>Arial,9</Font>
                  <HorAlignment>Center</HorAlignment>
                  <ID>3</ID>
                  <JoinCells isList="true" count="0" />
                  <Margins>0,0,0,0</Margins>
                  <Name>Table1_Cell4</Name>
                  <Page isRef="2" />
                  <Parent isRef="9" />
                  <Restrictions>AllowMove, AllowSelect, AllowChange</Restrictions>
                  <Text>金额</Text>
                  <TextBrush>Black</TextBrush>
                  <Type>Expression</Type>
                  <VertAlignment>Center</VertAlignment>
                </Table1_Cell4>
                <Table1_Cell5 Ref="14" type="Stimulsoft.Report.Components.Table.StiTableCell" isKey="true">
                  <Border>All;[105:105:105];1;Solid;False;4;[105:105:105]</Border>
                  <Brush>Transparent</Brush>
                  <ClientRectangle>8.4,0,1.4,0.6</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Font>Arial,9</Font>
                  <HorAlignment>Center</HorAlignment>
                  <ID>4</ID>
                  <JoinCells isList="true" count="0" />
                  <Margins>0,0,0,0</Margins>
                  <Name>Table1_Cell5</Name>
                  <Page isRef="2" />
                  <Parent isRef="9" />
                  <Restrictions>AllowMove, AllowSelect, AllowChange</Restrictions>
                  <Text>项目</Text>
                  <TextBrush>Black</TextBrush>
                  <Type>Expression</Type>
                  <VertAlignment>Center</VertAlignment>
                </Table1_Cell5>
                <Table1_Cell6 Ref="15" type="Stimulsoft.Report.Components.Table.StiTableCell" isKey="true">
                  <Border>All;[105:105:105];1;Solid;False;4;[105:105:105]</Border>
                  <Brush>Transparent</Brush>
                  <ClientRectangle>9.8,0,2.8,0.6</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Font>Arial,9</Font>
                  <HorAlignment>Center</HorAlignment>
                  <ID>25</ID>
                  <JoinCells isList="true" count="0" />
                  <Margins>0,0,0,0</Margins>
                  <Name>Table1_Cell6</Name>
                  <Page isRef="2" />
                  <Parent isRef="9" />
                  <Restrictions>AllowMove, AllowSelect, AllowChange</Restrictions>
                  <Text>金额</Text>
                  <TextBrush>Black</TextBrush>
                  <Type>Expression</Type>
                  <VertAlignment>Center</VertAlignment>
                </Table1_Cell6>
                <Table1_Cell7 Ref="16" type="Stimulsoft.Report.Components.Table.StiTableCell" isKey="true">
                  <Border>All;[105:105:105];1;Solid;False;4;[105:105:105]</Border>
                  <Brush>Transparent</Brush>
                  <ClientRectangle>0,0.6,1.6,0.6</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Font>Arial,9</Font>
                  <HorAlignment>Center</HorAlignment>
                  <ID>5</ID>
                  <JoinCells isList="true" count="0" />
                  <Margins>0,0,0,0</Margins>
                  <Name>Table1_Cell7</Name>
                  <Page isRef="2" />
                  <Parent isRef="9" />
                  <Restrictions>AllowMove, AllowSelect, AllowChange</Restrictions>
                  <Text>西药费</Text>
                  <TextBrush>Black</TextBrush>
                  <Type>Expression</Type>
                  <VertAlignment>Center</VertAlignment>
                </Table1_Cell7>
                <Table1_Cell8 Ref="17" type="Stimulsoft.Report.Components.Table.StiTableCell" isKey="true">
                  <Border>All;[105:105:105];1;Solid;False;4;[105:105:105]</Border>
                  <Brush>Transparent</Brush>
                  <ClientRectangle>1.6,0.6,2.4,0.6</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Font>Arial,9</Font>
                  <HorAlignment>Center</HorAlignment>
                  <ID>6</ID>
                  <JoinCells isList="true" count="0" />
                  <Margins>0,0,0,0</Margins>
                  <Name>Table1_Cell8</Name>
                  <Page isRef="2" />
                  <Parent isRef="9" />
                  <Restrictions>AllowMove, AllowSelect, AllowChange</Restrictions>
                  <Text>{西药费}</Text>
                  <TextBrush>Black</TextBrush>
                  <Type>Expression</Type>
                  <VertAlignment>Center</VertAlignment>
                </Table1_Cell8>
                <Table1_Cell9 Ref="18" type="Stimulsoft.Report.Components.Table.StiTableCell" isKey="true">
                  <Border>All;[105:105:105];1;Solid;False;4;[105:105:105]</Border>
                  <Brush>Transparent</Brush>
                  <ClientRectangle>4,0.6,1.6,0.6</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Font>Arial,9</Font>
                  <HorAlignment>Center</HorAlignment>
                  <ID>7</ID>
                  <JoinCells isList="true" count="0" />
                  <Margins>0,0,0,0</Margins>
                  <Name>Table1_Cell9</Name>
                  <Page isRef="2" />
                  <Parent isRef="9" />
                  <Restrictions>AllowMove, AllowSelect, AllowChange</Restrictions>
                  <Text>化验费</Text>
                  <TextBrush>Black</TextBrush>
                  <Type>Expression</Type>
                  <VertAlignment>Center</VertAlignment>
                </Table1_Cell9>
                <Table1_Cell10 Ref="19" type="Stimulsoft.Report.Components.Table.StiTableCell" isKey="true">
                  <Border>All;[105:105:105];1;Solid;False;4;[105:105:105]</Border>
                  <Brush>Transparent</Brush>
                  <ClientRectangle>5.6,0.6,2.8,0.6</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Font>Arial,9</Font>
                  <HorAlignment>Center</HorAlignment>
                  <ID>8</ID>
                  <JoinCells isList="true" count="0" />
                  <Margins>0,0,0,0</Margins>
                  <Name>Table1_Cell10</Name>
                  <Page isRef="2" />
                  <Parent isRef="9" />
                  <Restrictions>AllowMove, AllowSelect, AllowChange</Restrictions>
                  <Text>{化验费}</Text>
                  <TextBrush>Black</TextBrush>
                  <Type>Expression</Type>
                  <VertAlignment>Center</VertAlignment>
                </Table1_Cell10>
                <Table1_Cell11 Ref="20" type="Stimulsoft.Report.Components.Table.StiTableCell" isKey="true">
                  <Border>All;[105:105:105];1;Solid;False;4;[105:105:105]</Border>
                  <Brush>Transparent</Brush>
                  <ClientRectangle>8.4,0.6,1.4,0.6</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Font>Arial,9</Font>
                  <HorAlignment>Center</HorAlignment>
                  <ID>9</ID>
                  <JoinCells isList="true" count="0" />
                  <Margins>0,0,0,0</Margins>
                  <Name>Table1_Cell11</Name>
                  <Page isRef="2" />
                  <Parent isRef="9" />
                  <Restrictions>AllowMove, AllowSelect, AllowChange</Restrictions>
                  <Text>手术费</Text>
                  <TextBrush>Black</TextBrush>
                  <Type>Expression</Type>
                  <VertAlignment>Center</VertAlignment>
                </Table1_Cell11>
                <Table1_Cell12 Ref="21" type="Stimulsoft.Report.Components.Table.StiTableCell" isKey="true">
                  <Border>All;[105:105:105];1;Solid;False;4;[105:105:105]</Border>
                  <Brush>Transparent</Brush>
                  <ClientRectangle>9.8,0.6,2.8,0.6</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Font>Arial,9</Font>
                  <HorAlignment>Center</HorAlignment>
                  <ID>26</ID>
                  <JoinCells isList="true" count="0" />
                  <Margins>0,0,0,0</Margins>
                  <Name>Table1_Cell12</Name>
                  <Page isRef="2" />
                  <Parent isRef="9" />
                  <Restrictions>AllowMove, AllowSelect, AllowChange</Restrictions>
                  <Text>{手术费}</Text>
                  <TextBrush>Black</TextBrush>
                  <Type>Expression</Type>
                  <VertAlignment>Center</VertAlignment>
                </Table1_Cell12>
                <Table1_Cell13 Ref="22" type="Stimulsoft.Report.Components.Table.StiTableCell" isKey="true">
                  <Border>All;[105:105:105];1;Solid;False;4;[105:105:105]</Border>
                  <Brush>Transparent</Brush>
                  <ClientRectangle>0,1.2,1.6,0.6</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Font>Arial,9</Font>
                  <HorAlignment>Center</HorAlignment>
                  <ID>10</ID>
                  <JoinCells isList="true" count="0" />
                  <Margins>0,0,0,0</Margins>
                  <Name>Table1_Cell13</Name>
                  <Page isRef="2" />
                  <Parent isRef="9" />
                  <Restrictions>AllowMove, AllowSelect, AllowChange</Restrictions>
                  <Text>中成药</Text>
                  <TextBrush>Black</TextBrush>
                  <Type>Expression</Type>
                  <VertAlignment>Center</VertAlignment>
                </Table1_Cell13>
                <Table1_Cell14 Ref="23" type="Stimulsoft.Report.Components.Table.StiTableCell" isKey="true">
                  <Border>All;[105:105:105];1;Solid;False;4;[105:105:105]</Border>
                  <Brush>Transparent</Brush>
                  <ClientRectangle>1.6,1.2,2.4,0.6</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Font>Arial,9</Font>
                  <HorAlignment>Center</HorAlignment>
                  <ID>11</ID>
                  <JoinCells isList="true" count="0" />
                  <Margins>0,0,0,0</Margins>
                  <Name>Table1_Cell14</Name>
                  <Page isRef="2" />
                  <Parent isRef="9" />
                  <Restrictions>AllowMove, AllowSelect, AllowChange</Restrictions>
                  <Text>{中成药}</Text>
                  <TextBrush>Black</TextBrush>
                  <Type>Expression</Type>
                  <VertAlignment>Center</VertAlignment>
                </Table1_Cell14>
                <Table1_Cell15 Ref="24" type="Stimulsoft.Report.Components.Table.StiTableCell" isKey="true">
                  <Border>All;[105:105:105];1;Solid;False;4;[105:105:105]</Border>
                  <Brush>Transparent</Brush>
                  <ClientRectangle>4,1.2,1.6,0.6</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Font>Arial,9</Font>
                  <HorAlignment>Center</HorAlignment>
                  <ID>12</ID>
                  <JoinCells isList="true" count="0" />
                  <Margins>0,0,0,0</Margins>
                  <Name>Table1_Cell15</Name>
                  <Page isRef="2" />
                  <Parent isRef="9" />
                  <Restrictions>AllowMove, AllowSelect, AllowChange</Restrictions>
                  <Text>蒙药费</Text>
                  <TextBrush>Black</TextBrush>
                  <Type>Expression</Type>
                  <VertAlignment>Center</VertAlignment>
                </Table1_Cell15>
                <Table1_Cell16 Ref="25" type="Stimulsoft.Report.Components.Table.StiTableCell" isKey="true">
                  <Border>All;[105:105:105];1;Solid;False;4;[105:105:105]</Border>
                  <Brush>Transparent</Brush>
                  <ClientRectangle>5.6,1.2,2.8,0.6</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Font>Arial,9</Font>
                  <HorAlignment>Center</HorAlignment>
                  <ID>13</ID>
                  <JoinCells isList="true" count="0" />
                  <Margins>0,0,0,0</Margins>
                  <Name>Table1_Cell16</Name>
                  <Page isRef="2" />
                  <Parent isRef="9" />
                  <Restrictions>AllowMove, AllowSelect, AllowChange</Restrictions>
                  <Text>{蒙药费}</Text>
                  <TextBrush>Black</TextBrush>
                  <Type>Expression</Type>
                  <VertAlignment>Center</VertAlignment>
                </Table1_Cell16>
                <Table1_Cell17 Ref="26" type="Stimulsoft.Report.Components.Table.StiTableCell" isKey="true">
                  <Border>All;[105:105:105];1;Solid;False;4;[105:105:105]</Border>
                  <Brush>Transparent</Brush>
                  <ClientRectangle>8.4,1.2,1.4,0.6</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Font>Arial,9</Font>
                  <HorAlignment>Center</HorAlignment>
                  <ID>14</ID>
                  <JoinCells isList="true" count="0" />
                  <Margins>0,0,0,0</Margins>
                  <Name>Table1_Cell17</Name>
                  <Page isRef="2" />
                  <Parent isRef="9" />
                  <Restrictions>AllowMove, AllowSelect, AllowChange</Restrictions>
                  <Text>放射费</Text>
                  <TextBrush>Black</TextBrush>
                  <Type>Expression</Type>
                  <VertAlignment>Center</VertAlignment>
                </Table1_Cell17>
                <Table1_Cell18 Ref="27" type="Stimulsoft.Report.Components.Table.StiTableCell" isKey="true">
                  <Border>All;[105:105:105];1;Solid;False;4;[105:105:105]</Border>
                  <Brush>Transparent</Brush>
                  <ClientRectangle>9.8,1.2,2.8,0.6</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Font>Arial,9</Font>
                  <HorAlignment>Center</HorAlignment>
                  <ID>27</ID>
                  <JoinCells isList="true" count="0" />
                  <Margins>0,0,0,0</Margins>
                  <Name>Table1_Cell18</Name>
                  <Page isRef="2" />
                  <Parent isRef="9" />
                  <Restrictions>AllowMove, AllowSelect, AllowChange</Restrictions>
                  <Text>{放射费}</Text>
                  <TextBrush>Black</TextBrush>
                  <Type>Expression</Type>
                  <VertAlignment>Center</VertAlignment>
                </Table1_Cell18>
                <Table1_Cell19 Ref="28" type="Stimulsoft.Report.Components.Table.StiTableCell" isKey="true">
                  <Border>All;[105:105:105];1;Solid;False;4;[105:105:105]</Border>
                  <Brush>Transparent</Brush>
                  <ClientRectangle>0,1.8,1.6,0.6</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Font>Arial,9</Font>
                  <HorAlignment>Center</HorAlignment>
                  <ID>15</ID>
                  <JoinCells isList="true" count="0" />
                  <Margins>0,0,0,0</Margins>
                  <Name>Table1_Cell19</Name>
                  <Page isRef="2" />
                  <Parent isRef="9" />
                  <Restrictions>AllowMove, AllowSelect, AllowChange</Restrictions>
                  <Text>中草药</Text>
                  <TextBrush>Black</TextBrush>
                  <Type>Expression</Type>
                  <VertAlignment>Center</VertAlignment>
                </Table1_Cell19>
                <Table1_Cell20 Ref="29" type="Stimulsoft.Report.Components.Table.StiTableCell" isKey="true">
                  <Border>All;[105:105:105];1;Solid;False;4;[105:105:105]</Border>
                  <Brush>Transparent</Brush>
                  <ClientRectangle>1.6,1.8,2.4,0.6</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Font>Arial,9</Font>
                  <HorAlignment>Center</HorAlignment>
                  <ID>16</ID>
                  <JoinCells isList="true" count="0" />
                  <Margins>0,0,0,0</Margins>
                  <Name>Table1_Cell20</Name>
                  <Page isRef="2" />
                  <Parent isRef="9" />
                  <Restrictions>AllowMove, AllowSelect, AllowChange</Restrictions>
                  <Text>{中草药}</Text>
                  <TextBrush>Black</TextBrush>
                  <Type>Expression</Type>
                  <VertAlignment>Center</VertAlignment>
                </Table1_Cell20>
                <Table1_Cell21 Ref="30" type="Stimulsoft.Report.Components.Table.StiTableCell" isKey="true">
                  <Border>All;[105:105:105];1;Solid;False;4;[105:105:105]</Border>
                  <Brush>Transparent</Brush>
                  <ClientRectangle>4,1.8,1.6,0.6</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Font>Arial,9</Font>
                  <HorAlignment>Center</HorAlignment>
                  <ID>17</ID>
                  <JoinCells isList="true" count="0" />
                  <Margins>0,0,0,0</Margins>
                  <Name>Table1_Cell21</Name>
                  <Page isRef="2" />
                  <Parent isRef="9" />
                  <Restrictions>AllowMove, AllowSelect, AllowChange</Restrictions>
                  <Text>检查费</Text>
                  <TextBrush>Black</TextBrush>
                  <Type>Expression</Type>
                  <VertAlignment>Center</VertAlignment>
                </Table1_Cell21>
                <Table1_Cell22 Ref="31" type="Stimulsoft.Report.Components.Table.StiTableCell" isKey="true">
                  <Border>All;[105:105:105];1;Solid;False;4;[105:105:105]</Border>
                  <Brush>Transparent</Brush>
                  <ClientRectangle>5.6,1.8,2.8,0.6</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Font>Arial,9</Font>
                  <HorAlignment>Center</HorAlignment>
                  <ID>18</ID>
                  <JoinCells isList="true" count="0" />
                  <Margins>0,0,0,0</Margins>
                  <Name>Table1_Cell22</Name>
                  <Page isRef="2" />
                  <Parent isRef="9" />
                  <Restrictions>AllowMove, AllowSelect, AllowChange</Restrictions>
                  <Text>{检查费}</Text>
                  <TextBrush>Black</TextBrush>
                  <Type>Expression</Type>
                  <VertAlignment>Center</VertAlignment>
                </Table1_Cell22>
                <Table1_Cell23 Ref="32" type="Stimulsoft.Report.Components.Table.StiTableCell" isKey="true">
                  <Border>All;[105:105:105];1;Solid;False;4;[105:105:105]</Border>
                  <Brush>Transparent</Brush>
                  <ClientRectangle>8.4,1.8,1.4,0.6</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Font>Arial,9</Font>
                  <HorAlignment>Center</HorAlignment>
                  <ID>19</ID>
                  <JoinCells isList="true" count="0" />
                  <Margins>0,0,0,0</Margins>
                  <Name>Table1_Cell23</Name>
                  <Page isRef="2" />
                  <Parent isRef="9" />
                  <Restrictions>AllowMove, AllowSelect, AllowChange</Restrictions>
                  <Text>处置费</Text>
                  <TextBrush>Black</TextBrush>
                  <Type>Expression</Type>
                  <VertAlignment>Center</VertAlignment>
                </Table1_Cell23>
                <Table1_Cell24 Ref="33" type="Stimulsoft.Report.Components.Table.StiTableCell" isKey="true">
                  <Border>All;[105:105:105];1;Solid;False;4;[105:105:105]</Border>
                  <Brush>Transparent</Brush>
                  <ClientRectangle>9.8,1.8,2.8,0.6</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Font>Arial,9</Font>
                  <HorAlignment>Center</HorAlignment>
                  <ID>28</ID>
                  <JoinCells isList="true" count="0" />
                  <Margins>0,0,0,0</Margins>
                  <Name>Table1_Cell24</Name>
                  <Page isRef="2" />
                  <Parent isRef="9" />
                  <Restrictions>AllowMove, AllowSelect, AllowChange</Restrictions>
                  <Text>{处置费}</Text>
                  <TextBrush>Black</TextBrush>
                  <Type>Expression</Type>
                  <VertAlignment>Center</VertAlignment>
                </Table1_Cell24>
                <Table1_Cell25 Ref="34" type="Stimulsoft.Report.Components.Table.StiTableCell" isKey="true">
                  <Border>All;[105:105:105];1;Solid;False;4;[105:105:105]</Border>
                  <Brush>Transparent</Brush>
                  <ClientRectangle>0,2.4,1.6,0.8</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Font>Arial,9</Font>
                  <HorAlignment>Center</HorAlignment>
                  <ID>20</ID>
                  <JoinCells isList="true" count="0" />
                  <Margins>0,0,0,0</Margins>
                  <Name>Table1_Cell25</Name>
                  <Page isRef="2" />
                  <Parent isRef="9" />
                  <Restrictions>AllowMove, AllowSelect, AllowChange</Restrictions>
                  <Text>其他费</Text>
                  <TextBrush>Black</TextBrush>
                  <Type>Expression</Type>
                  <VertAlignment>Center</VertAlignment>
                </Table1_Cell25>
                <Table1_Cell26 Ref="35" type="Stimulsoft.Report.Components.Table.StiTableCell" isKey="true">
                  <Border>All;[105:105:105];1;Solid;False;4;[105:105:105]</Border>
                  <Brush>Transparent</Brush>
                  <ClientRectangle>1.6,2.4,2.4,0.8</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Font>Arial,9</Font>
                  <HorAlignment>Center</HorAlignment>
                  <ID>21</ID>
                  <JoinCells isList="true" count="0" />
                  <Margins>0,0,0,0</Margins>
                  <Name>Table1_Cell26</Name>
                  <Page isRef="2" />
                  <Parent isRef="9" />
                  <Restrictions>AllowMove, AllowSelect, AllowChange</Restrictions>
                  <Text>{其他费}</Text>
                  <TextBrush>Black</TextBrush>
                  <Type>Expression</Type>
                  <VertAlignment>Center</VertAlignment>
                </Table1_Cell26>
                <Table1_Cell27 Ref="36" type="Stimulsoft.Report.Components.Table.StiTableCell" isKey="true">
                  <Border>All;[105:105:105];1;Solid;False;4;[105:105:105]</Border>
                  <Brush>Transparent</Brush>
                  <ClientRectangle>4,2.4,1.6,0.8</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Font>Arial,9</Font>
                  <HorAlignment>Center</HorAlignment>
                  <ID>22</ID>
                  <JoinCells isList="true" count="0" />
                  <Margins>0,0,0,0</Margins>
                  <Name>Table1_Cell27</Name>
                  <Page isRef="2" />
                  <Parent isRef="9" />
                  <Restrictions>AllowMove, AllowSelect, AllowChange</Restrictions>
                  <Text>卫材费</Text>
                  <TextBrush>Black</TextBrush>
                  <Type>Expression</Type>
                  <VertAlignment>Center</VertAlignment>
                </Table1_Cell27>
                <Table1_Cell28 Ref="37" type="Stimulsoft.Report.Components.Table.StiTableCell" isKey="true">
                  <Border>All;[105:105:105];1;Solid;False;4;[105:105:105]</Border>
                  <Brush>Transparent</Brush>
                  <ClientRectangle>5.6,2.4,2.8,0.8</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Font>Arial,9</Font>
                  <HorAlignment>Center</HorAlignment>
                  <ID>23</ID>
                  <JoinCells isList="true" count="0" />
                  <Margins>0,0,0,0</Margins>
                  <Name>Table1_Cell28</Name>
                  <Page isRef="2" />
                  <Parent isRef="9" />
                  <Restrictions>AllowMove, AllowSelect, AllowChange</Restrictions>
                  <Text>{卫材费}</Text>
                  <TextBrush>Black</TextBrush>
                  <Type>Expression</Type>
                  <VertAlignment>Center</VertAlignment>
                </Table1_Cell28>
                <Table1_Cell29 Ref="38" type="Stimulsoft.Report.Components.Table.StiTableCell" isKey="true">
                  <Border>All;[105:105:105];1;Solid;False;4;[105:105:105]</Border>
                  <Brush>Transparent</Brush>
                  <ClientRectangle>8.4,2.4,1.4,0.8</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Font>Arial,9</Font>
                  <HorAlignment>Center</HorAlignment>
                  <ID>24</ID>
                  <JoinCells isList="true" count="0" />
                  <Margins>0,0,0,0</Margins>
                  <Name>Table1_Cell29</Name>
                  <Page isRef="2" />
                  <Parent isRef="9" />
                  <Restrictions>AllowMove, AllowSelect, AllowChange</Restrictions>
                  <Text>{变量1}</Text>
                  <TextBrush>Black</TextBrush>
                  <Type>Expression</Type>
                  <VertAlignment>Center</VertAlignment>
                </Table1_Cell29>
                <Table1_Cell30 Ref="39" type="Stimulsoft.Report.Components.Table.StiTableCell" isKey="true">
                  <Border>All;[105:105:105];1;Solid;False;4;[105:105:105]</Border>
                  <Brush>Transparent</Brush>
                  <ClientRectangle>9.8,2.4,2.8,0.8</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Font>Arial,9</Font>
                  <HorAlignment>Center</HorAlignment>
                  <ID>29</ID>
                  <JoinCells isList="true" count="0" />
                  <Margins>0,0,0,0</Margins>
                  <Name>Table1_Cell30</Name>
                  <Page isRef="2" />
                  <Parent isRef="9" />
                  <Restrictions>AllowMove, AllowSelect, AllowChange</Restrictions>
                  <Text>{变量1金额}</Text>
                  <TextBrush>Black</TextBrush>
                  <Type>Expression</Type>
                  <VertAlignment>Center</VertAlignment>
                </Table1_Cell30>
                <Table1_Cell31 Ref="40" type="Stimulsoft.Report.Components.Table.StiTableCell" isKey="true">
                  <Border>All;[105:105:105];1;Solid;False;4;[105:105:105]</Border>
                  <Brush>Transparent</Brush>
                  <ClientRectangle>0,3.2,1.6,0.6</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Font>Arial,9</Font>
                  <HorAlignment>Center</HorAlignment>
                  <ID>30</ID>
                  <JoinCells isList="true" count="0" />
                  <Margins>0,0,0,0</Margins>
                  <Name>Table1_Cell31</Name>
                  <Page isRef="2" />
                  <Parent isRef="9" />
                  <Restrictions>AllowMove, AllowSelect, AllowChange</Restrictions>
                  <Text>{变量2}</Text>
                  <TextBrush>Black</TextBrush>
                  <Type>Expression</Type>
                  <VertAlignment>Center</VertAlignment>
                </Table1_Cell31>
                <Table1_Cell32 Ref="41" type="Stimulsoft.Report.Components.Table.StiTableCell" isKey="true">
                  <Border>All;[105:105:105];1;Solid;False;4;[105:105:105]</Border>
                  <Brush>Transparent</Brush>
                  <ClientRectangle>1.6,3.2,2.4,0.6</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Font>Arial,9</Font>
                  <HorAlignment>Center</HorAlignment>
                  <ID>31</ID>
                  <JoinCells isList="true" count="0" />
                  <Margins>0,0,0,0</Margins>
                  <Name>Table1_Cell32</Name>
                  <Page isRef="2" />
                  <Parent isRef="9" />
                  <Restrictions>AllowMove, AllowSelect, AllowChange</Restrictions>
                  <Text>{变量2金额}</Text>
                  <TextBrush>Black</TextBrush>
                  <Type>Expression</Type>
                  <VertAlignment>Center</VertAlignment>
                </Table1_Cell32>
                <Table1_Cell33 Ref="42" type="Stimulsoft.Report.Components.Table.StiTableCell" isKey="true">
                  <Border>All;[105:105:105];1;Solid;False;4;[105:105:105]</Border>
                  <Brush>Transparent</Brush>
                  <ClientRectangle>4,3.2,1.6,0.6</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Font>Arial,9</Font>
                  <HorAlignment>Center</HorAlignment>
                  <ID>32</ID>
                  <JoinCells isList="true" count="0" />
                  <Margins>0,0,0,0</Margins>
                  <Name>Table1_Cell33</Name>
                  <Page isRef="2" />
                  <Parent isRef="9" />
                  <Restrictions>AllowMove, AllowSelect, AllowChange</Restrictions>
                  <Text>{变量3}</Text>
                  <TextBrush>Black</TextBrush>
                  <Type>Expression</Type>
                  <VertAlignment>Center</VertAlignment>
                </Table1_Cell33>
                <Table1_Cell34 Ref="43" type="Stimulsoft.Report.Components.Table.StiTableCell" isKey="true">
                  <Border>All;[105:105:105];1;Solid;False;4;[105:105:105]</Border>
                  <Brush>Transparent</Brush>
                  <ClientRectangle>5.6,3.2,2.8,0.6</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Font>Arial,9</Font>
                  <HorAlignment>Center</HorAlignment>
                  <ID>33</ID>
                  <JoinCells isList="true" count="0" />
                  <Margins>0,0,0,0</Margins>
                  <Name>Table1_Cell34</Name>
                  <Page isRef="2" />
                  <Parent isRef="9" />
                  <Restrictions>AllowMove, AllowSelect, AllowChange</Restrictions>
                  <Text>{变量3金额}</Text>
                  <TextBrush>Black</TextBrush>
                  <Type>Expression</Type>
                  <VertAlignment>Center</VertAlignment>
                </Table1_Cell34>
                <Table1_Cell35 Ref="44" type="Stimulsoft.Report.Components.Table.StiTableCell" isKey="true">
                  <Border>All;[105:105:105];1;Solid;False;4;[105:105:105]</Border>
                  <Brush>Transparent</Brush>
                  <ClientRectangle>8.4,3.2,1.4,0.6</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Font>Arial,9</Font>
                  <HorAlignment>Center</HorAlignment>
                  <ID>34</ID>
                  <JoinCells isList="true" count="0" />
                  <Margins>0,0,0,0</Margins>
                  <Name>Table1_Cell35</Name>
                  <Page isRef="2" />
                  <Parent isRef="9" />
                  <Restrictions>AllowMove, AllowSelect, AllowChange</Restrictions>
                  <Text>{变量4}</Text>
                  <TextBrush>Black</TextBrush>
                  <Type>Expression</Type>
                  <VertAlignment>Center</VertAlignment>
                </Table1_Cell35>
                <Table1_Cell36 Ref="45" type="Stimulsoft.Report.Components.Table.StiTableCell" isKey="true">
                  <Border>All;[105:105:105];1;Solid;False;4;[105:105:105]</Border>
                  <Brush>Transparent</Brush>
                  <ClientRectangle>9.8,3.2,2.8,0.6</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Font>Arial,9</Font>
                  <HorAlignment>Center</HorAlignment>
                  <ID>35</ID>
                  <JoinCells isList="true" count="0" />
                  <Margins>0,0,0,0</Margins>
                  <Name>Table1_Cell36</Name>
                  <Page isRef="2" />
                  <Parent isRef="9" />
                  <Restrictions>AllowMove, AllowSelect, AllowChange</Restrictions>
                  <Text>{变量4金额}</Text>
                  <TextBrush>Black</TextBrush>
                  <Type>Expression</Type>
                  <VertAlignment>Center</VertAlignment>
                </Table1_Cell36>
                <Table1_Cell37 Ref="46" type="Stimulsoft.Report.Components.Table.StiTableCell" isKey="true">
                  <Border>All;[105:105:105];1;Solid;False;4;[105:105:105]</Border>
                  <Brush>Transparent</Brush>
                  <ClientRectangle>0,3.8,1.6,0.6</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Enabled>False</Enabled>
                  <Font>Arial,9</Font>
                  <HorAlignment>Center</HorAlignment>
                  <ID>36</ID>
                  <JoinCells isList="true" count="0" />
                  <Margins>0,0,0,0</Margins>
                  <Name>Table1_Cell37</Name>
                  <Page isRef="2" />
                  <Parent isRef="9" />
                  <ParentJoin>37</ParentJoin>
                  <Restrictions>AllowMove, AllowSelect, AllowChange</Restrictions>
                  <TextBrush>Black</TextBrush>
                  <VertAlignment>Center</VertAlignment>
                </Table1_Cell37>
                <Table1_Cell38 Ref="47" type="Stimulsoft.Report.Components.Table.StiTableCell" isKey="true">
                  <Border>All;[105:105:105];1;Solid;False;4;[105:105:105]</Border>
                  <Brush>Transparent</Brush>
                  <ClientRectangle>0,3.8,4,0.6</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Font>Arial,9</Font>
                  <HorAlignment>Center</HorAlignment>
                  <ID>37</ID>
                  <Join>True</Join>
                  <JoinCells isList="true" count="2">
                    <value>36</value>
                    <value>37</value>
                  </JoinCells>
                  <JoinHeight>1</JoinHeight>
                  <JoinWidth>2</JoinWidth>
                  <Margins>0,0,0,0</Margins>
                  <Name>Table1_Cell38</Name>
                  <Page isRef="2" />
                  <Parent isRef="9" />
                  <ParentJoin>37</ParentJoin>
                  <Restrictions>AllowMove, AllowSelect, AllowChange</Restrictions>
                  <Text>合计金额</Text>
                  <TextBrush>Black</TextBrush>
                  <Type>Expression</Type>
                  <VertAlignment>Center</VertAlignment>
                </Table1_Cell38>
                <Table1_Cell39 Ref="48" type="Stimulsoft.Report.Components.Table.StiTableCell" isKey="true">
                  <Border>All;[105:105:105];1;Solid;False;4;[105:105:105]</Border>
                  <Brush>Transparent</Brush>
                  <ClientRectangle>4,3.8,1.6,0.6</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Enabled>False</Enabled>
                  <Font>Arial,9</Font>
                  <HorAlignment>Center</HorAlignment>
                  <ID>38</ID>
                  <JoinCells isList="true" count="0" />
                  <Margins>0,0,0,0</Margins>
                  <Name>Table1_Cell39</Name>
                  <Page isRef="2" />
                  <Parent isRef="9" />
                  <ParentJoin>41</ParentJoin>
                  <Restrictions>AllowMove, AllowSelect, AllowChange</Restrictions>
                  <TextBrush>Black</TextBrush>
                  <VertAlignment>Center</VertAlignment>
                </Table1_Cell39>
                <Table1_Cell40 Ref="49" type="Stimulsoft.Report.Components.Table.StiTableCell" isKey="true">
                  <Border>All;[105:105:105];1;Solid;False;4;[105:105:105]</Border>
                  <Brush>Transparent</Brush>
                  <ClientRectangle>5.6,3.8,2.8,0.6</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Enabled>False</Enabled>
                  <Font>Arial,9</Font>
                  <HorAlignment>Center</HorAlignment>
                  <ID>39</ID>
                  <JoinCells isList="true" count="0" />
                  <Margins>0,0,0,0</Margins>
                  <Name>Table1_Cell40</Name>
                  <Page isRef="2" />
                  <Parent isRef="9" />
                  <ParentJoin>41</ParentJoin>
                  <Restrictions>AllowMove, AllowSelect, AllowChange</Restrictions>
                  <TextBrush>Black</TextBrush>
                  <VertAlignment>Center</VertAlignment>
                </Table1_Cell40>
                <Table1_Cell41 Ref="50" type="Stimulsoft.Report.Components.Table.StiTableCell" isKey="true">
                  <Border>All;[105:105:105];1;Solid;False;4;[105:105:105]</Border>
                  <Brush>Transparent</Brush>
                  <ClientRectangle>8.4,3.8,1.4,0.6</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Enabled>False</Enabled>
                  <Font>Arial,9</Font>
                  <HorAlignment>Center</HorAlignment>
                  <ID>40</ID>
                  <JoinCells isList="true" count="0" />
                  <Margins>0,0,0,0</Margins>
                  <Name>Table1_Cell41</Name>
                  <Page isRef="2" />
                  <Parent isRef="9" />
                  <ParentJoin>41</ParentJoin>
                  <Restrictions>AllowMove, AllowSelect, AllowChange</Restrictions>
                  <TextBrush>Black</TextBrush>
                  <VertAlignment>Center</VertAlignment>
                </Table1_Cell41>
                <Table1_Cell42 Ref="51" type="Stimulsoft.Report.Components.Table.StiTableCell" isKey="true">
                  <Border>All;[105:105:105];1;Solid;False;4;[105:105:105]</Border>
                  <Brush>Transparent</Brush>
                  <ClientRectangle>4,3.8,8.6,0.6</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Font>Arial,9</Font>
                  <HorAlignment>Center</HorAlignment>
                  <ID>41</ID>
                  <Join>True</Join>
                  <JoinCells isList="true" count="4">
                    <value>38</value>
                    <value>39</value>
                    <value>40</value>
                    <value>41</value>
                  </JoinCells>
                  <JoinHeight>1</JoinHeight>
                  <JoinWidth>4</JoinWidth>
                  <Margins>0,0,0,0</Margins>
                  <Name>Table1_Cell42</Name>
                  <Page isRef="2" />
                  <Parent isRef="9" />
                  <ParentJoin>41</ParentJoin>
                  <Restrictions>AllowMove, AllowSelect, AllowChange</Restrictions>
                  <Text>￥： {金额合计}</Text>
                  <TextBrush>Black</TextBrush>
                  <Type>Expression</Type>
                  <VertAlignment>Center</VertAlignment>
                </Table1_Cell42>
                <Table1_Cell43 Ref="52" type="Stimulsoft.Report.Components.Table.StiTableCell" isKey="true">
                  <Border>All;[105:105:105];1;Solid;False;4;[105:105:105]</Border>
                  <Brush>Transparent</Brush>
                  <ClientRectangle>0,4.4,1.6,0.6</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Enabled>False</Enabled>
                  <Font>Arial,9</Font>
                  <HorAlignment>Center</HorAlignment>
                  <ID>42</ID>
                  <JoinCells isList="true" count="0" />
                  <Margins>0,0,0,0</Margins>
                  <Name>Table1_Cell43</Name>
                  <Page isRef="2" />
                  <Parent isRef="9" />
                  <ParentJoin>43</ParentJoin>
                  <Restrictions>AllowMove, AllowSelect, AllowChange</Restrictions>
                  <TextBrush>Black</TextBrush>
                  <VertAlignment>Center</VertAlignment>
                </Table1_Cell43>
                <Table1_Cell44 Ref="53" type="Stimulsoft.Report.Components.Table.StiTableCell" isKey="true">
                  <Border>All;[105:105:105];1;Solid;False;4;[105:105:105]</Border>
                  <Brush>Transparent</Brush>
                  <ClientRectangle>0,4.4,4,0.6</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Font>Arial,9</Font>
                  <HorAlignment>Center</HorAlignment>
                  <ID>43</ID>
                  <Join>True</Join>
                  <JoinCells isList="true" count="2">
                    <value>42</value>
                    <value>43</value>
                  </JoinCells>
                  <JoinHeight>1</JoinHeight>
                  <JoinWidth>2</JoinWidth>
                  <Margins>0,0,0,0</Margins>
                  <Name>Table1_Cell44</Name>
                  <Page isRef="2" />
                  <Parent isRef="9" />
                  <ParentJoin>43</ParentJoin>
                  <Restrictions>AllowMove, AllowSelect, AllowChange</Restrictions>
                  <Text>人民币(大写)</Text>
                  <TextBrush>Black</TextBrush>
                  <Type>Expression</Type>
                  <VertAlignment>Center</VertAlignment>
                </Table1_Cell44>
                <Table1_Cell45 Ref="54" type="Stimulsoft.Report.Components.Table.StiTableCell" isKey="true">
                  <Border>All;[105:105:105];1;Solid;False;4;[105:105:105]</Border>
                  <Brush>Transparent</Brush>
                  <ClientRectangle>4,4.4,1.6,0.6</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Enabled>False</Enabled>
                  <Font>Arial,9</Font>
                  <HorAlignment>Center</HorAlignment>
                  <ID>44</ID>
                  <JoinCells isList="true" count="0" />
                  <Margins>0,0,0,0</Margins>
                  <Name>Table1_Cell45</Name>
                  <Page isRef="2" />
                  <Parent isRef="9" />
                  <ParentJoin>47</ParentJoin>
                  <Restrictions>AllowMove, AllowSelect, AllowChange</Restrictions>
                  <TextBrush>Black</TextBrush>
                  <VertAlignment>Center</VertAlignment>
                </Table1_Cell45>
                <Table1_Cell46 Ref="55" type="Stimulsoft.Report.Components.Table.StiTableCell" isKey="true">
                  <Border>All;[105:105:105];1;Solid;False;4;[105:105:105]</Border>
                  <Brush>Transparent</Brush>
                  <ClientRectangle>5.6,4.4,2.8,0.6</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Enabled>False</Enabled>
                  <Font>Arial,9</Font>
                  <HorAlignment>Center</HorAlignment>
                  <ID>45</ID>
                  <JoinCells isList="true" count="0" />
                  <Margins>0,0,0,0</Margins>
                  <Name>Table1_Cell46</Name>
                  <Page isRef="2" />
                  <Parent isRef="9" />
                  <ParentJoin>47</ParentJoin>
                  <Restrictions>AllowMove, AllowSelect, AllowChange</Restrictions>
                  <TextBrush>Black</TextBrush>
                  <VertAlignment>Center</VertAlignment>
                </Table1_Cell46>
                <Table1_Cell47 Ref="56" type="Stimulsoft.Report.Components.Table.StiTableCell" isKey="true">
                  <Border>All;[105:105:105];1;Solid;False;4;[105:105:105]</Border>
                  <Brush>Transparent</Brush>
                  <ClientRectangle>8.4,4.4,1.4,0.6</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Enabled>False</Enabled>
                  <Font>Arial,9</Font>
                  <HorAlignment>Center</HorAlignment>
                  <ID>46</ID>
                  <JoinCells isList="true" count="0" />
                  <Margins>0,0,0,0</Margins>
                  <Name>Table1_Cell47</Name>
                  <Page isRef="2" />
                  <Parent isRef="9" />
                  <ParentJoin>47</ParentJoin>
                  <Restrictions>AllowMove, AllowSelect, AllowChange</Restrictions>
                  <TextBrush>Black</TextBrush>
                  <VertAlignment>Center</VertAlignment>
                </Table1_Cell47>
                <Table1_Cell48 Ref="57" type="Stimulsoft.Report.Components.Table.StiTableCell" isKey="true">
                  <Border>All;[105:105:105];1;Solid;False;4;[105:105:105]</Border>
                  <Brush>Transparent</Brush>
                  <ClientRectangle>4,4.4,8.6,0.6</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Font>Arial,9</Font>
                  <HorAlignment>Center</HorAlignment>
                  <ID>47</ID>
                  <Join>True</Join>
                  <JoinCells isList="true" count="4">
                    <value>44</value>
                    <value>45</value>
                    <value>46</value>
                    <value>47</value>
                  </JoinCells>
                  <JoinHeight>1</JoinHeight>
                  <JoinWidth>4</JoinWidth>
                  <Margins>0,0,0,0</Margins>
                  <Name>Table1_Cell48</Name>
                  <Page isRef="2" />
                  <Parent isRef="9" />
                  <ParentJoin>47</ParentJoin>
                  <Restrictions>AllowMove, AllowSelect, AllowChange</Restrictions>
                  <Text>{人民币大写}</Text>
                  <TextBrush>Black</TextBrush>
                  <Type>Expression</Type>
                  <VertAlignment>Center</VertAlignment>
                </Table1_Cell48>
              </Components>
              <Conditions isList="true" count="0" />
              <Filters isList="true" count="0" />
              <MinHeight>1.6</MinHeight>
              <Name>Table1</Name>
              <NumberID>48</NumberID>
              <Page isRef="2" />
              <Parent isRef="4" />
              <RowCount>8</RowCount>
              <Sort isList="true" count="0" />
            </Table1>
          </Components>
          <Conditions isList="true" count="0" />
          <Name>Panel1</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
        </Panel1>
        <Text2 Ref="58" type="Text" isKey="true">
          <Border>None;[105:105:105];1;Solid;False;4;[105:105:105]</Border>
          <Brush>Transparent</Brush>
          <ClientRectangle>1.6,1.2,4.8,0.6</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,9,Bold</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text2</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>医院门诊收费专用票据</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text2>
        <Text3 Ref="59" type="Text" isKey="true">
          <Border>None;[105:105:105];1;Solid;False;4;[105:105:105]</Border>
          <Brush>Transparent</Brush>
          <ClientRectangle>1.6,1.8,1.6,0.4</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,9</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text3</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{姓名}</Text>
          <TextBrush>Black</TextBrush>
        </Text3>
        <Text4 Ref="60" type="Text" isKey="true">
          <Border>None;[105:105:105];1;Solid;False;4;[105:105:105]</Border>
          <Brush>Transparent</Brush>
          <ClientRectangle>0.6,1.8,1,0.4</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,9</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text4</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>姓名</Text>
          <TextBrush>Black</TextBrush>
        </Text4>
        <Text5 Ref="61" type="Text" isKey="true">
          <Border>None;[105:105:105];1;Solid;False;4;[105:105:105]</Border>
          <Brush>Transparent</Brush>
          <ClientRectangle>4.79,1.8,7.83,0.4</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,9</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text5</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{医院名称}</Text>
          <TextBrush>Black</TextBrush>
        </Text5>
        <Text6 Ref="62" type="Text" isKey="true">
          <Border>None;[105:105:105];1;Solid;False;4;[105:105:105]</Border>
          <Brush>Transparent</Brush>
          <ClientRectangle>3.2,1.8,1.6,0.4</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,9</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text6</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>医院名称</Text>
          <TextBrush>Black</TextBrush>
        </Text6>
        <Panel2 Ref="63" type="Stimulsoft.Report.Components.StiPanel" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>13,0.6,5.98,8.23</ClientRectangle>
          <Components isList="true" count="1">
            <Table2 Ref="64" type="Stimulsoft.Report.Components.Table.StiTable" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0.4,5.98,7.17</ClientRectangle>
              <ColumnCount>4</ColumnCount>
              <Components isList="true" count="56">
                <Table2_Cell1 Ref="65" type="Stimulsoft.Report.Components.Table.StiTableCell" isKey="true">
                  <Border>None;[105:105:105];1;Solid;False;4;[105:105:105]</Border>
                  <Brush>Transparent</Brush>
                  <ClientRectangle>0,0,1.32,0.55</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Enabled>False</Enabled>
                  <Font>Arial,9</Font>
                  <HorAlignment>Center</HorAlignment>
                  <ID>0</ID>
                  <JoinCells isList="true" count="0" />
                  <Margins>0,0,0,0</Margins>
                  <Name>Table2_Cell1</Name>
                  <Page isRef="2" />
                  <Parent isRef="64" />
                  <ParentJoin>1</ParentJoin>
                  <Restrictions>AllowMove, AllowSelect, AllowChange</Restrictions>
                  <TextBrush>Black</TextBrush>
                  <VertAlignment>Center</VertAlignment>
                </Table2_Cell1>
                <Table2_Cell2 Ref="66" type="Stimulsoft.Report.Components.Table.StiTableCell" isKey="true">
                  <Border>None;[105:105:105];1;Solid;False;4;[105:105:105]</Border>
                  <Brush>Transparent</Brush>
                  <ClientRectangle>1.2,0,1.53,0.55</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Font>Arial,9</Font>
                  <ID>1</ID>
                  <Join>True</Join>
                  <JoinCells isList="true" count="2">
                    <value>0</value>
                    <value>1</value>
                  </JoinCells>
                  <JoinHeight>1</JoinHeight>
                  <JoinWidth>2</JoinWidth>
                  <Margins>0,0,0,0</Margins>
                  <Name>Table2_Cell2</Name>
                  <Page isRef="2" />
                  <Parent isRef="64" />
                  <ParentJoin>1</ParentJoin>
                  <Restrictions>AllowMove, AllowSelect, AllowChange</Restrictions>
                  <Text>收费凭证</Text>
                  <TextBrush>Black</TextBrush>
                  <Type>Expression</Type>
                  <VertAlignment>Center</VertAlignment>
                </Table2_Cell2>
                <Table2_Cell3 Ref="67" type="Stimulsoft.Report.Components.Table.StiTableCell" isKey="true">
                  <Border>None;[105:105:105];1;Solid;False;4;[105:105:105]</Border>
                  <Brush>Transparent</Brush>
                  <ClientRectangle>2.73,0,1.31,0.55</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Enabled>False</Enabled>
                  <Font>Arial,9</Font>
                  <HorAlignment>Center</HorAlignment>
                  <ID>2</ID>
                  <JoinCells isList="true" count="0" />
                  <Margins>0,0,0,0</Margins>
                  <Name>Table2_Cell3</Name>
                  <Page isRef="2" />
                  <Parent isRef="64" />
                  <ParentJoin>3</ParentJoin>
                  <Restrictions>AllowMove, AllowSelect, AllowChange</Restrictions>
                  <TextBrush>Black</TextBrush>
                  <VertAlignment>Center</VertAlignment>
                </Table2_Cell3>
                <Table2_Cell4 Ref="68" type="Stimulsoft.Report.Components.Table.StiTableCell" isKey="true">
                  <Border>None;[105:105:105];1;Solid;False;4;[105:105:105]</Border>
                  <Brush>Transparent</Brush>
                  <ClientRectangle>3.93,0,2.02,0.55</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Font>Arial,9</Font>
                  <ID>3</ID>
                  <Join>True</Join>
                  <JoinCells isList="true" count="2">
                    <value>2</value>
                    <value>3</value>
                  </JoinCells>
                  <JoinHeight>1</JoinHeight>
                  <JoinWidth>2</JoinWidth>
                  <Margins>0,0,0,0</Margins>
                  <Name>Table2_Cell4</Name>
                  <Page isRef="2" />
                  <Parent isRef="64" />
                  <ParentJoin>3</ParentJoin>
                  <Restrictions>AllowMove, AllowSelect, AllowChange</Restrictions>
                  <Text>收费凭证</Text>
                  <TextBrush>Black</TextBrush>
                  <Type>Expression</Type>
                  <VertAlignment>Center</VertAlignment>
                </Table2_Cell4>
                <Table2_Cell5 Ref="69" type="Stimulsoft.Report.Components.Table.StiTableCell" isKey="true">
                  <Border>None;[105:105:105];1;Solid;False;4;[105:105:105]</Border>
                  <Brush>Transparent</Brush>
                  <ClientRectangle>0,0.55,1.32,0.55</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Font>Arial,9</Font>
                  <HorAlignment>Center</HorAlignment>
                  <ID>5</ID>
                  <JoinCells isList="true" count="0" />
                  <Margins>0,0,0,0</Margins>
                  <Name>Table2_Cell5</Name>
                  <Page isRef="2" />
                  <Parent isRef="64" />
                  <Restrictions>AllowMove, AllowSelect, AllowChange</Restrictions>
                  <Text>姓名</Text>
                  <TextBrush>Black</TextBrush>
                  <Type>Expression</Type>
                  <VertAlignment>Center</VertAlignment>
                </Table2_Cell5>
                <Table2_Cell6 Ref="70" type="Stimulsoft.Report.Components.Table.StiTableCell" isKey="true">
                  <Border>None;[105:105:105];1;Solid;False;4;[105:105:105]</Border>
                  <Brush>Transparent</Brush>
                  <ClientRectangle>1.32,0.55,1.41,0.55</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Font>Arial,9</Font>
                  <HorAlignment>Center</HorAlignment>
                  <ID>6</ID>
                  <JoinCells isList="true" count="0" />
                  <Margins>0,0,0,0</Margins>
                  <Name>Table2_Cell6</Name>
                  <Page isRef="2" />
                  <Parent isRef="64" />
                  <Restrictions>AllowMove, AllowSelect, AllowChange</Restrictions>
                  <Text>{姓名1}</Text>
                  <TextBrush>Black</TextBrush>
                  <Type>Expression</Type>
                  <VertAlignment>Center</VertAlignment>
                </Table2_Cell6>
                <Table2_Cell7 Ref="71" type="Stimulsoft.Report.Components.Table.StiTableCell" isKey="true">
                  <Border>None;[105:105:105];1;Solid;False;4;[105:105:105]</Border>
                  <Brush>Transparent</Brush>
                  <ClientRectangle>2.73,0.55,1.31,0.55</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Font>Arial,9</Font>
                  <HorAlignment>Center</HorAlignment>
                  <ID>7</ID>
                  <JoinCells isList="true" count="0" />
                  <Margins>0,0,0,0</Margins>
                  <Name>Table2_Cell7</Name>
                  <Page isRef="2" />
                  <Parent isRef="64" />
                  <Restrictions>AllowMove, AllowSelect, AllowChange</Restrictions>
                  <Text>姓名</Text>
                  <TextBrush>Black</TextBrush>
                  <Type>Expression</Type>
                  <VertAlignment>Center</VertAlignment>
                </Table2_Cell7>
                <Table2_Cell8 Ref="72" type="Stimulsoft.Report.Components.Table.StiTableCell" isKey="true">
                  <Border>None;[105:105:105];1;Solid;False;4;[105:105:105]</Border>
                  <Brush>Transparent</Brush>
                  <ClientRectangle>4.04,0.55,1.91,0.55</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Font>Arial,9</Font>
                  <HorAlignment>Center</HorAlignment>
                  <ID>8</ID>
                  <JoinCells isList="true" count="0" />
                  <Margins>0,0,0,0</Margins>
                  <Name>Table2_Cell8</Name>
                  <Page isRef="2" />
                  <Parent isRef="64" />
                  <Restrictions>AllowMove, AllowSelect, AllowChange</Restrictions>
                  <Text>{姓名2}</Text>
                  <TextBrush>Black</TextBrush>
                  <Type>Expression</Type>
                  <VertAlignment>Center</VertAlignment>
                </Table2_Cell8>
                <Table2_Cell9 Ref="73" type="Stimulsoft.Report.Components.Table.StiTableCell" isKey="true">
                  <Border>None;[105:105:105];1;Solid;False;4;[105:105:105]</Border>
                  <Brush>Transparent</Brush>
                  <ClientRectangle>0,1.11,1.32,0.55</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Font>Arial,9</Font>
                  <HorAlignment>Center</HorAlignment>
                  <ID>10</ID>
                  <JoinCells isList="true" count="0" />
                  <Margins>0,0,0,0</Margins>
                  <Name>Table2_Cell9</Name>
                  <Page isRef="2" />
                  <Parent isRef="64" />
                  <Restrictions>AllowMove, AllowSelect, AllowChange</Restrictions>
                  <Text>项目</Text>
                  <TextBrush>Black</TextBrush>
                  <Type>Expression</Type>
                  <VertAlignment>Center</VertAlignment>
                </Table2_Cell9>
                <Table2_Cell10 Ref="74" type="Stimulsoft.Report.Components.Table.StiTableCell" isKey="true">
                  <Border>None;[105:105:105];1;Solid;False;4;[105:105:105]</Border>
                  <Brush>Transparent</Brush>
                  <ClientRectangle>1.32,1.11,1.41,0.55</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Font>Arial,9</Font>
                  <HorAlignment>Center</HorAlignment>
                  <ID>11</ID>
                  <JoinCells isList="true" count="0" />
                  <Margins>0,0,0,0</Margins>
                  <Name>Table2_Cell10</Name>
                  <Page isRef="2" />
                  <Parent isRef="64" />
                  <Restrictions>AllowMove, AllowSelect, AllowChange</Restrictions>
                  <Text>{项目1}</Text>
                  <TextBrush>Black</TextBrush>
                  <Type>Expression</Type>
                  <VertAlignment>Center</VertAlignment>
                </Table2_Cell10>
                <Table2_Cell11 Ref="75" type="Stimulsoft.Report.Components.Table.StiTableCell" isKey="true">
                  <Border>None;[105:105:105];1;Solid;False;4;[105:105:105]</Border>
                  <Brush>Transparent</Brush>
                  <ClientRectangle>2.73,1.11,1.31,0.55</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Font>Arial,9</Font>
                  <HorAlignment>Center</HorAlignment>
                  <ID>12</ID>
                  <JoinCells isList="true" count="0" />
                  <Margins>0,0,0,0</Margins>
                  <Name>Table2_Cell11</Name>
                  <Page isRef="2" />
                  <Parent isRef="64" />
                  <Restrictions>AllowMove, AllowSelect, AllowChange</Restrictions>
                  <Text>项目</Text>
                  <TextBrush>Black</TextBrush>
                  <Type>Expression</Type>
                  <VertAlignment>Center</VertAlignment>
                </Table2_Cell11>
                <Table2_Cell12 Ref="76" type="Stimulsoft.Report.Components.Table.StiTableCell" isKey="true">
                  <Border>None;[105:105:105];1;Solid;False;4;[105:105:105]</Border>
                  <Brush>Transparent</Brush>
                  <ClientRectangle>4.04,1.11,1.91,0.55</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Font>Arial,9</Font>
                  <HorAlignment>Center</HorAlignment>
                  <ID>13</ID>
                  <JoinCells isList="true" count="0" />
                  <Margins>0,0,0,0</Margins>
                  <Name>Table2_Cell12</Name>
                  <Page isRef="2" />
                  <Parent isRef="64" />
                  <Restrictions>AllowMove, AllowSelect, AllowChange</Restrictions>
                  <Text>{项目2}</Text>
                  <TextBrush>Black</TextBrush>
                  <Type>Expression</Type>
                  <VertAlignment>Center</VertAlignment>
                </Table2_Cell12>
                <Table2_Cell13 Ref="77" type="Stimulsoft.Report.Components.Table.StiTableCell" isKey="true">
                  <Border>None;[105:105:105];1;Solid;False;4;[105:105:105]</Border>
                  <Brush>Transparent</Brush>
                  <ClientRectangle>0,1.66,1.32,0.55</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Font>Arial,9</Font>
                  <HorAlignment>Center</HorAlignment>
                  <ID>15</ID>
                  <JoinCells isList="true" count="0" />
                  <Margins>0,0,0,0</Margins>
                  <Name>Table2_Cell13</Name>
                  <Page isRef="2" />
                  <Parent isRef="64" />
                  <Restrictions>AllowMove, AllowSelect, AllowChange</Restrictions>
                  <Text>金额</Text>
                  <TextBrush>Black</TextBrush>
                  <Type>Expression</Type>
                  <VertAlignment>Center</VertAlignment>
                </Table2_Cell13>
                <Table2_Cell14 Ref="78" type="Stimulsoft.Report.Components.Table.StiTableCell" isKey="true">
                  <Border>None;[105:105:105];1;Solid;False;4;[105:105:105]</Border>
                  <Brush>Transparent</Brush>
                  <ClientRectangle>1.32,1.66,1.41,0.55</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Font>Arial,9</Font>
                  <HorAlignment>Center</HorAlignment>
                  <ID>16</ID>
                  <JoinCells isList="true" count="0" />
                  <Margins>0,0,0,0</Margins>
                  <Name>Table2_Cell14</Name>
                  <Page isRef="2" />
                  <Parent isRef="64" />
                  <Restrictions>AllowMove, AllowSelect, AllowChange</Restrictions>
                  <Text>{金额1}</Text>
                  <TextBrush>Black</TextBrush>
                  <Type>Expression</Type>
                  <VertAlignment>Center</VertAlignment>
                </Table2_Cell14>
                <Table2_Cell15 Ref="79" type="Stimulsoft.Report.Components.Table.StiTableCell" isKey="true">
                  <Border>None;[105:105:105];1;Solid;False;4;[105:105:105]</Border>
                  <Brush>Transparent</Brush>
                  <ClientRectangle>2.73,1.66,1.31,0.55</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Font>Arial,9</Font>
                  <HorAlignment>Center</HorAlignment>
                  <ID>17</ID>
                  <JoinCells isList="true" count="0" />
                  <Margins>0,0,0,0</Margins>
                  <Name>Table2_Cell15</Name>
                  <Page isRef="2" />
                  <Parent isRef="64" />
                  <Restrictions>AllowMove, AllowSelect, AllowChange</Restrictions>
                  <Text>金额</Text>
                  <TextBrush>Black</TextBrush>
                  <Type>Expression</Type>
                  <VertAlignment>Center</VertAlignment>
                </Table2_Cell15>
                <Table2_Cell16 Ref="80" type="Stimulsoft.Report.Components.Table.StiTableCell" isKey="true">
                  <Border>None;[105:105:105];1;Solid;False;4;[105:105:105]</Border>
                  <Brush>Transparent</Brush>
                  <ClientRectangle>4.04,1.66,1.91,0.55</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Font>Arial,9</Font>
                  <HorAlignment>Center</HorAlignment>
                  <ID>18</ID>
                  <JoinCells isList="true" count="0" />
                  <Margins>0,0,0,0</Margins>
                  <Name>Table2_Cell16</Name>
                  <Page isRef="2" />
                  <Parent isRef="64" />
                  <Restrictions>AllowMove, AllowSelect, AllowChange</Restrictions>
                  <Text>{金额2}</Text>
                  <TextBrush>Black</TextBrush>
                  <Type>Expression</Type>
                  <VertAlignment>Center</VertAlignment>
                </Table2_Cell16>
                <Table2_Cell17 Ref="81" type="Stimulsoft.Report.Components.Table.StiTableCell" isKey="true">
                  <Border>None;[105:105:105];1;Solid;False;4;[105:105:105]</Border>
                  <Brush>Transparent</Brush>
                  <ClientRectangle>0,2.21,1.32,0.55</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Font>Arial,9</Font>
                  <HorAlignment>Center</HorAlignment>
                  <ID>20</ID>
                  <JoinCells isList="true" count="0" />
                  <Margins>0,0,0,0</Margins>
                  <Name>Table2_Cell17</Name>
                  <Page isRef="2" />
                  <Parent isRef="64" />
                  <Restrictions>AllowMove, AllowSelect, AllowChange</Restrictions>
                  <Text>日期</Text>
                  <TextBrush>Black</TextBrush>
                  <Type>Expression</Type>
                  <VertAlignment>Center</VertAlignment>
                </Table2_Cell17>
                <Table2_Cell18 Ref="82" type="Stimulsoft.Report.Components.Table.StiTableCell" isKey="true">
                  <Border>None;[105:105:105];1;Solid;False;4;[105:105:105]</Border>
                  <Brush>Transparent</Brush>
                  <ClientRectangle>1.32,2.21,1.41,0.55</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Font>Arial,7</Font>
                  <ID>21</ID>
                  <JoinCells isList="true" count="0" />
                  <Margins>0,0,0,0</Margins>
                  <Name>Table2_Cell18</Name>
                  <Page isRef="2" />
                  <Parent isRef="64" />
                  <Restrictions>AllowMove, AllowSelect, AllowChange</Restrictions>
                  <Text>{日期1}</Text>
                  <TextBrush>Black</TextBrush>
                  <Type>Expression</Type>
                  <VertAlignment>Center</VertAlignment>
                </Table2_Cell18>
                <Table2_Cell19 Ref="83" type="Stimulsoft.Report.Components.Table.StiTableCell" isKey="true">
                  <Border>None;[105:105:105];1;Solid;False;4;[105:105:105]</Border>
                  <Brush>Transparent</Brush>
                  <ClientRectangle>2.73,2.21,1.31,0.55</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Font>Arial,9</Font>
                  <HorAlignment>Center</HorAlignment>
                  <ID>22</ID>
                  <JoinCells isList="true" count="0" />
                  <Margins>0,0,0,0</Margins>
                  <Name>Table2_Cell19</Name>
                  <Page isRef="2" />
                  <Parent isRef="64" />
                  <Restrictions>AllowMove, AllowSelect, AllowChange</Restrictions>
                  <Text>日期</Text>
                  <TextBrush>Black</TextBrush>
                  <Type>Expression</Type>
                  <VertAlignment>Center</VertAlignment>
                </Table2_Cell19>
                <Table2_Cell20 Ref="84" type="Stimulsoft.Report.Components.Table.StiTableCell" isKey="true">
                  <Border>None;[105:105:105];1;Solid;False;4;[105:105:105]</Border>
                  <Brush>Transparent</Brush>
                  <ClientRectangle>4.04,2.21,1.91,0.55</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Font>Arial,7</Font>
                  <ID>23</ID>
                  <JoinCells isList="true" count="0" />
                  <Margins>0,0,0,0</Margins>
                  <Name>Table2_Cell20</Name>
                  <Page isRef="2" />
                  <Parent isRef="64" />
                  <Restrictions>AllowMove, AllowSelect, AllowChange</Restrictions>
                  <Text>{日期2}</Text>
                  <TextBrush>Black</TextBrush>
                  <Type>Expression</Type>
                  <VertAlignment>Center</VertAlignment>
                </Table2_Cell20>
                <Table2_Cell21 Ref="85" type="Stimulsoft.Report.Components.Table.StiTableCell" isKey="true">
                  <Border>None;[105:105:105];1;Solid;False;4;[105:105:105]</Border>
                  <Brush>Transparent</Brush>
                  <ClientRectangle>0,2.76,1.32,0.55</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Font>Arial,9</Font>
                  <HorAlignment>Center</HorAlignment>
                  <ID>25</ID>
                  <JoinCells isList="true" count="0" />
                  <Margins>0,0,0,0</Margins>
                  <Name>Table2_Cell21</Name>
                  <Page isRef="2" />
                  <Parent isRef="64" />
                  <Restrictions>AllowMove, AllowSelect, AllowChange</Restrictions>
                  <Text>收款员</Text>
                  <TextBrush>Black</TextBrush>
                  <Type>Expression</Type>
                  <VertAlignment>Center</VertAlignment>
                </Table2_Cell21>
                <Table2_Cell22 Ref="86" type="Stimulsoft.Report.Components.Table.StiTableCell" isKey="true">
                  <Border>None;[105:105:105];1;Solid;False;4;[105:105:105]</Border>
                  <Brush>Transparent</Brush>
                  <ClientRectangle>1.32,2.76,1.41,0.55</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Font>Arial,9</Font>
                  <HorAlignment>Center</HorAlignment>
                  <ID>26</ID>
                  <JoinCells isList="true" count="0" />
                  <Margins>0,0,0,0</Margins>
                  <Name>Table2_Cell22</Name>
                  <Page isRef="2" />
                  <Parent isRef="64" />
                  <Restrictions>AllowMove, AllowSelect, AllowChange</Restrictions>
                  <Text>{收款员1}</Text>
                  <TextBrush>Black</TextBrush>
                  <Type>Expression</Type>
                  <VertAlignment>Center</VertAlignment>
                </Table2_Cell22>
                <Table2_Cell23 Ref="87" type="Stimulsoft.Report.Components.Table.StiTableCell" isKey="true">
                  <Border>None;[105:105:105];1;Solid;False;4;[105:105:105]</Border>
                  <Brush>Transparent</Brush>
                  <ClientRectangle>2.73,2.76,1.31,0.55</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Font>Arial,9</Font>
                  <HorAlignment>Center</HorAlignment>
                  <ID>27</ID>
                  <JoinCells isList="true" count="0" />
                  <Margins>0,0,0,0</Margins>
                  <Name>Table2_Cell23</Name>
                  <Page isRef="2" />
                  <Parent isRef="64" />
                  <Restrictions>AllowMove, AllowSelect, AllowChange</Restrictions>
                  <Text>收款员</Text>
                  <TextBrush>Black</TextBrush>
                  <Type>Expression</Type>
                  <VertAlignment>Center</VertAlignment>
                </Table2_Cell23>
                <Table2_Cell24 Ref="88" type="Stimulsoft.Report.Components.Table.StiTableCell" isKey="true">
                  <Border>None;[105:105:105];1;Solid;False;4;[105:105:105]</Border>
                  <Brush>Transparent</Brush>
                  <ClientRectangle>4.04,2.76,1.91,0.55</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Font>Arial,9</Font>
                  <HorAlignment>Center</HorAlignment>
                  <ID>28</ID>
                  <JoinCells isList="true" count="0" />
                  <Margins>0,0,0,0</Margins>
                  <Name>Table2_Cell24</Name>
                  <Page isRef="2" />
                  <Parent isRef="64" />
                  <Restrictions>AllowMove, AllowSelect, AllowChange</Restrictions>
                  <Text>{收款员2}</Text>
                  <TextBrush>Black</TextBrush>
                  <Type>Expression</Type>
                  <VertAlignment>Center</VertAlignment>
                </Table2_Cell24>
                <Table2_Cell25 Ref="89" type="Stimulsoft.Report.Components.Table.StiTableCell" isKey="true">
                  <Border>None;[105:105:105];1;Solid;False;4;[105:105:105]</Border>
                  <Brush>Transparent</Brush>
                  <ClientRectangle>0,3.32,1.32,0.55</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Font>Arial,9</Font>
                  <HorAlignment>Center</HorAlignment>
                  <ID>29</ID>
                  <JoinCells isList="true" count="0" />
                  <Margins>0,0,0,0</Margins>
                  <Name>Table2_Cell25</Name>
                  <Page isRef="2" />
                  <Parent isRef="64" />
                  <Restrictions>AllowMove, AllowSelect, AllowChange</Restrictions>
                  <Text>微机号</Text>
                  <TextBrush>Black</TextBrush>
                  <Type>Expression</Type>
                  <VertAlignment>Center</VertAlignment>
                </Table2_Cell25>
                <Table2_Cell26 Ref="90" type="Stimulsoft.Report.Components.Table.StiTableCell" isKey="true">
                  <Border>None;[105:105:105];1;Solid;False;4;[105:105:105]</Border>
                  <Brush>Transparent</Brush>
                  <ClientRectangle>1.32,3.32,1.41,0.55</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Font>Arial,9</Font>
                  <HorAlignment>Center</HorAlignment>
                  <ID>30</ID>
                  <JoinCells isList="true" count="0" />
                  <Margins>0,0,0,0</Margins>
                  <Name>Table2_Cell26</Name>
                  <Page isRef="2" />
                  <Parent isRef="64" />
                  <Restrictions>AllowMove, AllowSelect, AllowChange</Restrictions>
                  <Text>{微机号1}</Text>
                  <TextBrush>Black</TextBrush>
                  <Type>Expression</Type>
                  <VertAlignment>Center</VertAlignment>
                </Table2_Cell26>
                <Table2_Cell27 Ref="91" type="Stimulsoft.Report.Components.Table.StiTableCell" isKey="true">
                  <Border>None;[105:105:105];1;Solid;False;4;[105:105:105]</Border>
                  <Brush>Transparent</Brush>
                  <ClientRectangle>2.73,3.32,1.31,0.55</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Font>Arial,9</Font>
                  <HorAlignment>Center</HorAlignment>
                  <ID>31</ID>
                  <JoinCells isList="true" count="0" />
                  <Margins>0,0,0,0</Margins>
                  <Name>Table2_Cell27</Name>
                  <Page isRef="2" />
                  <Parent isRef="64" />
                  <Restrictions>AllowMove, AllowSelect, AllowChange</Restrictions>
                  <Text>微机号</Text>
                  <TextBrush>Black</TextBrush>
                  <Type>Expression</Type>
                  <VertAlignment>Center</VertAlignment>
                </Table2_Cell27>
                <Table2_Cell28 Ref="92" type="Stimulsoft.Report.Components.Table.StiTableCell" isKey="true">
                  <Border>None;[105:105:105];1;Solid;False;4;[105:105:105]</Border>
                  <Brush>Transparent</Brush>
                  <ClientRectangle>4.04,3.32,1.91,0.55</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Font>Arial,9</Font>
                  <HorAlignment>Center</HorAlignment>
                  <ID>32</ID>
                  <JoinCells isList="true" count="0" />
                  <Margins>0,0,0,0</Margins>
                  <Name>Table2_Cell28</Name>
                  <Page isRef="2" />
                  <Parent isRef="64" />
                  <Restrictions>AllowMove, AllowSelect, AllowChange</Restrictions>
                  <Text>{微机号2}</Text>
                  <TextBrush>Black</TextBrush>
                  <Type>Expression</Type>
                  <VertAlignment>Center</VertAlignment>
                </Table2_Cell28>
                <Table2_Cell29 Ref="93" type="Stimulsoft.Report.Components.Table.StiTableCell" isKey="true">
                  <Border>None;[105:105:105];1;Solid;False;4;[105:105:105]</Border>
                  <Brush>Transparent</Brush>
                  <ClientRectangle>0,3.87,1.32,0.55</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Enabled>False</Enabled>
                  <Font>Arial,9</Font>
                  <HorAlignment>Center</HorAlignment>
                  <ID>33</ID>
                  <JoinCells isList="true" count="0" />
                  <Margins>0,0,0,0</Margins>
                  <Name>Table2_Cell29</Name>
                  <Page isRef="2" />
                  <Parent isRef="64" />
                  <ParentJoin>34</ParentJoin>
                  <Restrictions>AllowMove, AllowSelect, AllowChange</Restrictions>
                  <TextBrush>Black</TextBrush>
                  <VertAlignment>Center</VertAlignment>
                </Table2_Cell29>
                <Table2_Cell30 Ref="94" type="Stimulsoft.Report.Components.Table.StiTableCell" isKey="true">
                  <Border>None;[105:105:105];1;Solid;False;4;[105:105:105]</Border>
                  <Brush>Transparent</Brush>
                  <ClientRectangle>1.2,3.87,1.53,0.55</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Font>Arial,9</Font>
                  <ID>34</ID>
                  <Join>True</Join>
                  <JoinCells isList="true" count="2">
                    <value>33</value>
                    <value>34</value>
                  </JoinCells>
                  <JoinHeight>1</JoinHeight>
                  <JoinWidth>2</JoinWidth>
                  <Margins>0,0,0,0</Margins>
                  <Name>Table2_Cell30</Name>
                  <Page isRef="2" />
                  <Parent isRef="64" />
                  <ParentJoin>34</ParentJoin>
                  <Restrictions>AllowMove, AllowSelect, AllowChange</Restrictions>
                  <Text>收费凭证</Text>
                  <TextBrush>Black</TextBrush>
                  <Type>Expression</Type>
                  <VertAlignment>Center</VertAlignment>
                </Table2_Cell30>
                <Table2_Cell31 Ref="95" type="Stimulsoft.Report.Components.Table.StiTableCell" isKey="true">
                  <Border>None;[105:105:105];1;Solid;False;4;[105:105:105]</Border>
                  <Brush>Transparent</Brush>
                  <ClientRectangle>2.73,3.87,1.31,0.55</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Enabled>False</Enabled>
                  <Font>Arial,9</Font>
                  <HorAlignment>Center</HorAlignment>
                  <ID>35</ID>
                  <JoinCells isList="true" count="0" />
                  <Margins>0,0,0,0</Margins>
                  <Name>Table2_Cell31</Name>
                  <Page isRef="2" />
                  <Parent isRef="64" />
                  <ParentJoin>36</ParentJoin>
                  <Restrictions>AllowMove, AllowSelect, AllowChange</Restrictions>
                  <TextBrush>Black</TextBrush>
                  <VertAlignment>Center</VertAlignment>
                </Table2_Cell31>
                <Table2_Cell32 Ref="96" type="Stimulsoft.Report.Components.Table.StiTableCell" isKey="true">
                  <Border>None;[105:105:105];1;Solid;False;4;[105:105:105]</Border>
                  <Brush>Transparent</Brush>
                  <ClientRectangle>3.93,3.87,2.02,0.55</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Font>Arial,9</Font>
                  <ID>36</ID>
                  <Join>True</Join>
                  <JoinCells isList="true" count="2">
                    <value>35</value>
                    <value>36</value>
                  </JoinCells>
                  <JoinHeight>1</JoinHeight>
                  <JoinWidth>2</JoinWidth>
                  <Margins>0,0,0,0</Margins>
                  <Name>Table2_Cell32</Name>
                  <Page isRef="2" />
                  <Parent isRef="64" />
                  <ParentJoin>36</ParentJoin>
                  <Restrictions>AllowMove, AllowSelect, AllowChange</Restrictions>
                  <Text>收费凭证</Text>
                  <TextBrush>Black</TextBrush>
                  <Type>Expression</Type>
                  <VertAlignment>Center</VertAlignment>
                </Table2_Cell32>
                <Table2_Cell33 Ref="97" type="Stimulsoft.Report.Components.Table.StiTableCell" isKey="true">
                  <Border>None;[105:105:105];1;Solid;False;4;[105:105:105]</Border>
                  <Brush>Transparent</Brush>
                  <ClientRectangle>0,4.42,1.32,0.55</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Font>Arial,9</Font>
                  <HorAlignment>Center</HorAlignment>
                  <ID>37</ID>
                  <JoinCells isList="true" count="0" />
                  <Margins>0,0,0,0</Margins>
                  <Name>Table2_Cell33</Name>
                  <Page isRef="2" />
                  <Parent isRef="64" />
                  <Restrictions>AllowMove, AllowSelect, AllowChange</Restrictions>
                  <Text>姓名</Text>
                  <TextBrush>Black</TextBrush>
                  <Type>Expression</Type>
                  <VertAlignment>Center</VertAlignment>
                </Table2_Cell33>
                <Table2_Cell34 Ref="98" type="Stimulsoft.Report.Components.Table.StiTableCell" isKey="true">
                  <Border>None;[105:105:105];1;Solid;False;4;[105:105:105]</Border>
                  <Brush>Transparent</Brush>
                  <ClientRectangle>1.32,4.42,1.41,0.55</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Font>Arial,9</Font>
                  <HorAlignment>Center</HorAlignment>
                  <ID>38</ID>
                  <JoinCells isList="true" count="0" />
                  <Margins>0,0,0,0</Margins>
                  <Name>Table2_Cell34</Name>
                  <Page isRef="2" />
                  <Parent isRef="64" />
                  <Restrictions>AllowMove, AllowSelect, AllowChange</Restrictions>
                  <Text>{姓名3}</Text>
                  <TextBrush>Black</TextBrush>
                  <Type>Expression</Type>
                  <VertAlignment>Center</VertAlignment>
                </Table2_Cell34>
                <Table2_Cell35 Ref="99" type="Stimulsoft.Report.Components.Table.StiTableCell" isKey="true">
                  <Border>None;[105:105:105];1;Solid;False;4;[105:105:105]</Border>
                  <Brush>Transparent</Brush>
                  <ClientRectangle>2.73,4.42,1.31,0.55</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Font>Arial,9</Font>
                  <HorAlignment>Center</HorAlignment>
                  <ID>39</ID>
                  <JoinCells isList="true" count="0" />
                  <Margins>0,0,0,0</Margins>
                  <Name>Table2_Cell35</Name>
                  <Page isRef="2" />
                  <Parent isRef="64" />
                  <Restrictions>AllowMove, AllowSelect, AllowChange</Restrictions>
                  <Text>姓名</Text>
                  <TextBrush>Black</TextBrush>
                  <Type>Expression</Type>
                  <VertAlignment>Center</VertAlignment>
                </Table2_Cell35>
                <Table2_Cell36 Ref="100" type="Stimulsoft.Report.Components.Table.StiTableCell" isKey="true">
                  <Border>None;[105:105:105];1;Solid;False;4;[105:105:105]</Border>
                  <Brush>Transparent</Brush>
                  <ClientRectangle>4.04,4.42,1.91,0.55</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Font>Arial,9</Font>
                  <HorAlignment>Center</HorAlignment>
                  <ID>40</ID>
                  <JoinCells isList="true" count="0" />
                  <Margins>0,0,0,0</Margins>
                  <Name>Table2_Cell36</Name>
                  <Page isRef="2" />
                  <Parent isRef="64" />
                  <Restrictions>AllowMove, AllowSelect, AllowChange</Restrictions>
                  <Text>{姓名4}</Text>
                  <TextBrush>Black</TextBrush>
                  <Type>Expression</Type>
                  <VertAlignment>Center</VertAlignment>
                </Table2_Cell36>
                <Table2_Cell37 Ref="101" type="Stimulsoft.Report.Components.Table.StiTableCell" isKey="true">
                  <Border>None;[105:105:105];1;Solid;False;4;[105:105:105]</Border>
                  <Brush>Transparent</Brush>
                  <ClientRectangle>0,4.97,1.32,0.55</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Font>Arial,9</Font>
                  <HorAlignment>Center</HorAlignment>
                  <ID>41</ID>
                  <JoinCells isList="true" count="0" />
                  <Margins>0,0,0,0</Margins>
                  <Name>Table2_Cell37</Name>
                  <Page isRef="2" />
                  <Parent isRef="64" />
                  <Restrictions>AllowMove, AllowSelect, AllowChange</Restrictions>
                  <Text>项目</Text>
                  <TextBrush>Black</TextBrush>
                  <Type>Expression</Type>
                  <VertAlignment>Center</VertAlignment>
                </Table2_Cell37>
                <Table2_Cell38 Ref="102" type="Stimulsoft.Report.Components.Table.StiTableCell" isKey="true">
                  <Border>None;[105:105:105];1;Solid;False;4;[105:105:105]</Border>
                  <Brush>Transparent</Brush>
                  <ClientRectangle>1.32,4.97,1.41,0.55</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Font>Arial,9</Font>
                  <HorAlignment>Center</HorAlignment>
                  <ID>42</ID>
                  <JoinCells isList="true" count="0" />
                  <Margins>0,0,0,0</Margins>
                  <Name>Table2_Cell38</Name>
                  <Page isRef="2" />
                  <Parent isRef="64" />
                  <Restrictions>AllowMove, AllowSelect, AllowChange</Restrictions>
                  <Text>{项目3}</Text>
                  <TextBrush>Black</TextBrush>
                  <Type>Expression</Type>
                  <VertAlignment>Center</VertAlignment>
                </Table2_Cell38>
                <Table2_Cell39 Ref="103" type="Stimulsoft.Report.Components.Table.StiTableCell" isKey="true">
                  <Border>None;[105:105:105];1;Solid;False;4;[105:105:105]</Border>
                  <Brush>Transparent</Brush>
                  <ClientRectangle>2.73,4.97,1.31,0.55</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Font>Arial,9</Font>
                  <HorAlignment>Center</HorAlignment>
                  <ID>43</ID>
                  <JoinCells isList="true" count="0" />
                  <Margins>0,0,0,0</Margins>
                  <Name>Table2_Cell39</Name>
                  <Page isRef="2" />
                  <Parent isRef="64" />
                  <Restrictions>AllowMove, AllowSelect, AllowChange</Restrictions>
                  <Text>项目</Text>
                  <TextBrush>Black</TextBrush>
                  <Type>Expression</Type>
                  <VertAlignment>Center</VertAlignment>
                </Table2_Cell39>
                <Table2_Cell40 Ref="104" type="Stimulsoft.Report.Components.Table.StiTableCell" isKey="true">
                  <Border>None;[105:105:105];1;Solid;False;4;[105:105:105]</Border>
                  <Brush>Transparent</Brush>
                  <ClientRectangle>4.04,4.97,1.91,0.55</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Font>Arial,9</Font>
                  <HorAlignment>Center</HorAlignment>
                  <ID>44</ID>
                  <JoinCells isList="true" count="0" />
                  <Margins>0,0,0,0</Margins>
                  <Name>Table2_Cell40</Name>
                  <Page isRef="2" />
                  <Parent isRef="64" />
                  <Restrictions>AllowMove, AllowSelect, AllowChange</Restrictions>
                  <Text>{项目4}</Text>
                  <TextBrush>Black</TextBrush>
                  <Type>Expression</Type>
                  <VertAlignment>Center</VertAlignment>
                </Table2_Cell40>
                <Table2_Cell41 Ref="105" type="Stimulsoft.Report.Components.Table.StiTableCell" isKey="true">
                  <Border>None;[105:105:105];1;Solid;False;4;[105:105:105]</Border>
                  <Brush>Transparent</Brush>
                  <ClientRectangle>0,5.53,1.32,0.55</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Font>Arial,9</Font>
                  <HorAlignment>Center</HorAlignment>
                  <ID>45</ID>
                  <JoinCells isList="true" count="0" />
                  <Margins>0,0,0,0</Margins>
                  <Name>Table2_Cell41</Name>
                  <Page isRef="2" />
                  <Parent isRef="64" />
                  <Restrictions>AllowMove, AllowSelect, AllowChange</Restrictions>
                  <Text>金额</Text>
                  <TextBrush>Black</TextBrush>
                  <Type>Expression</Type>
                  <VertAlignment>Center</VertAlignment>
                </Table2_Cell41>
                <Table2_Cell42 Ref="106" type="Stimulsoft.Report.Components.Table.StiTableCell" isKey="true">
                  <Border>None;[105:105:105];1;Solid;False;4;[105:105:105]</Border>
                  <Brush>Transparent</Brush>
                  <ClientRectangle>1.32,5.53,1.41,0.55</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Font>Arial,9</Font>
                  <HorAlignment>Center</HorAlignment>
                  <ID>46</ID>
                  <JoinCells isList="true" count="0" />
                  <Margins>0,0,0,0</Margins>
                  <Name>Table2_Cell42</Name>
                  <Page isRef="2" />
                  <Parent isRef="64" />
                  <Restrictions>AllowMove, AllowSelect, AllowChange</Restrictions>
                  <Text>{金额3}</Text>
                  <TextBrush>Black</TextBrush>
                  <Type>Expression</Type>
                  <VertAlignment>Center</VertAlignment>
                </Table2_Cell42>
                <Table2_Cell43 Ref="107" type="Stimulsoft.Report.Components.Table.StiTableCell" isKey="true">
                  <Border>None;[105:105:105];1;Solid;False;4;[105:105:105]</Border>
                  <Brush>Transparent</Brush>
                  <ClientRectangle>2.73,5.53,1.31,0.55</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Font>Arial,9</Font>
                  <HorAlignment>Center</HorAlignment>
                  <ID>47</ID>
                  <JoinCells isList="true" count="0" />
                  <Margins>0,0,0,0</Margins>
                  <Name>Table2_Cell43</Name>
                  <Page isRef="2" />
                  <Parent isRef="64" />
                  <Restrictions>AllowMove, AllowSelect, AllowChange</Restrictions>
                  <Text>金额</Text>
                  <TextBrush>Black</TextBrush>
                  <Type>Expression</Type>
                  <VertAlignment>Center</VertAlignment>
                </Table2_Cell43>
                <Table2_Cell44 Ref="108" type="Stimulsoft.Report.Components.Table.StiTableCell" isKey="true">
                  <Border>None;[105:105:105];1;Solid;False;4;[105:105:105]</Border>
                  <Brush>Transparent</Brush>
                  <ClientRectangle>4.04,5.53,1.91,0.55</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Font>Arial,9</Font>
                  <HorAlignment>Center</HorAlignment>
                  <ID>48</ID>
                  <JoinCells isList="true" count="0" />
                  <Margins>0,0,0,0</Margins>
                  <Name>Table2_Cell44</Name>
                  <Page isRef="2" />
                  <Parent isRef="64" />
                  <Restrictions>AllowMove, AllowSelect, AllowChange</Restrictions>
                  <Text>{金额4}</Text>
                  <TextBrush>Black</TextBrush>
                  <Type>Expression</Type>
                  <VertAlignment>Center</VertAlignment>
                </Table2_Cell44>
                <Table2_Cell45 Ref="109" type="Stimulsoft.Report.Components.Table.StiTableCell" isKey="true">
                  <Border>None;[105:105:105];1;Solid;False;4;[105:105:105]</Border>
                  <Brush>Transparent</Brush>
                  <ClientRectangle>0,6.08,1.32,0.55</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Font>Arial,9</Font>
                  <HorAlignment>Center</HorAlignment>
                  <ID>49</ID>
                  <JoinCells isList="true" count="0" />
                  <Margins>0,0,0,0</Margins>
                  <Name>Table2_Cell45</Name>
                  <Page isRef="2" />
                  <Parent isRef="64" />
                  <Restrictions>AllowMove, AllowSelect, AllowChange</Restrictions>
                  <Text>日期</Text>
                  <TextBrush>Black</TextBrush>
                  <Type>Expression</Type>
                  <VertAlignment>Center</VertAlignment>
                </Table2_Cell45>
                <Table2_Cell46 Ref="110" type="Stimulsoft.Report.Components.Table.StiTableCell" isKey="true">
                  <Border>None;[105:105:105];1;Solid;False;4;[105:105:105]</Border>
                  <Brush>Transparent</Brush>
                  <ClientRectangle>1.32,6.08,1.41,0.55</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Font>Arial,7</Font>
                  <ID>50</ID>
                  <JoinCells isList="true" count="0" />
                  <Margins>0,0,0,0</Margins>
                  <Name>Table2_Cell46</Name>
                  <Page isRef="2" />
                  <Parent isRef="64" />
                  <Restrictions>AllowMove, AllowSelect, AllowChange</Restrictions>
                  <Text>{日期3}</Text>
                  <TextBrush>Black</TextBrush>
                  <Type>Expression</Type>
                  <VertAlignment>Center</VertAlignment>
                </Table2_Cell46>
                <Table2_Cell47 Ref="111" type="Stimulsoft.Report.Components.Table.StiTableCell" isKey="true">
                  <Border>None;[105:105:105];1;Solid;False;4;[105:105:105]</Border>
                  <Brush>Transparent</Brush>
                  <ClientRectangle>2.73,6.08,1.31,0.55</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Font>Arial,9</Font>
                  <HorAlignment>Center</HorAlignment>
                  <ID>51</ID>
                  <JoinCells isList="true" count="0" />
                  <Margins>0,0,0,0</Margins>
                  <Name>Table2_Cell47</Name>
                  <Page isRef="2" />
                  <Parent isRef="64" />
                  <Restrictions>AllowMove, AllowSelect, AllowChange</Restrictions>
                  <Text>日期</Text>
                  <TextBrush>Black</TextBrush>
                  <Type>Expression</Type>
                  <VertAlignment>Center</VertAlignment>
                </Table2_Cell47>
                <Table2_Cell48 Ref="112" type="Stimulsoft.Report.Components.Table.StiTableCell" isKey="true">
                  <Border>None;[105:105:105];1;Solid;False;4;[105:105:105]</Border>
                  <Brush>Transparent</Brush>
                  <ClientRectangle>4.04,6.08,1.91,0.55</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Font>Arial,7</Font>
                  <ID>52</ID>
                  <JoinCells isList="true" count="0" />
                  <Margins>0,0,0,0</Margins>
                  <Name>Table2_Cell48</Name>
                  <Page isRef="2" />
                  <Parent isRef="64" />
                  <Restrictions>AllowMove, AllowSelect, AllowChange</Restrictions>
                  <Text>{日期4}</Text>
                  <TextBrush>Black</TextBrush>
                  <Type>Expression</Type>
                  <VertAlignment>Center</VertAlignment>
                </Table2_Cell48>
                <Table2_Cell49 Ref="113" type="Stimulsoft.Report.Components.Table.StiTableCell" isKey="true">
                  <Border>None;[105:105:105];1;Solid;False;4;[105:105:105]</Border>
                  <Brush>Transparent</Brush>
                  <ClientRectangle>0,6.63,1.32,0.55</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Font>Arial,9</Font>
                  <HorAlignment>Center</HorAlignment>
                  <ID>53</ID>
                  <JoinCells isList="true" count="0" />
                  <Margins>0,0,0,0</Margins>
                  <Name>Table2_Cell49</Name>
                  <Page isRef="2" />
                  <Parent isRef="64" />
                  <Restrictions>AllowMove, AllowSelect, AllowChange</Restrictions>
                  <Text>收款员</Text>
                  <TextBrush>Black</TextBrush>
                  <Type>Expression</Type>
                  <VertAlignment>Center</VertAlignment>
                </Table2_Cell49>
                <Table2_Cell50 Ref="114" type="Stimulsoft.Report.Components.Table.StiTableCell" isKey="true">
                  <Border>None;[105:105:105];1;Solid;False;4;[105:105:105]</Border>
                  <Brush>Transparent</Brush>
                  <ClientRectangle>1.32,6.63,1.41,0.55</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Font>Arial,9</Font>
                  <HorAlignment>Center</HorAlignment>
                  <ID>54</ID>
                  <JoinCells isList="true" count="0" />
                  <Margins>0,0,0,0</Margins>
                  <Name>Table2_Cell50</Name>
                  <Page isRef="2" />
                  <Parent isRef="64" />
                  <Restrictions>AllowMove, AllowSelect, AllowChange</Restrictions>
                  <Text>{收款员3}</Text>
                  <TextBrush>Black</TextBrush>
                  <Type>Expression</Type>
                  <VertAlignment>Center</VertAlignment>
                </Table2_Cell50>
                <Table2_Cell51 Ref="115" type="Stimulsoft.Report.Components.Table.StiTableCell" isKey="true">
                  <Border>None;[105:105:105];1;Solid;False;4;[105:105:105]</Border>
                  <Brush>Transparent</Brush>
                  <ClientRectangle>2.73,6.63,1.31,0.55</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Font>Arial,9</Font>
                  <HorAlignment>Center</HorAlignment>
                  <ID>55</ID>
                  <JoinCells isList="true" count="0" />
                  <Margins>0,0,0,0</Margins>
                  <Name>Table2_Cell51</Name>
                  <Page isRef="2" />
                  <Parent isRef="64" />
                  <Restrictions>AllowMove, AllowSelect, AllowChange</Restrictions>
                  <Text>收款员</Text>
                  <TextBrush>Black</TextBrush>
                  <Type>Expression</Type>
                  <VertAlignment>Center</VertAlignment>
                </Table2_Cell51>
                <Table2_Cell52 Ref="116" type="Stimulsoft.Report.Components.Table.StiTableCell" isKey="true">
                  <Border>None;[105:105:105];1;Solid;False;4;[105:105:105]</Border>
                  <Brush>Transparent</Brush>
                  <ClientRectangle>4.04,6.63,1.91,0.55</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Font>Arial,9</Font>
                  <HorAlignment>Center</HorAlignment>
                  <ID>56</ID>
                  <JoinCells isList="true" count="0" />
                  <Margins>0,0,0,0</Margins>
                  <Name>Table2_Cell52</Name>
                  <Page isRef="2" />
                  <Parent isRef="64" />
                  <Restrictions>AllowMove, AllowSelect, AllowChange</Restrictions>
                  <Text>{收款员4}</Text>
                  <TextBrush>Black</TextBrush>
                  <Type>Expression</Type>
                  <VertAlignment>Center</VertAlignment>
                </Table2_Cell52>
                <Table2_Cell53 Ref="117" type="Stimulsoft.Report.Components.Table.StiTableCell" isKey="true">
                  <Border>None;[105:105:105];1;Solid;False;4;[105:105:105]</Border>
                  <Brush>Transparent</Brush>
                  <ClientRectangle>0,7.19,1.32,0.55</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Font>Arial,9</Font>
                  <HorAlignment>Center</HorAlignment>
                  <ID>57</ID>
                  <JoinCells isList="true" count="0" />
                  <Margins>0,0,0,0</Margins>
                  <Name>Table2_Cell53</Name>
                  <Page isRef="2" />
                  <Parent isRef="64" />
                  <Restrictions>AllowMove, AllowSelect, AllowChange</Restrictions>
                  <Text>微机号</Text>
                  <TextBrush>Black</TextBrush>
                  <Type>Expression</Type>
                  <VertAlignment>Center</VertAlignment>
                </Table2_Cell53>
                <Table2_Cell54 Ref="118" type="Stimulsoft.Report.Components.Table.StiTableCell" isKey="true">
                  <Border>None;[105:105:105];1;Solid;False;4;[105:105:105]</Border>
                  <Brush>Transparent</Brush>
                  <ClientRectangle>1.32,7.19,1.41,0.55</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Font>Arial,9</Font>
                  <HorAlignment>Center</HorAlignment>
                  <ID>58</ID>
                  <JoinCells isList="true" count="0" />
                  <Margins>0,0,0,0</Margins>
                  <Name>Table2_Cell54</Name>
                  <Page isRef="2" />
                  <Parent isRef="64" />
                  <Restrictions>AllowMove, AllowSelect, AllowChange</Restrictions>
                  <Text>{微机号3}</Text>
                  <TextBrush>Black</TextBrush>
                  <Type>Expression</Type>
                  <VertAlignment>Center</VertAlignment>
                </Table2_Cell54>
                <Table2_Cell55 Ref="119" type="Stimulsoft.Report.Components.Table.StiTableCell" isKey="true">
                  <Border>None;[105:105:105];1;Solid;False;4;[105:105:105]</Border>
                  <Brush>Transparent</Brush>
                  <ClientRectangle>2.73,7.19,1.31,0.55</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Font>Arial,9</Font>
                  <HorAlignment>Center</HorAlignment>
                  <ID>59</ID>
                  <JoinCells isList="true" count="0" />
                  <Margins>0,0,0,0</Margins>
                  <Name>Table2_Cell55</Name>
                  <Page isRef="2" />
                  <Parent isRef="64" />
                  <Restrictions>AllowMove, AllowSelect, AllowChange</Restrictions>
                  <Text>微机号</Text>
                  <TextBrush>Black</TextBrush>
                  <Type>Expression</Type>
                  <VertAlignment>Center</VertAlignment>
                </Table2_Cell55>
                <Table2_Cell56 Ref="120" type="Stimulsoft.Report.Components.Table.StiTableCell" isKey="true">
                  <Border>None;[105:105:105];1;Solid;False;4;[105:105:105]</Border>
                  <Brush>Transparent</Brush>
                  <ClientRectangle>4.04,7.19,1.91,0.55</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Font>Arial,9</Font>
                  <HorAlignment>Center</HorAlignment>
                  <ID>60</ID>
                  <JoinCells isList="true" count="0" />
                  <Margins>0,0,0,0</Margins>
                  <Name>Table2_Cell56</Name>
                  <Page isRef="2" />
                  <Parent isRef="64" />
                  <Restrictions>AllowMove, AllowSelect, AllowChange</Restrictions>
                  <Text>{微机号4}</Text>
                  <TextBrush>Black</TextBrush>
                  <Type>Expression</Type>
                  <VertAlignment>Center</VertAlignment>
                </Table2_Cell56>
              </Components>
              <Conditions isList="true" count="0" />
              <DataRelationName isNull="true" />
              <Filters isList="true" count="0" />
              <MinHeight>2.8000000000000003</MinHeight>
              <Name>Table2</Name>
              <NumberID>61</NumberID>
              <Page isRef="2" />
              <Parent isRef="63" />
              <RowCount>14</RowCount>
              <Sort isList="true" count="0" />
            </Table2>
          </Components>
          <Conditions isList="true" count="0" />
          <Name>Panel2</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
        </Panel2>
      </Components>
      <Conditions isList="true" count="0" />
      <Guid>b0a70bb994724837b3e8b468faf8f262</Guid>
      <LargeHeight>True</LargeHeight>
      <Margins>0,0,0,0</Margins>
      <Name>Page1</Name>
      <PageHeight>9.3</PageHeight>
      <PageWidth>19</PageWidth>
      <Report isRef="0" />
      <Watermark Ref="121" type="Stimulsoft.Report.Components.StiWatermark" isKey="true">
        <Font>Arial,100</Font>
        <TextBrush>[50:0:0:0]</TextBrush>
      </Watermark>
    </Page1>
  </Pages>
  <PrinterSettings Ref="122" type="Stimulsoft.Report.Print.StiPrinterSettings" isKey="true" />
  <ReferencedAssemblies isList="true" count="8">
    <value>System.Dll</value>
    <value>System.Drawing.Dll</value>
    <value>System.Windows.Forms.Dll</value>
    <value>System.Data.Dll</value>
    <value>System.Xml.Dll</value>
    <value>Stimulsoft.Controls.Dll</value>
    <value>Stimulsoft.Base.Dll</value>
    <value>Stimulsoft.Report.Dll</value>
  </ReferencedAssemblies>
  <ReportAlias>吉林省医疗机构门诊收费专用票据</ReportAlias>
  <ReportChanged>11/2/2020 2:25:00 PM</ReportChanged>
  <ReportCreated>5/28/2012 9:16:24 AM</ReportCreated>
  <ReportFile>C:\Users\<USER>\Desktop\his2010v3(长春)\his2010\Rpt\内蒙古通辽市门诊收费专用票据(不套打).mrt</ReportFile>
  <ReportGuid>3f6ca284cf234fad813268620a25d388</ReportGuid>
  <ReportName>吉林省医疗机构门诊收费专用票据</ReportName>
  <ReportUnit>Centimeters</ReportUnit>
  <ReportVersion>2011.2.1026</ReportVersion>
  <Script>using System;
using System.Drawing;
using System.Windows.Forms;
using System.Data;
using Stimulsoft.Controls;
using Stimulsoft.Base.Drawing;
using Stimulsoft.Report;
using Stimulsoft.Report.Dialogs;
using Stimulsoft.Report.Components;

namespace Reports
{
    public class Report : Stimulsoft.Report.StiReport
    {
        public Report()        {
            this.InitializeComponent();
        }

        #region StiReport Designer generated code - do not modify
        public DateTime 打印时间;
        public DateTime 打印时间1;
        public DateTime 打印时间2;
        public DateTime 打印时间3;
        public string 打印编号;
        public string 打印编号1;
        public string 打印编号2;
        public string 打印编号3;
        public string 患者姓名;
        public string 患者姓名1;
        public string 患者姓名2;
        public string 患者姓名3;
        public string 医院名称;
        public string 患者类别;
        public string 收款员;
        public string 科室1;
        public string 科室2;
        public string 科室3;
        public string 类别1;
        public string 类别2;
        public string 类别3;
        public string 类别4;
        public string 类别5;
        public string 类别6;
        public string 作废1;
        public string 作废2;
        public string 作废3;
        public double 类别金额1;
        public double 类别金额2;
        public double 类别金额3;
        public double 类别金额4;
        public double 类别金额5;
        public double 类别金额6;
        public double 合计1;
        public double 合计2;
        public double 合计3;
        public double 合计;
        public double 检查费;
        public double 手术费;
        public double 输氧费;
        public double 输血费;
        public double 处置费;
        public double 注射费;
        public double 治疗费;
        public double 西药费;
        public double 中成药;
        public double 中草药;
        public double 化验费;
        public double X光费;
        public double 电诊费;
        public double CT费;
        public double 磁共振;
        public double 原个人账户余额;
        public double 卡支付;
        public double 现金支付;
        public double 各项补助支出;
        public double 工伤保险支付;
        public double 生育保险支付;
        public string 支出合计;
        public double 个人账户余额;
        public string 社保编号;
        public string 医保编号;
        public Stimulsoft.Report.Components.StiPage Page1;
        public Stimulsoft.Report.Components.StiPageHeaderBand PageHeaderBand1;
        public Stimulsoft.Report.Components.StiText Text2;
        public Stimulsoft.Report.Components.StiText Text3;
        public Stimulsoft.Report.Components.StiText Text4;
        public Stimulsoft.Report.Components.StiText Text5;
        public Stimulsoft.Report.Components.StiText Text6;
        public Stimulsoft.Report.Components.StiText Text7;
        public Stimulsoft.Report.Components.StiText Text8;
        public Stimulsoft.Report.Components.StiText Text9;
        public Stimulsoft.Report.Components.StiText Text10;
        public Stimulsoft.Report.Components.StiText Text11;
        public Stimulsoft.Report.Components.StiText Text12;
        public Stimulsoft.Report.Components.StiText Text13;
        public Stimulsoft.Report.Components.StiText Text14;
        public Stimulsoft.Report.Components.StiText Text15;
        public Stimulsoft.Report.Components.StiText Text16;
        public Stimulsoft.Report.Components.StiText Text1;
        public Stimulsoft.Report.Components.StiText Text17;
        public Stimulsoft.Report.Components.StiText Text18;
        public Stimulsoft.Report.Components.StiText Text19;
        public Stimulsoft.Report.Components.StiText Text20;
        public Stimulsoft.Report.Components.StiText Text21;
        public Stimulsoft.Report.Components.StiText Text22;
        public Stimulsoft.Report.Components.StiText Text23;
        public Stimulsoft.Report.Components.StiText Text24;
        public Stimulsoft.Report.Components.StiText Text25;
        public Stimulsoft.Report.Components.StiText Text26;
        public Stimulsoft.Report.Components.StiText Text27;
        public Stimulsoft.Report.Components.StiText Text28;
        public Stimulsoft.Report.Components.StiText Text29;
        public Stimulsoft.Report.Components.StiText Text30;
        public Stimulsoft.Report.Components.StiText Text31;
        public Stimulsoft.Report.Components.StiText Text32;
        public Stimulsoft.Report.Components.StiText Text33;
        public Stimulsoft.Report.Components.StiText Text34;
        public Stimulsoft.Report.Components.StiText Text35;
        public Stimulsoft.Report.Components.StiText Text36;
        public Stimulsoft.Report.Components.StiText Text37;
        public Stimulsoft.Report.Components.StiText Text38;
        public Stimulsoft.Report.Components.StiText Text39;
        public Stimulsoft.Report.Components.StiText Text40;
        public Stimulsoft.Report.Components.StiText Text41;
        public Stimulsoft.Report.Components.StiText Text42;
        public Stimulsoft.Report.Components.StiText Text43;
        public Stimulsoft.Report.Components.StiText Text44;
        public Stimulsoft.Report.Components.StiText Text45;
        public Stimulsoft.Report.Components.StiText Text46;
        public Stimulsoft.Report.Components.StiText Text47;
        public Stimulsoft.Report.Components.StiText Text48;
        public Stimulsoft.Report.Components.StiText Text49;
        public Stimulsoft.Report.Components.StiText Text50;
        public Stimulsoft.Report.Components.StiText Text51;
        public Stimulsoft.Report.Components.StiText Text52;
        public Stimulsoft.Report.Components.StiText Text53;
        public Stimulsoft.Report.Components.StiText Text54;
        public Stimulsoft.Report.Components.StiText Text55;
        public Stimulsoft.Report.Components.StiText Text56;
        public Stimulsoft.Report.Components.StiText Text57;
        public Stimulsoft.Report.Components.StiText Text58;
        public Stimulsoft.Report.Components.StiText Text59;
        public Stimulsoft.Report.Components.StiText Text60;
        public Stimulsoft.Report.Components.StiText Text61;
        public Stimulsoft.Report.Components.StiText Text62;
        public Stimulsoft.Report.Components.StiText Text63;
        public Stimulsoft.Report.Components.StiText Text64;
        public Stimulsoft.Report.Components.StiText Text65;
        public Stimulsoft.Report.Components.StiText Text66;
        public Stimulsoft.Report.Components.StiText Text67;
        public Stimulsoft.Report.Components.StiText Text68;
        public Stimulsoft.Report.Components.StiText Text69;
        public Stimulsoft.Report.Components.StiText Text70;
        public Stimulsoft.Report.Components.StiText Text71;
        public Stimulsoft.Report.Components.StiText Text72;
        public Stimulsoft.Report.Components.StiText Text73;
        public Stimulsoft.Report.Components.StiText Text74;
        public Stimulsoft.Report.Components.StiText Text75;
        public Stimulsoft.Report.Components.StiText Text76;
        public Stimulsoft.Report.Components.StiText Text77;
        public Stimulsoft.Report.Components.StiText Text78;
        public Stimulsoft.Report.Components.StiText Text79;
        public Stimulsoft.Report.Components.StiText Text80;
        public Stimulsoft.Report.Components.StiText Text81;
        public Stimulsoft.Report.Components.StiText Text82;
        public Stimulsoft.Report.Components.StiText Text83;
        public Stimulsoft.Report.Components.StiText Text84;
        public Stimulsoft.Report.Components.StiText Text85;
        public Stimulsoft.Report.Components.StiText Text86;
        public Stimulsoft.Report.Components.StiText Text87;
        public Stimulsoft.Report.Components.StiText Text88;
        public Stimulsoft.Report.Components.StiText Text89;
        public Stimulsoft.Report.Components.StiText Text90;
        public Stimulsoft.Report.Components.StiText Text91;
        public Stimulsoft.Report.Components.StiText Text92;
        public Stimulsoft.Report.Components.StiText Text93;
        public Stimulsoft.Report.Components.StiText Text94;
        public Stimulsoft.Report.Components.StiText Text95;
        public Stimulsoft.Report.Components.StiText Text96;
        public Stimulsoft.Report.Components.StiText Text97;
        public Stimulsoft.Report.Components.StiText Text98;
        public Stimulsoft.Report.Components.StiText Text99;
        public Stimulsoft.Report.Components.StiText Text100;
        public Stimulsoft.Report.Components.StiWatermark Page1_Watermark;
        public Stimulsoft.Report.Print.StiPrinterSettings 吉林省医疗机构门诊收费专用票据_PrinterSettings;
        
        public void Text2__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, 打印编号1, true);
        }
        
        public void Text3__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, 打印时间1, true);
        }
        
        public void Text4__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, 患者姓名1, true);
        }
        
        public void Text5__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, 科室1, true);
        }
        
        public void Text6__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = System.String.Format("{0:#0.00}", 合计1);
        }
        
        public void Text7__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, 打印编号2, true);
        }
        
        public void Text8__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, 打印时间2, true);
        }
        
        public void Text9__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, 患者姓名2, true);
        }
        
        public void Text10__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, 科室2, true);
        }
        
        public void Text11__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = System.String.Format("{0:#0.00}", 合计2);
        }
        
        public void Text12__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, 打印编号3, true);
        }
        
        public void Text13__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, 打印时间3, true);
        }
        
        public void Text14__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, 患者姓名3, true);
        }
        
        public void Text15__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, 科室3, true);
        }
        
        public void Text16__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = System.String.Format("{0:#0.00}", 合计3);
        }
        
        public void Text1__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, 类别1, true);
        }
        
        public void Text17__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = System.String.Format("{0:#0.00}", 类别金额1);
        }
        
        public void Text18__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, 类别2, true);
        }
        
        public void Text19__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = System.String.Format("{0:#0.00}", 类别金额2);
        }
        
        public void Text20__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, 类别3, true);
        }
        
        public void Text21__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = System.String.Format("{0:#0.00}", 类别金额3);
        }
        
        public void Text22__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, 类别4, true);
        }
        
        public void Text23__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = System.String.Format("{0:#0.00}", 类别金额4);
        }
        
        public void Text24__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = " " + ToString(sender, 类别5, true);
        }
        
        public void Text25__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = System.String.Format("{0:#0.00}", 类别金额5);
        }
        
        public void Text26__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, 类别6, true);
        }
        
        public void Text27__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = System.String.Format("{0:#0.00}", 类别金额6);
        }
        
        public void Text28__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "金额";
        }
        
        public void Text29__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = System.String.Format("{0:#0.00}", 检查费);
        }
        
        public void Text30__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = System.String.Format("{0:#0.00}", 手术费);
        }
        
        public void Text31__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = System.String.Format("{0:#0.00}", 输氧费);
        }
        
        public void Text32__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = System.String.Format("{0:#0.00}", 输血费);
        }
        
        public void Text33__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = System.String.Format("{0:#0.00}", 处置费);
        }
        
        public void Text34__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = System.String.Format("{0:#0.00}", 注射费);
        }
        
        public void Text35__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = System.String.Format("{0:#0.00}", 治疗费);
        }
        
        public void Text36__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "项目";
        }
        
        public void Text37__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "金额";
        }
        
        public void Text38__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = System.String.Format("{0:#0.00}", 西药费);
        }
        
        public void Text39__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = System.String.Format("{0:#0.00}", 中成药);
        }
        
        public void Text40__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = System.String.Format("{0:#0.00}", 中草药);
        }
        
        public void Text41__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = System.String.Format("{0:#0.00}", 化验费);
        }
        
        public void Text42__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = System.String.Format("{0:#0.00}", X光费);
        }
        
        public void Text43__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = System.String.Format("{0:#0.00}", 电诊费);
        }
        
        public void Text44__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = System.String.Format("{0:#0.00}", CT费);
        }
        
        public void Text45__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "项目";
        }
        
        public void Text46__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = System.String.Format("{0:#0.00}", 磁共振);
        }
        
        public void Text47__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, 原个人账户余额, true);
        }
        
        public void Text48__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, 卡支付, true);
        }
        
        public void Text49__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, 合计, true);
        }
        
        public void Text50__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, 各项补助支出, true);
        }
        
        public void Text51__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, 工伤保险支付, true);
        }
        
        public void Text52__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, 生育保险支付, true);
        }
        
        public void Text53__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, 支出合计, true);
        }
        
        public void Text54__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, 个人账户余额, true);
        }
        
        public void Text55__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, 打印时间, true);
        }
        
        public void Text56__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, 收款员, true);
        }
        
        public void Text57__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "金额";
        }
        
        public void Text58__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = System.String.Format("{0:#0.00}", 检查费);
        }
        
        public void Text59__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = System.String.Format("{0:#0.00}", 手术费);
        }
        
        public void Text60__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = System.String.Format("{0:#0.00}", 输氧费);
        }
        
        public void Text61__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = System.String.Format("{0:#0.00}", 输血费);
        }
        
        public void Text62__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = System.String.Format("{0:#0.00}", 处置费);
        }
        
        public void Text63__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = System.String.Format("{0:#0.00}", 注射费);
        }
        
        public void Text64__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = System.String.Format("{0:#0.00}", 治疗费);
        }
        
        public void Text65__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "项目";
        }
        
        public void Text66__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "金额";
        }
        
        public void Text67__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = System.String.Format("{0:#0.00}", 西药费);
        }
        
        public void Text68__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = System.String.Format("{0:#0.00}", 中成药);
        }
        
        public void Text69__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = System.String.Format("{0:#0.00}", 中草药);
        }
        
        public void Text70__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = System.String.Format("{0:#0.00}", 化验费);
        }
        
        public void Text71__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = System.String.Format("{0:#0.00}", X光费);
        }
        
        public void Text72__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = System.String.Format("{0:#0.00}", 电诊费);
        }
        
        public void Text73__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = System.String.Format("{0:#0.00}", CT费);
        }
        
        public void Text74__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "项目";
        }
        
        public void Text75__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = System.String.Format("{0:#0.00}", 磁共振);
        }
        
        public void Text76__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, 原个人账户余额, true);
        }
        
        public void Text77__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, 卡支付, true);
        }
        
        public void Text78__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, 合计, true);
        }
        
        public void Text79__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, 各项补助支出, true);
        }
        
        public void Text80__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, 工伤保险支付, true);
        }
        
        public void Text81__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, 生育保险支付, true);
        }
        
        public void Text82__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, 支出合计, true);
        }
        
        public void Text83__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, 个人账户余额, true);
        }
        
        public void Text84__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, 打印时间, true);
        }
        
        public void Text85__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, 收款员, true);
        }
        
        public void Text86__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, 医院名称, true);
        }
        
        public void Text87__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, 医保编号, true);
        }
        
        public void Text88__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, 患者姓名, true);
        }
        
        public void Text89__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, 社保编号, true);
        }
        
        public void Text90__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, 患者类别, true);
        }
        
        public void Text91__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, 打印编号, true);
        }
        
        public void Text92__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, 医院名称, true);
        }
        
        public void Text93__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, 医保编号, true);
        }
        
        public void Text94__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, 患者姓名, true);
        }
        
        public void Text95__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, 社保编号, true);
        }
        
        public void Text96__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, 患者类别, true);
        }
        
        public void Text97__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, 打印编号, true);
        }
        
        public void Text98__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, 作废3, true);
        }
        
        public void Text99__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, 作废2, true);
        }
        
        public void Text100__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, 作废1, true);
        }
        
        private void InitializeComponent()
        {
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "打印时间", "打印时间", "", typeof(DateTime), "5/28/2012 9:53:02 AM", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "打印时间1", "打印时间1", "", typeof(DateTime), "6/27/2012 11:38:46 AM", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "打印时间2", "打印时间2", "", typeof(DateTime), "6/27/2012 11:39:07 AM", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "打印时间3", "打印时间3", "", typeof(DateTime), "6/27/2012 11:39:12 AM", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "打印编号", "打印编号", "", typeof(string), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "打印编号1", "打印编号1", "", typeof(string), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "打印编号2", "打印编号2", "", typeof(string), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "打印编号3", "打印编号3", "", typeof(string), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "患者姓名", "患者姓名", "", typeof(string), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "患者姓名1", "患者姓名1", "", typeof(string), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "患者姓名2", "患者姓名2", "", typeof(string), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "患者姓名3", "患者姓名3", "", typeof(string), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "医院名称", "医院名称", "", typeof(string), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "患者类别", "患者类别", "", typeof(string), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "收款员", "收款员", "", typeof(string), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "科室1", "科室1", "", typeof(string), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "科室2", "科室2", "", typeof(string), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "科室3", "科室3", "", typeof(string), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "类别1", "类别1", "", typeof(string), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "类别2", "类别2", "", typeof(string), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "类别3", "类别3", "", typeof(string), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "类别4", "类别4", "", typeof(string), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "类别5", "类别5", "", typeof(string), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "类别6", "类别6", "", typeof(string), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "作废1", "作废1", "", typeof(string), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "作废2", "作废2", "", typeof(string), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "作废3", "作废3", "", typeof(string), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "类别金额1", "类别金额1", "", typeof(double), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "类别金额2", "类别金额2", "", typeof(double), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "类别金额3", "类别金额3", "", typeof(double), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "类别金额4", "类别金额4", "", typeof(double), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "类别金额5", "类别金额5", "", typeof(double), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "类别金额6", "类别金额6", "", typeof(double), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "合计1", "合计1", "", typeof(double), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "合计2", "合计2", "", typeof(double), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "合计3", "合计3", "", typeof(double), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "合计", "合计", "", typeof(double), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "检查费", "检查费", "", typeof(double), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "手术费", "手术费", "", typeof(double), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "输氧费", "输氧费", "", typeof(double), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "输血费", "输血费", "", typeof(double), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "处置费", "处置费", "", typeof(double), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "注射费", "注射费", "", typeof(double), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "治疗费", "治疗费", "", typeof(double), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "西药费", "西药费", "", typeof(double), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "中成药", "中成药", "", typeof(double), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "中草药", "中草药", "", typeof(double), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "化验费", "化验费", "", typeof(double), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "X光费", "X光费", "", typeof(double), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "电诊费", "电诊费", "", typeof(double), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "CT费", "CT费", "", typeof(double), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "磁共振", "磁共振", "", typeof(double), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "原个人账户余额", "原个人账户余额", "", typeof(double), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "卡支付", "卡支付", "", typeof(double), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "现金支付", "现金支付", "", typeof(double), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "各项补助支出", "各项补助支出", "", typeof(double), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "工伤保险支付", "工伤保险支付", "", typeof(double), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "生育保险支付", "生育保险支付", "", typeof(double), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "支出合计", "支出合计", "", typeof(string), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "个人账户余额", "个人账户余额", "", typeof(double), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "社保编号", "社保编号", "", typeof(string), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "医保编号", "医保编号", "", typeof(string), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.NeedsCompiling = false;
            // 
            // Variables init
            // 
            this.打印时间 = ParseDateTime("5/28/2012 9:53:02 AM");
            this.打印时间1 = ParseDateTime("6/27/2012 11:38:46 AM");
            this.打印时间2 = ParseDateTime("6/27/2012 11:39:07 AM");
            this.打印时间3 = ParseDateTime("6/27/2012 11:39:12 AM");
            this.打印编号 = "";
            this.打印编号1 = "";
            this.打印编号2 = "";
            this.打印编号3 = "";
            this.患者姓名 = "";
            this.患者姓名1 = "";
            this.患者姓名2 = "";
            this.患者姓名3 = "";
            this.医院名称 = "";
            this.患者类别 = "";
            this.收款员 = "";
            this.科室1 = "";
            this.科室2 = "";
            this.科室3 = "";
            this.类别1 = "";
            this.类别2 = "";
            this.类别3 = "";
            this.类别4 = "";
            this.类别5 = "";
            this.类别6 = "";
            this.作废1 = "";
            this.作废2 = "";
            this.作废3 = "";
            this.类别金额1 = 0d;
            this.类别金额2 = 0d;
            this.类别金额3 = 0d;
            this.类别金额4 = 0d;
            this.类别金额5 = 0d;
            this.类别金额6 = 0d;
            this.合计1 = 0d;
            this.合计2 = 0d;
            this.合计3 = 0d;
            this.合计 = 0d;
            this.检查费 = 0d;
            this.手术费 = 0d;
            this.输氧费 = 0d;
            this.输血费 = 0d;
            this.处置费 = 0d;
            this.注射费 = 0d;
            this.治疗费 = 0d;
            this.西药费 = 0d;
            this.中成药 = 0d;
            this.中草药 = 0d;
            this.化验费 = 0d;
            this.X光费 = 0d;
            this.电诊费 = 0d;
            this.CT费 = 0d;
            this.磁共振 = 0d;
            this.原个人账户余额 = 0d;
            this.卡支付 = 0d;
            this.现金支付 = 0d;
            this.各项补助支出 = 0d;
            this.工伤保险支付 = 0d;
            this.生育保险支付 = 0d;
            this.支出合计 = "";
            this.个人账户余额 = 0d;
            this.社保编号 = "";
            this.医保编号 = "";
            this.EngineVersion = Stimulsoft.Report.Engine.StiEngineVersion.EngineV2;
            this.ReferencedAssemblies = new System.String[] {
                    "System.Dll",
                    "System.Drawing.Dll",
                    "System.Windows.Forms.Dll",
                    "System.Data.Dll",
                    "System.Xml.Dll",
                    "Stimulsoft.Controls.Dll",
                    "Stimulsoft.Base.Dll",
                    "Stimulsoft.Report.Dll"};
            this.ReportAlias = "吉林省医疗机构门诊收费专用票据";
            // 
            // ReportChanged
            // 
            this.ReportChanged = new DateTime(2012, 6, 27, 15, 25, 51, 0);
            // 
            // ReportCreated
            // 
            this.ReportCreated = new DateTime(2012, 5, 28, 9, 16, 24, 0);
            this.ReportFile = "E:\\BJF\\[ING]\\NET\\his2010-New-增加科室对诊疗\\output\\Rpt\\吉林省医疗机构门诊收费专用票据.mrt";
            this.ReportGuid = "958ef65c39464931b3ead60605035631";
            this.ReportName = "吉林省医疗机构门诊收费专用票据";
            this.ReportUnit = Stimulsoft.Report.StiReportUnitType.Centimeters;
            this.ReportVersion = "2011.2.1026";
            this.ScriptLanguage = Stimulsoft.Report.StiReportLanguageType.CSharp;
            // 
            // Page1
            // 
            this.Page1 = new Stimulsoft.Report.Components.StiPage();
            this.Page1.Guid = "b0a70bb994724837b3e8b468faf8f262";
            this.Page1.LargeHeight = true;
            this.Page1.Name = "Page1";
            this.Page1.PageHeight = 15.3;
            this.Page1.PageWidth = 24;
            this.Page1.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 2, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Page1.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            // 
            // PageHeaderBand1
            // 
            this.PageHeaderBand1 = new Stimulsoft.Report.Components.StiPageHeaderBand();
            this.PageHeaderBand1.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(0, 0.4, 24, 15.3);
            this.PageHeaderBand1.Name = "PageHeaderBand1";
            this.PageHeaderBand1.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.PageHeaderBand1.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            // 
            // Text2
            // 
            this.Text2 = new Stimulsoft.Report.Components.StiText();
            this.Text2.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(15.6, 1.7, 3.3, 0.5);
            this.Text2.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Right;
            this.Text2.Name = "Text2";
            this.Text2.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text2__GetValue);
            this.Text2.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text2.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text2.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text2.Font = new System.Drawing.Font("Arial", 8F);
            this.Text2.Guid = null;
            this.Text2.Indicator = null;
            this.Text2.Interaction = null;
            this.Text2.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text2.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text2.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text3
            // 
            this.Text3 = new Stimulsoft.Report.Components.StiText();
            this.Text3.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(15.6, 2.2, 3.3, 0.5);
            this.Text3.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Right;
            this.Text3.Name = "Text3";
            this.Text3.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text3__GetValue);
            this.Text3.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text3.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text3.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text3.Font = new System.Drawing.Font("Arial", 8F);
            this.Text3.Guid = null;
            this.Text3.Indicator = null;
            this.Text3.Interaction = null;
            this.Text3.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text3.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text3.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text4
            // 
            this.Text4 = new Stimulsoft.Report.Components.StiText();
            this.Text4.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(18.9, 1.7, 3.2, 0.5);
            this.Text4.Guid = "c1b218a94303471ab779349a2e444bea";
            this.Text4.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text4.Name = "Text4";
            this.Text4.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text4__GetValue);
            this.Text4.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text4.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text4.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text4.Font = new System.Drawing.Font("Arial", 8F);
            this.Text4.Indicator = null;
            this.Text4.Interaction = null;
            this.Text4.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text4.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text4.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text5
            // 
            this.Text5 = new Stimulsoft.Report.Components.StiText();
            this.Text5.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(18.9, 2.2, 3.2, 0.5);
            this.Text5.Guid = "3426cc15edd740ef9846bfe22feee856";
            this.Text5.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text5.Name = "Text5";
            this.Text5.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text5__GetValue);
            this.Text5.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text5.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text5.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text5.Font = new System.Drawing.Font("Arial", 8F);
            this.Text5.Indicator = null;
            this.Text5.Interaction = null;
            this.Text5.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text5.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text5.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text6
            // 
            this.Text6 = new Stimulsoft.Report.Components.StiText();
            this.Text6.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(15.6, 3.7, 3.8, 0.5);
            this.Text6.HideZeros = true;
            this.Text6.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Right;
            this.Text6.Name = "Text6";
            this.Text6.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text6__GetValue);
            this.Text6.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text6.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text6.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text6.Font = new System.Drawing.Font("Arial", 8F);
            this.Text6.Guid = null;
            this.Text6.Indicator = null;
            this.Text6.Interaction = null;
            this.Text6.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text6.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text6.TextFormat = new Stimulsoft.Report.Components.TextFormats.StiCustomFormatService("#0.00");
            this.Text6.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text7
            // 
            this.Text7 = new Stimulsoft.Report.Components.StiText();
            this.Text7.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(15.6, 6.2, 3.3, 0.5);
            this.Text7.Guid = "3f8b4be83c1d45c88565c73521dc54ab";
            this.Text7.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Right;
            this.Text7.Name = "Text7";
            this.Text7.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text7__GetValue);
            this.Text7.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text7.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text7.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text7.Font = new System.Drawing.Font("Arial", 8F);
            this.Text7.Indicator = null;
            this.Text7.Interaction = null;
            this.Text7.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text7.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text7.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text8
            // 
            this.Text8 = new Stimulsoft.Report.Components.StiText();
            this.Text8.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(15.6, 6.7, 3.3, 0.5);
            this.Text8.Guid = "1cccf1d1667e403abb437abcab485a44";
            this.Text8.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Right;
            this.Text8.Name = "Text8";
            this.Text8.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text8__GetValue);
            this.Text8.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text8.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text8.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text8.Font = new System.Drawing.Font("Arial", 8F);
            this.Text8.Indicator = null;
            this.Text8.Interaction = null;
            this.Text8.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text8.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text8.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text9
            // 
            this.Text9 = new Stimulsoft.Report.Components.StiText();
            this.Text9.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(18.9, 6.2, 3.2, 0.5);
            this.Text9.Guid = "aed944c4770a40fa8f5913d1bb18f7ec";
            this.Text9.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text9.Name = "Text9";
            this.Text9.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text9__GetValue);
            this.Text9.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text9.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text9.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text9.Font = new System.Drawing.Font("Arial", 8F);
            this.Text9.Indicator = null;
            this.Text9.Interaction = null;
            this.Text9.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text9.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text9.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text10
            // 
            this.Text10 = new Stimulsoft.Report.Components.StiText();
            this.Text10.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(18.9, 6.7, 3.2, 0.5);
            this.Text10.Guid = "ae885e6c44be471e9e5fa8b18adb0b6c";
            this.Text10.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text10.Name = "Text10";
            this.Text10.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text10__GetValue);
            this.Text10.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text10.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text10.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text10.Font = new System.Drawing.Font("Arial", 8F);
            this.Text10.Indicator = null;
            this.Text10.Interaction = null;
            this.Text10.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text10.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text10.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text11
            // 
            this.Text11 = new Stimulsoft.Report.Components.StiText();
            this.Text11.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(15.6, 8.2, 3.8, 0.5);
            this.Text11.Guid = "622c27d7c0c549afbfc6cf19f38c5483";
            this.Text11.HideZeros = true;
            this.Text11.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Right;
            this.Text11.Name = "Text11";
            this.Text11.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text11__GetValue);
            this.Text11.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text11.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text11.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text11.Font = new System.Drawing.Font("Arial", 8F);
            this.Text11.Indicator = null;
            this.Text11.Interaction = null;
            this.Text11.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text11.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text11.TextFormat = new Stimulsoft.Report.Components.TextFormats.StiCustomFormatService("#0.00");
            this.Text11.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text12
            // 
            this.Text12 = new Stimulsoft.Report.Components.StiText();
            this.Text12.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(15.6, 10.65, 3.3, 0.5);
            this.Text12.Guid = "9ab5455cb6854dffbd25a3b959ea6e2e";
            this.Text12.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Right;
            this.Text12.Name = "Text12";
            this.Text12.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text12__GetValue);
            this.Text12.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text12.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text12.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text12.Font = new System.Drawing.Font("Arial", 8F);
            this.Text12.Indicator = null;
            this.Text12.Interaction = null;
            this.Text12.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text12.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text12.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text13
            // 
            this.Text13 = new Stimulsoft.Report.Components.StiText();
            this.Text13.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(15.6, 11.15, 3.3, 0.5);
            this.Text13.Guid = "dabe54ac22534c908bb9721b822fd3a3";
            this.Text13.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Right;
            this.Text13.Name = "Text13";
            this.Text13.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text13__GetValue);
            this.Text13.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text13.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text13.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text13.Font = new System.Drawing.Font("Arial", 8F);
            this.Text13.Indicator = null;
            this.Text13.Interaction = null;
            this.Text13.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text13.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text13.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text14
            // 
            this.Text14 = new Stimulsoft.Report.Components.StiText();
            this.Text14.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(18.9, 10.65, 3.2, 0.5);
            this.Text14.Guid = "6cb7d92e045d46bcb1e7e17b4c4132b8";
            this.Text14.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text14.Name = "Text14";
            this.Text14.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text14__GetValue);
            this.Text14.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text14.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text14.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text14.Font = new System.Drawing.Font("Arial", 8F);
            this.Text14.Indicator = null;
            this.Text14.Interaction = null;
            this.Text14.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text14.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text14.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text15
            // 
            this.Text15 = new Stimulsoft.Report.Components.StiText();
            this.Text15.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(18.9, 11.15, 3.2, 0.5);
            this.Text15.Guid = "a2a3b9afc9a94862a8f8e7d18d743bde";
            this.Text15.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text15.Name = "Text15";
            this.Text15.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text15__GetValue);
            this.Text15.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text15.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text15.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text15.Font = new System.Drawing.Font("Arial", 8F);
            this.Text15.Indicator = null;
            this.Text15.Interaction = null;
            this.Text15.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text15.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text15.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text16
            // 
            this.Text16 = new Stimulsoft.Report.Components.StiText();
            this.Text16.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(15.6, 12.65, 3.8, 0.5);
            this.Text16.Guid = "1dbf35fa59b648eabbd06a83f80e573e";
            this.Text16.HideZeros = true;
            this.Text16.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Right;
            this.Text16.Name = "Text16";
            this.Text16.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text16__GetValue);
            this.Text16.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text16.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text16.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text16.Font = new System.Drawing.Font("Arial", 8F);
            this.Text16.Indicator = null;
            this.Text16.Interaction = null;
            this.Text16.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text16.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text16.TextFormat = new Stimulsoft.Report.Components.TextFormats.StiCustomFormatService("#0.00");
            this.Text16.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text1
            // 
            this.Text1 = new Stimulsoft.Report.Components.StiText();
            this.Text1.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(15.6, 2.7, 3.8, 0.5);
            this.Text1.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Right;
            this.Text1.Name = "Text1";
            this.Text1.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text1__GetValue);
            this.Text1.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text1.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text1.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text1.Font = new System.Drawing.Font("Arial", 8F);
            this.Text1.Guid = null;
            this.Text1.Indicator = null;
            this.Text1.Interaction = null;
            this.Text1.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text1.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text1.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text17
            // 
            this.Text17 = new Stimulsoft.Report.Components.StiText();
            this.Text17.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(19.4, 2.7, 2.7, 0.5);
            this.Text17.Guid = "3261bdfe4b454dc6bb87d34a9812709f";
            this.Text17.HideZeros = true;
            this.Text17.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Right;
            this.Text17.Name = "Text17";
            this.Text17.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text17__GetValue);
            this.Text17.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text17.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text17.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text17.Font = new System.Drawing.Font("Arial", 8F);
            this.Text17.Indicator = null;
            this.Text17.Interaction = null;
            this.Text17.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text17.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text17.TextFormat = new Stimulsoft.Report.Components.TextFormats.StiCustomFormatService("#0.00");
            this.Text17.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text18
            // 
            this.Text18 = new Stimulsoft.Report.Components.StiText();
            this.Text18.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(15.6, 3.2, 3.8, 0.5);
            this.Text18.Guid = "466fd20e47c74579bc56931b38c622d7";
            this.Text18.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Right;
            this.Text18.Name = "Text18";
            this.Text18.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text18__GetValue);
            this.Text18.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text18.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text18.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text18.Font = new System.Drawing.Font("Arial", 8F);
            this.Text18.Indicator = null;
            this.Text18.Interaction = null;
            this.Text18.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text18.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text18.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text19
            // 
            this.Text19 = new Stimulsoft.Report.Components.StiText();
            this.Text19.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(19.4, 3.2, 2.7, 0.5);
            this.Text19.Guid = "3a2bae24e8d74c34a6e67e3fdf974306";
            this.Text19.HideZeros = true;
            this.Text19.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Right;
            this.Text19.Name = "Text19";
            this.Text19.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text19__GetValue);
            this.Text19.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text19.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text19.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text19.Font = new System.Drawing.Font("Arial", 8F);
            this.Text19.Indicator = null;
            this.Text19.Interaction = null;
            this.Text19.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text19.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text19.TextFormat = new Stimulsoft.Report.Components.TextFormats.StiCustomFormatService("#0.00");
            this.Text19.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text20
            // 
            this.Text20 = new Stimulsoft.Report.Components.StiText();
            this.Text20.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(15.6, 7.2, 3.8, 0.5);
            this.Text20.Guid = "7b4f828f947245849a791729eda99b36";
            this.Text20.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Right;
            this.Text20.Name = "Text20";
            this.Text20.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text20__GetValue);
            this.Text20.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text20.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text20.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text20.Font = new System.Drawing.Font("Arial", 8F);
            this.Text20.Indicator = null;
            this.Text20.Interaction = null;
            this.Text20.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text20.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text20.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text21
            // 
            this.Text21 = new Stimulsoft.Report.Components.StiText();
            this.Text21.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(19.4, 7.2, 2.7, 0.5);
            this.Text21.Guid = "0e20efff38e34a2ba7f2b3be76201b08";
            this.Text21.HideZeros = true;
            this.Text21.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Right;
            this.Text21.Name = "Text21";
            this.Text21.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text21__GetValue);
            this.Text21.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text21.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text21.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text21.Font = new System.Drawing.Font("Arial", 8F);
            this.Text21.Indicator = null;
            this.Text21.Interaction = null;
            this.Text21.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text21.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text21.TextFormat = new Stimulsoft.Report.Components.TextFormats.StiCustomFormatService("#0.00");
            this.Text21.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text22
            // 
            this.Text22 = new Stimulsoft.Report.Components.StiText();
            this.Text22.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(15.6, 7.7, 3.8, 0.5);
            this.Text22.Guid = "3d9f7dbb40584cf48511bce3748fd2e0";
            this.Text22.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Right;
            this.Text22.Name = "Text22";
            this.Text22.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text22__GetValue);
            this.Text22.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text22.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text22.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text22.Font = new System.Drawing.Font("Arial", 8F);
            this.Text22.Indicator = null;
            this.Text22.Interaction = null;
            this.Text22.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text22.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text22.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text23
            // 
            this.Text23 = new Stimulsoft.Report.Components.StiText();
            this.Text23.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(19.4, 7.7, 2.7, 0.5);
            this.Text23.Guid = "ca30cc589cb64be4a6d471cd18807ca4";
            this.Text23.HideZeros = true;
            this.Text23.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Right;
            this.Text23.Name = "Text23";
            this.Text23.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text23__GetValue);
            this.Text23.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text23.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text23.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text23.Font = new System.Drawing.Font("Arial", 8F);
            this.Text23.Indicator = null;
            this.Text23.Interaction = null;
            this.Text23.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text23.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text23.TextFormat = new Stimulsoft.Report.Components.TextFormats.StiCustomFormatService("#0.00");
            this.Text23.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text24
            // 
            this.Text24 = new Stimulsoft.Report.Components.StiText();
            this.Text24.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(15.6, 11.65, 3.8, 0.5);
            this.Text24.Guid = "dac30ec0a28641849a674ef0962d7bf1";
            this.Text24.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Right;
            this.Text24.Name = "Text24";
            this.Text24.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text24__GetValue);
            this.Text24.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text24.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text24.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text24.Font = new System.Drawing.Font("Arial", 8F);
            this.Text24.Indicator = null;
            this.Text24.Interaction = null;
            this.Text24.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text24.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text24.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text25
            // 
            this.Text25 = new Stimulsoft.Report.Components.StiText();
            this.Text25.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(19.4, 11.65, 2.7, 0.5);
            this.Text25.Guid = "5c2d1d4033b54acb8c3b90bbb4bf1b86";
            this.Text25.HideZeros = true;
            this.Text25.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Right;
            this.Text25.Name = "Text25";
            this.Text25.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text25__GetValue);
            this.Text25.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text25.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text25.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text25.Font = new System.Drawing.Font("Arial", 8F);
            this.Text25.Indicator = null;
            this.Text25.Interaction = null;
            this.Text25.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text25.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text25.TextFormat = new Stimulsoft.Report.Components.TextFormats.StiCustomFormatService("#0.00");
            this.Text25.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text26
            // 
            this.Text26 = new Stimulsoft.Report.Components.StiText();
            this.Text26.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(15.6, 12.15, 3.8, 0.5);
            this.Text26.Guid = "81a7e517563c4571a687c5ec2f525f65";
            this.Text26.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Right;
            this.Text26.Name = "Text26";
            this.Text26.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text26__GetValue);
            this.Text26.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text26.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text26.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text26.Font = new System.Drawing.Font("Arial", 8F);
            this.Text26.Indicator = null;
            this.Text26.Interaction = null;
            this.Text26.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text26.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text26.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text27
            // 
            this.Text27 = new Stimulsoft.Report.Components.StiText();
            this.Text27.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(19.4, 12.15, 2.7, 0.5);
            this.Text27.Guid = "d62108f085004051b6b166310b665d0c";
            this.Text27.HideZeros = true;
            this.Text27.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Right;
            this.Text27.Name = "Text27";
            this.Text27.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text27__GetValue);
            this.Text27.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text27.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text27.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text27.Font = new System.Drawing.Font("Arial", 8F);
            this.Text27.Indicator = null;
            this.Text27.Interaction = null;
            this.Text27.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text27.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text27.TextFormat = new Stimulsoft.Report.Components.TextFormats.StiCustomFormatService("#0.00");
            this.Text27.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text28
            // 
            this.Text28 = new Stimulsoft.Report.Components.StiText();
            this.Text28.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(12.15, 3.6, 1.9, 0.5);
            this.Text28.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text28.Name = "Text28";
            this.Text28.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text28__GetValue);
            this.Text28.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text28.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text28.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text28.Font = new System.Drawing.Font("Arial", 8F);
            this.Text28.Guid = null;
            this.Text28.Indicator = null;
            this.Text28.Interaction = null;
            this.Text28.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text28.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text28.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text29
            // 
            this.Text29 = new Stimulsoft.Report.Components.StiText();
            this.Text29.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(11.15, 4.1, 2.9, 0.5);
            this.Text29.Guid = "02cfc691efca452382eea7f546a5bab5";
            this.Text29.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Right;
            this.Text29.Name = "Text29";
            this.Text29.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text29__GetValue);
            this.Text29.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text29.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text29.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text29.Font = new System.Drawing.Font("Arial", 8F);
            this.Text29.Indicator = null;
            this.Text29.Interaction = null;
            this.Text29.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text29.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text29.TextFormat = new Stimulsoft.Report.Components.TextFormats.StiCustomFormatService("#0.00");
            this.Text29.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text30
            // 
            this.Text30 = new Stimulsoft.Report.Components.StiText();
            this.Text30.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(11.15, 4.6, 2.9, 0.5);
            this.Text30.Guid = "e494099731304ae9af544b498b7dbc2f";
            this.Text30.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Right;
            this.Text30.Name = "Text30";
            this.Text30.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text30__GetValue);
            this.Text30.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text30.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text30.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text30.Font = new System.Drawing.Font("Arial", 8F);
            this.Text30.Indicator = null;
            this.Text30.Interaction = null;
            this.Text30.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text30.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text30.TextFormat = new Stimulsoft.Report.Components.TextFormats.StiCustomFormatService("#0.00");
            this.Text30.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text31
            // 
            this.Text31 = new Stimulsoft.Report.Components.StiText();
            this.Text31.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(11.15, 5.1, 2.9, 0.5);
            this.Text31.Guid = "9489364edeb74cf7917ab554623dc9ab";
            this.Text31.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Right;
            this.Text31.Name = "Text31";
            this.Text31.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text31__GetValue);
            this.Text31.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text31.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text31.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text31.Font = new System.Drawing.Font("Arial", 8F);
            this.Text31.Indicator = null;
            this.Text31.Interaction = null;
            this.Text31.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text31.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text31.TextFormat = new Stimulsoft.Report.Components.TextFormats.StiCustomFormatService("#0.00");
            this.Text31.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text32
            // 
            this.Text32 = new Stimulsoft.Report.Components.StiText();
            this.Text32.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(11.15, 5.6, 2.9, 0.5);
            this.Text32.Guid = "13149d83516945bba2c74893d860f431";
            this.Text32.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Right;
            this.Text32.Name = "Text32";
            this.Text32.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text32__GetValue);
            this.Text32.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text32.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text32.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text32.Font = new System.Drawing.Font("Arial", 8F);
            this.Text32.Indicator = null;
            this.Text32.Interaction = null;
            this.Text32.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text32.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text32.TextFormat = new Stimulsoft.Report.Components.TextFormats.StiCustomFormatService("#0.00");
            this.Text32.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text33
            // 
            this.Text33 = new Stimulsoft.Report.Components.StiText();
            this.Text33.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(11.15, 6.1, 2.9, 0.5);
            this.Text33.Guid = "5710e8bc4b8e4ebabd9808f52a06013a";
            this.Text33.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Right;
            this.Text33.Name = "Text33";
            this.Text33.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text33__GetValue);
            this.Text33.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text33.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text33.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text33.Font = new System.Drawing.Font("Arial", 8F);
            this.Text33.Indicator = null;
            this.Text33.Interaction = null;
            this.Text33.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text33.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text33.TextFormat = new Stimulsoft.Report.Components.TextFormats.StiCustomFormatService("#0.00");
            this.Text33.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text34
            // 
            this.Text34 = new Stimulsoft.Report.Components.StiText();
            this.Text34.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(11.15, 6.6, 2.9, 0.5);
            this.Text34.Guid = "478cd77463a24947acc7b500cc17e95b";
            this.Text34.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Right;
            this.Text34.Name = "Text34";
            this.Text34.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text34__GetValue);
            this.Text34.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text34.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text34.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text34.Font = new System.Drawing.Font("Arial", 8F);
            this.Text34.Indicator = null;
            this.Text34.Interaction = null;
            this.Text34.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text34.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text34.TextFormat = new Stimulsoft.Report.Components.TextFormats.StiCustomFormatService("#0.00");
            this.Text34.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text35
            // 
            this.Text35 = new Stimulsoft.Report.Components.StiText();
            this.Text35.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(11.15, 7.1, 2.9, 0.5);
            this.Text35.Guid = "6b358c9c7e4343e7bc311a6f1875c708";
            this.Text35.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Right;
            this.Text35.Name = "Text35";
            this.Text35.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text35__GetValue);
            this.Text35.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text35.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text35.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text35.Font = new System.Drawing.Font("Arial", 8F);
            this.Text35.Indicator = null;
            this.Text35.Interaction = null;
            this.Text35.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text35.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text35.TextFormat = new Stimulsoft.Report.Components.TextFormats.StiCustomFormatService("#0.00");
            this.Text35.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text36
            // 
            this.Text36 = new Stimulsoft.Report.Components.StiText();
            this.Text36.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(11.15, 3.6, 1, 0.5);
            this.Text36.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text36.Name = "Text36";
            this.Text36.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text36__GetValue);
            this.Text36.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text36.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text36.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text36.Font = new System.Drawing.Font("Arial", 8F);
            this.Text36.Guid = null;
            this.Text36.Indicator = null;
            this.Text36.Interaction = null;
            this.Text36.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text36.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text36.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text37
            // 
            this.Text37 = new Stimulsoft.Report.Components.StiText();
            this.Text37.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(9.35, 3.6, 1.8, 0.5);
            this.Text37.Guid = "e7ad2618d9fa479aa94f020324493184";
            this.Text37.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text37.Name = "Text37";
            this.Text37.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text37__GetValue);
            this.Text37.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text37.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text37.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text37.Font = new System.Drawing.Font("Arial", 8F);
            this.Text37.Indicator = null;
            this.Text37.Interaction = null;
            this.Text37.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text37.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text37.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text38
            // 
            this.Text38 = new Stimulsoft.Report.Components.StiText();
            this.Text38.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(8.35, 4.1, 2.8, 0.5);
            this.Text38.Guid = "582a28ea6ca847b695319fdff3aaedfa";
            this.Text38.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Right;
            this.Text38.Name = "Text38";
            this.Text38.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text38__GetValue);
            this.Text38.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text38.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text38.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text38.Font = new System.Drawing.Font("Arial", 8F);
            this.Text38.Indicator = null;
            this.Text38.Interaction = null;
            this.Text38.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text38.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text38.TextFormat = new Stimulsoft.Report.Components.TextFormats.StiCustomFormatService("#0.00");
            this.Text38.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text39
            // 
            this.Text39 = new Stimulsoft.Report.Components.StiText();
            this.Text39.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(8.35, 4.6, 2.8, 0.5);
            this.Text39.Guid = "fa6df7d9cf944a4fb72c30fa5a9f76bb";
            this.Text39.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Right;
            this.Text39.Name = "Text39";
            this.Text39.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text39__GetValue);
            this.Text39.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text39.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text39.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text39.Font = new System.Drawing.Font("Arial", 8F);
            this.Text39.Indicator = null;
            this.Text39.Interaction = null;
            this.Text39.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text39.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text39.TextFormat = new Stimulsoft.Report.Components.TextFormats.StiCustomFormatService("#0.00");
            this.Text39.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text40
            // 
            this.Text40 = new Stimulsoft.Report.Components.StiText();
            this.Text40.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(8.35, 5.1, 2.8, 0.5);
            this.Text40.Guid = "daa81dba95be4eac8ecd2c43e47aa9c1";
            this.Text40.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Right;
            this.Text40.Name = "Text40";
            this.Text40.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text40__GetValue);
            this.Text40.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text40.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text40.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text40.Font = new System.Drawing.Font("Arial", 8F);
            this.Text40.Indicator = null;
            this.Text40.Interaction = null;
            this.Text40.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text40.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text40.TextFormat = new Stimulsoft.Report.Components.TextFormats.StiCustomFormatService("#0.00");
            this.Text40.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text41
            // 
            this.Text41 = new Stimulsoft.Report.Components.StiText();
            this.Text41.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(8.35, 5.6, 2.8, 0.5);
            this.Text41.Guid = "c4f28656a04c4c1490e154295a8cb8ad";
            this.Text41.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Right;
            this.Text41.Name = "Text41";
            this.Text41.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text41__GetValue);
            this.Text41.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text41.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text41.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text41.Font = new System.Drawing.Font("Arial", 8F);
            this.Text41.Indicator = null;
            this.Text41.Interaction = null;
            this.Text41.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text41.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text41.TextFormat = new Stimulsoft.Report.Components.TextFormats.StiCustomFormatService("#0.00");
            this.Text41.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text42
            // 
            this.Text42 = new Stimulsoft.Report.Components.StiText();
            this.Text42.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(8.35, 6.1, 2.8, 0.5);
            this.Text42.Guid = "3bd50697918d404baa7dea94bbc7d9cb";
            this.Text42.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Right;
            this.Text42.Name = "Text42";
            this.Text42.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text42__GetValue);
            this.Text42.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text42.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text42.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text42.Font = new System.Drawing.Font("Arial", 8F);
            this.Text42.Indicator = null;
            this.Text42.Interaction = null;
            this.Text42.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text42.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text42.TextFormat = new Stimulsoft.Report.Components.TextFormats.StiCustomFormatService("#0.00");
            this.Text42.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text43
            // 
            this.Text43 = new Stimulsoft.Report.Components.StiText();
            this.Text43.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(8.35, 6.6, 2.8, 0.5);
            this.Text43.Guid = "8cdf29fd95bd47a684552a83586d2b64";
            this.Text43.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Right;
            this.Text43.Name = "Text43";
            this.Text43.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text43__GetValue);
            this.Text43.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text43.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text43.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text43.Font = new System.Drawing.Font("Arial", 8F);
            this.Text43.Indicator = null;
            this.Text43.Interaction = null;
            this.Text43.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text43.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text43.TextFormat = new Stimulsoft.Report.Components.TextFormats.StiCustomFormatService("#0.00");
            this.Text43.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text44
            // 
            this.Text44 = new Stimulsoft.Report.Components.StiText();
            this.Text44.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(8.35, 7.1, 2.8, 0.5);
            this.Text44.Guid = "bccc6c9148fd4bd9ab6fdf0689d8b4e1";
            this.Text44.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Right;
            this.Text44.Name = "Text44";
            this.Text44.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text44__GetValue);
            this.Text44.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text44.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text44.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text44.Font = new System.Drawing.Font("Arial", 8F);
            this.Text44.Indicator = null;
            this.Text44.Interaction = null;
            this.Text44.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text44.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text44.TextFormat = new Stimulsoft.Report.Components.TextFormats.StiCustomFormatService("#0.00");
            this.Text44.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text45
            // 
            this.Text45 = new Stimulsoft.Report.Components.StiText();
            this.Text45.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(8.35, 3.6, 1, 0.5);
            this.Text45.Guid = "f032cb4d76cb42b2a65a327ad26d56dd";
            this.Text45.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text45.Name = "Text45";
            this.Text45.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text45__GetValue);
            this.Text45.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text45.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text45.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.InitializeComponent2();
        }
        
        public void InitializeComponent2()
        {
            this.Text45.Font = new System.Drawing.Font("Arial", 8F);
            this.Text45.Indicator = null;
            this.Text45.Interaction = null;
            this.Text45.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text45.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text45.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text46
            // 
            this.Text46 = new Stimulsoft.Report.Components.StiText();
            this.Text46.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(8.35, 7.6, 2.8, 0.5);
            this.Text46.Guid = "93b48f17783048e8b753b54a7e602b76";
            this.Text46.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Right;
            this.Text46.Name = "Text46";
            this.Text46.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text46__GetValue);
            this.Text46.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text46.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text46.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text46.Font = new System.Drawing.Font("Arial", 8F);
            this.Text46.Indicator = null;
            this.Text46.Interaction = null;
            this.Text46.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text46.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text46.TextFormat = new Stimulsoft.Report.Components.TextFormats.StiCustomFormatService("#0.00");
            this.Text46.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text47
            // 
            this.Text47 = new Stimulsoft.Report.Components.StiText();
            this.Text47.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(8.35, 8.1, 5.7, 0.5);
            this.Text47.Enabled = false;
            this.Text47.Guid = "62f822afb48c439ea2f0f65bb4546f53";
            this.Text47.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Right;
            this.Text47.Name = "Text47";
            this.Text47.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text47__GetValue);
            this.Text47.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text47.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text47.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text47.Font = new System.Drawing.Font("Arial", 8F);
            this.Text47.Indicator = null;
            this.Text47.Interaction = null;
            this.Text47.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text47.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text47.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text48
            // 
            this.Text48 = new Stimulsoft.Report.Components.StiText();
            this.Text48.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(8.35, 8.6, 5.7, 0.5);
            this.Text48.Enabled = false;
            this.Text48.Guid = "0821dd732aa342db8342060c94981b29";
            this.Text48.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Right;
            this.Text48.Name = "Text48";
            this.Text48.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text48__GetValue);
            this.Text48.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text48.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text48.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text48.Font = new System.Drawing.Font("Arial", 8F);
            this.Text48.Indicator = null;
            this.Text48.Interaction = null;
            this.Text48.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text48.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text48.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text49
            // 
            this.Text49 = new Stimulsoft.Report.Components.StiText();
            this.Text49.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(8.35, 9.1, 5.7, 0.5);
            this.Text49.Guid = "90dfee0d7d044d1b859e48e04272eca5";
            this.Text49.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Right;
            this.Text49.Name = "Text49";
            this.Text49.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text49__GetValue);
            this.Text49.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text49.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text49.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text49.Font = new System.Drawing.Font("Arial", 8F);
            this.Text49.Indicator = null;
            this.Text49.Interaction = null;
            this.Text49.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text49.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text49.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text50
            // 
            this.Text50 = new Stimulsoft.Report.Components.StiText();
            this.Text50.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(8.35, 9.6, 5.7, 0.5);
            this.Text50.Enabled = false;
            this.Text50.Guid = "8ee6be4bc1f044a3bbe63134883f6942";
            this.Text50.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Right;
            this.Text50.Name = "Text50";
            this.Text50.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text50__GetValue);
            this.Text50.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text50.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text50.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text50.Font = new System.Drawing.Font("Arial", 8F);
            this.Text50.Indicator = null;
            this.Text50.Interaction = null;
            this.Text50.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text50.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text50.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text51
            // 
            this.Text51 = new Stimulsoft.Report.Components.StiText();
            this.Text51.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(8.35, 10.1, 5.7, 0.5);
            this.Text51.Enabled = false;
            this.Text51.Guid = "d5df8563667049748f1fd2d4f11cc081";
            this.Text51.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Right;
            this.Text51.Name = "Text51";
            this.Text51.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text51__GetValue);
            this.Text51.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text51.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text51.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text51.Font = new System.Drawing.Font("Arial", 8F);
            this.Text51.Indicator = null;
            this.Text51.Interaction = null;
            this.Text51.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text51.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text51.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text52
            // 
            this.Text52 = new Stimulsoft.Report.Components.StiText();
            this.Text52.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(8.35, 10.6, 5.7, 0.5);
            this.Text52.Enabled = false;
            this.Text52.Guid = "e720973d65124ea5ac20b667797525e3";
            this.Text52.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Right;
            this.Text52.Name = "Text52";
            this.Text52.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text52__GetValue);
            this.Text52.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text52.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text52.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text52.Font = new System.Drawing.Font("Arial", 8F);
            this.Text52.Indicator = null;
            this.Text52.Interaction = null;
            this.Text52.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text52.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text52.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text53
            // 
            this.Text53 = new Stimulsoft.Report.Components.StiText();
            this.Text53.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(8.35, 11.1, 5.7, 0.5);
            this.Text53.Guid = "763441baa48b4cd496c29a74c10567ed";
            this.Text53.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Right;
            this.Text53.Name = "Text53";
            this.Text53.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text53__GetValue);
            this.Text53.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text53.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text53.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text53.Font = new System.Drawing.Font("Arial", 8F);
            this.Text53.Indicator = null;
            this.Text53.Interaction = null;
            this.Text53.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text53.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text53.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text54
            // 
            this.Text54 = new Stimulsoft.Report.Components.StiText();
            this.Text54.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(8.35, 11.6, 5.7, 0.5);
            this.Text54.Enabled = false;
            this.Text54.Guid = "02a2d3663067478aab316890230b84d3";
            this.Text54.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Right;
            this.Text54.Name = "Text54";
            this.Text54.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text54__GetValue);
            this.Text54.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text54.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text54.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text54.Font = new System.Drawing.Font("Arial", 8F);
            this.Text54.Indicator = null;
            this.Text54.Interaction = null;
            this.Text54.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text54.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text54.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text55
            // 
            this.Text55 = new Stimulsoft.Report.Components.StiText();
            this.Text55.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(11.15, 12.1, 2.9, 0.5);
            this.Text55.Guid = "a541a12cfbbf46ddbe105db2c8f617ac";
            this.Text55.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text55.Name = "Text55";
            this.Text55.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text55__GetValue);
            this.Text55.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text55.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text55.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text55.Font = new System.Drawing.Font("Arial", 8F);
            this.Text55.Indicator = null;
            this.Text55.Interaction = null;
            this.Text55.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text55.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text55.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text56
            // 
            this.Text56 = new Stimulsoft.Report.Components.StiText();
            this.Text56.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(8.35, 12.1, 2.8, 0.5);
            this.Text56.Guid = "c94a8226d7f7449cb30f882a89bc6bf4";
            this.Text56.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text56.Name = "Text56";
            this.Text56.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text56__GetValue);
            this.Text56.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text56.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text56.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text56.Font = new System.Drawing.Font("Arial", 8F);
            this.Text56.Indicator = null;
            this.Text56.Interaction = null;
            this.Text56.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text56.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text56.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text57
            // 
            this.Text57 = new Stimulsoft.Report.Components.StiText();
            this.Text57.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(4.9, 3.6, 1.9, 0.5);
            this.Text57.Guid = "4efc0e9097034c50bd906a23ebc6b6a6";
            this.Text57.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text57.Name = "Text57";
            this.Text57.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text57__GetValue);
            this.Text57.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text57.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text57.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text57.Font = new System.Drawing.Font("Arial", 8F);
            this.Text57.Indicator = null;
            this.Text57.Interaction = null;
            this.Text57.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text57.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text57.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text58
            // 
            this.Text58 = new Stimulsoft.Report.Components.StiText();
            this.Text58.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(3.9, 4.1, 2.9, 0.5);
            this.Text58.Guid = "885f9dee7b7846afa8ea83de50877ae0";
            this.Text58.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Right;
            this.Text58.Name = "Text58";
            this.Text58.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text58__GetValue);
            this.Text58.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text58.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text58.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text58.Font = new System.Drawing.Font("Arial", 8F);
            this.Text58.Indicator = null;
            this.Text58.Interaction = null;
            this.Text58.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text58.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text58.TextFormat = new Stimulsoft.Report.Components.TextFormats.StiCustomFormatService("#0.00");
            this.Text58.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text59
            // 
            this.Text59 = new Stimulsoft.Report.Components.StiText();
            this.Text59.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(3.9, 4.6, 2.9, 0.5);
            this.Text59.Guid = "e00dd40e44454627a614d0d4a94fd243";
            this.Text59.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Right;
            this.Text59.Name = "Text59";
            this.Text59.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text59__GetValue);
            this.Text59.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text59.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text59.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text59.Font = new System.Drawing.Font("Arial", 8F);
            this.Text59.Indicator = null;
            this.Text59.Interaction = null;
            this.Text59.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text59.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text59.TextFormat = new Stimulsoft.Report.Components.TextFormats.StiCustomFormatService("#0.00");
            this.Text59.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text60
            // 
            this.Text60 = new Stimulsoft.Report.Components.StiText();
            this.Text60.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(3.9, 5.1, 2.9, 0.5);
            this.Text60.Guid = "b64a0ad208674fa38ba949939218d035";
            this.Text60.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Right;
            this.Text60.Name = "Text60";
            this.Text60.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text60__GetValue);
            this.Text60.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text60.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text60.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text60.Font = new System.Drawing.Font("Arial", 8F);
            this.Text60.Indicator = null;
            this.Text60.Interaction = null;
            this.Text60.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text60.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text60.TextFormat = new Stimulsoft.Report.Components.TextFormats.StiCustomFormatService("#0.00");
            this.Text60.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text61
            // 
            this.Text61 = new Stimulsoft.Report.Components.StiText();
            this.Text61.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(3.9, 5.6, 2.9, 0.5);
            this.Text61.Guid = "9c0546dd434641049ca65b72143c5fb5";
            this.Text61.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Right;
            this.Text61.Name = "Text61";
            this.Text61.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text61__GetValue);
            this.Text61.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text61.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text61.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text61.Font = new System.Drawing.Font("Arial", 8F);
            this.Text61.Indicator = null;
            this.Text61.Interaction = null;
            this.Text61.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text61.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text61.TextFormat = new Stimulsoft.Report.Components.TextFormats.StiCustomFormatService("#0.00");
            this.Text61.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text62
            // 
            this.Text62 = new Stimulsoft.Report.Components.StiText();
            this.Text62.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(3.9, 6.1, 2.9, 0.5);
            this.Text62.Guid = "7967833e6e84478ab92df9c1cff94867";
            this.Text62.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Right;
            this.Text62.Name = "Text62";
            this.Text62.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text62__GetValue);
            this.Text62.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text62.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text62.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text62.Font = new System.Drawing.Font("Arial", 8F);
            this.Text62.Indicator = null;
            this.Text62.Interaction = null;
            this.Text62.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text62.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text62.TextFormat = new Stimulsoft.Report.Components.TextFormats.StiCustomFormatService("#0.00");
            this.Text62.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text63
            // 
            this.Text63 = new Stimulsoft.Report.Components.StiText();
            this.Text63.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(3.9, 6.6, 2.9, 0.5);
            this.Text63.Guid = "93726b1ab801496f865d39f3bd4391b3";
            this.Text63.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Right;
            this.Text63.Name = "Text63";
            this.Text63.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text63__GetValue);
            this.Text63.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text63.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text63.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text63.Font = new System.Drawing.Font("Arial", 8F);
            this.Text63.Indicator = null;
            this.Text63.Interaction = null;
            this.Text63.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text63.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text63.TextFormat = new Stimulsoft.Report.Components.TextFormats.StiCustomFormatService("#0.00");
            this.Text63.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text64
            // 
            this.Text64 = new Stimulsoft.Report.Components.StiText();
            this.Text64.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(3.9, 7.1, 2.9, 0.5);
            this.Text64.Guid = "0c0536936f7d4ff5a1f87a3ce1838503";
            this.Text64.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Right;
            this.Text64.Name = "Text64";
            this.Text64.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text64__GetValue);
            this.Text64.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text64.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text64.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text64.Font = new System.Drawing.Font("Arial", 8F);
            this.Text64.Indicator = null;
            this.Text64.Interaction = null;
            this.Text64.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text64.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text64.TextFormat = new Stimulsoft.Report.Components.TextFormats.StiCustomFormatService("#0.00");
            this.Text64.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text65
            // 
            this.Text65 = new Stimulsoft.Report.Components.StiText();
            this.Text65.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(3.9, 3.6, 1, 0.5);
            this.Text65.Guid = "df2581e79944463a9f43145595b581ff";
            this.Text65.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text65.Name = "Text65";
            this.Text65.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text65__GetValue);
            this.Text65.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text65.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text65.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text65.Font = new System.Drawing.Font("Arial", 8F);
            this.Text65.Indicator = null;
            this.Text65.Interaction = null;
            this.Text65.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text65.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text65.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text66
            // 
            this.Text66 = new Stimulsoft.Report.Components.StiText();
            this.Text66.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(2.1, 3.6, 1.8, 0.5);
            this.Text66.Guid = "4f8b2d0b1dca4ddab8557440512145e1";
            this.Text66.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text66.Name = "Text66";
            this.Text66.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text66__GetValue);
            this.Text66.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text66.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text66.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text66.Font = new System.Drawing.Font("Arial", 8F);
            this.Text66.Indicator = null;
            this.Text66.Interaction = null;
            this.Text66.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text66.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text66.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text67
            // 
            this.Text67 = new Stimulsoft.Report.Components.StiText();
            this.Text67.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(1.1, 4.1, 2.8, 0.5);
            this.Text67.Guid = "4144f5a80ff3482b8ba099057131b23a";
            this.Text67.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Right;
            this.Text67.Name = "Text67";
            this.Text67.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text67__GetValue);
            this.Text67.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text67.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text67.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text67.Font = new System.Drawing.Font("Arial", 8F);
            this.Text67.Indicator = null;
            this.Text67.Interaction = null;
            this.Text67.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text67.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text67.TextFormat = new Stimulsoft.Report.Components.TextFormats.StiCustomFormatService("#0.00");
            this.Text67.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text68
            // 
            this.Text68 = new Stimulsoft.Report.Components.StiText();
            this.Text68.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(1.1, 4.6, 2.8, 0.5);
            this.Text68.Guid = "7f5cfb16fd87495bb0ef1879f036a6d9";
            this.Text68.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Right;
            this.Text68.Name = "Text68";
            this.Text68.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text68__GetValue);
            this.Text68.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text68.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text68.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text68.Font = new System.Drawing.Font("Arial", 8F);
            this.Text68.Indicator = null;
            this.Text68.Interaction = null;
            this.Text68.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text68.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text68.TextFormat = new Stimulsoft.Report.Components.TextFormats.StiCustomFormatService("#0.00");
            this.Text68.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text69
            // 
            this.Text69 = new Stimulsoft.Report.Components.StiText();
            this.Text69.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(1.1, 5.1, 2.8, 0.5);
            this.Text69.Guid = "948a67d9c8514c91b0398c95e86fbe48";
            this.Text69.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Right;
            this.Text69.Name = "Text69";
            this.Text69.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text69__GetValue);
            this.Text69.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text69.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text69.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text69.Font = new System.Drawing.Font("Arial", 8F);
            this.Text69.Indicator = null;
            this.Text69.Interaction = null;
            this.Text69.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text69.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text69.TextFormat = new Stimulsoft.Report.Components.TextFormats.StiCustomFormatService("#0.00");
            this.Text69.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text70
            // 
            this.Text70 = new Stimulsoft.Report.Components.StiText();
            this.Text70.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(1.1, 5.6, 2.8, 0.5);
            this.Text70.Guid = "799df0a33e1242c9aeb82533cc085c32";
            this.Text70.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Right;
            this.Text70.Name = "Text70";
            this.Text70.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text70__GetValue);
            this.Text70.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text70.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text70.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text70.Font = new System.Drawing.Font("Arial", 8F);
            this.Text70.Indicator = null;
            this.Text70.Interaction = null;
            this.Text70.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text70.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text70.TextFormat = new Stimulsoft.Report.Components.TextFormats.StiCustomFormatService("#0.00");
            this.Text70.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text71
            // 
            this.Text71 = new Stimulsoft.Report.Components.StiText();
            this.Text71.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(1.1, 6.1, 2.8, 0.5);
            this.Text71.Guid = "5ec7acc5f1f649f88a0341dea3f56ed9";
            this.Text71.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Right;
            this.Text71.Name = "Text71";
            this.Text71.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text71__GetValue);
            this.Text71.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text71.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text71.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text71.Font = new System.Drawing.Font("Arial", 8F);
            this.Text71.Indicator = null;
            this.Text71.Interaction = null;
            this.Text71.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text71.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text71.TextFormat = new Stimulsoft.Report.Components.TextFormats.StiCustomFormatService("#0.00");
            this.Text71.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text72
            // 
            this.Text72 = new Stimulsoft.Report.Components.StiText();
            this.Text72.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(1.1, 6.6, 2.8, 0.5);
            this.Text72.Guid = "01cbca78b60240dc9a7ca29c9443863a";
            this.Text72.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Right;
            this.Text72.Name = "Text72";
            this.Text72.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text72__GetValue);
            this.Text72.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text72.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text72.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text72.Font = new System.Drawing.Font("Arial", 8F);
            this.Text72.Indicator = null;
            this.Text72.Interaction = null;
            this.Text72.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text72.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text72.TextFormat = new Stimulsoft.Report.Components.TextFormats.StiCustomFormatService("#0.00");
            this.Text72.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text73
            // 
            this.Text73 = new Stimulsoft.Report.Components.StiText();
            this.Text73.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(1.1, 7.1, 2.8, 0.5);
            this.Text73.Guid = "69a5bb7f63bc47fbbda0791210bac176";
            this.Text73.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Right;
            this.Text73.Name = "Text73";
            this.Text73.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text73__GetValue);
            this.Text73.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text73.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text73.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text73.Font = new System.Drawing.Font("Arial", 8F);
            this.Text73.Indicator = null;
            this.Text73.Interaction = null;
            this.Text73.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text73.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text73.TextFormat = new Stimulsoft.Report.Components.TextFormats.StiCustomFormatService("#0.00");
            this.Text73.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text74
            // 
            this.Text74 = new Stimulsoft.Report.Components.StiText();
            this.Text74.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(1.1, 3.6, 1, 0.5);
            this.Text74.Guid = "543735a3ac8e442d92f0384f3e04ccda";
            this.Text74.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text74.Name = "Text74";
            this.Text74.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text74__GetValue);
            this.Text74.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text74.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text74.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text74.Font = new System.Drawing.Font("Arial", 8F);
            this.Text74.Indicator = null;
            this.Text74.Interaction = null;
            this.Text74.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text74.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text74.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text75
            // 
            this.Text75 = new Stimulsoft.Report.Components.StiText();
            this.Text75.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(1.1, 7.6, 2.8, 0.5);
            this.Text75.Guid = "2f10b60dbd0e411fbc77a69b856083a2";
            this.Text75.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Right;
            this.Text75.Name = "Text75";
            this.Text75.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text75__GetValue);
            this.Text75.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text75.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text75.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text75.Font = new System.Drawing.Font("Arial", 8F);
            this.Text75.Indicator = null;
            this.Text75.Interaction = null;
            this.Text75.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text75.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text75.TextFormat = new Stimulsoft.Report.Components.TextFormats.StiCustomFormatService("#0.00");
            this.Text75.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text76
            // 
            this.Text76 = new Stimulsoft.Report.Components.StiText();
            this.Text76.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(1.1, 8.1, 5.7, 0.5);
            this.Text76.Enabled = false;
            this.Text76.Guid = "53124ffd41d34dafba3f8cbbeaa6674c";
            this.Text76.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Right;
            this.Text76.Name = "Text76";
            this.Text76.NullValue = "Empty";
            this.Text76.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text76__GetValue);
            this.Text76.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text76.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text76.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text76.Font = new System.Drawing.Font("Arial", 8F);
            this.Text76.Indicator = null;
            this.Text76.Interaction = null;
            this.Text76.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text76.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text76.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text77
            // 
            this.Text77 = new Stimulsoft.Report.Components.StiText();
            this.Text77.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(1.1, 8.6, 5.7, 0.5);
            this.Text77.Enabled = false;
            this.Text77.Guid = "ed6fc04887104eb58176ad4830d69cd6";
            this.Text77.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Right;
            this.Text77.Name = "Text77";
            this.Text77.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text77__GetValue);
            this.Text77.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text77.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text77.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text77.Font = new System.Drawing.Font("Arial", 8F);
            this.Text77.Indicator = null;
            this.Text77.Interaction = null;
            this.Text77.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text77.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text77.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text78
            // 
            this.Text78 = new Stimulsoft.Report.Components.StiText();
            this.Text78.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(1.1, 9.1, 5.7, 0.5);
            this.Text78.Guid = "8b999907751740cfa347d9c770ff10c8";
            this.Text78.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Right;
            this.Text78.Name = "Text78";
            this.Text78.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text78__GetValue);
            this.Text78.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text78.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text78.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text78.Font = new System.Drawing.Font("Arial", 8F);
            this.Text78.Indicator = null;
            this.Text78.Interaction = null;
            this.Text78.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text78.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text78.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text79
            // 
            this.Text79 = new Stimulsoft.Report.Components.StiText();
            this.Text79.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(1.1, 9.6, 5.7, 0.5);
            this.Text79.Enabled = false;
            this.Text79.Guid = "84319b1bd9294e0d952c1b49582bbd2f";
            this.Text79.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Right;
            this.Text79.Name = "Text79";
            this.Text79.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text79__GetValue);
            this.Text79.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text79.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text79.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text79.Font = new System.Drawing.Font("Arial", 8F);
            this.Text79.Indicator = null;
            this.Text79.Interaction = null;
            this.Text79.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text79.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text79.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text80
            // 
            this.Text80 = new Stimulsoft.Report.Components.StiText();
            this.Text80.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(1.1, 10.1, 5.7, 0.5);
            this.Text80.Enabled = false;
            this.Text80.Guid = "0c4a7d42e2904480b63e048f75fa7c8c";
            this.Text80.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Right;
            this.Text80.Name = "Text80";
            this.Text80.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text80__GetValue);
            this.Text80.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text80.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text80.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text80.Font = new System.Drawing.Font("Arial", 8F);
            this.Text80.Indicator = null;
            this.Text80.Interaction = null;
            this.Text80.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text80.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text80.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text81
            // 
            this.Text81 = new Stimulsoft.Report.Components.StiText();
            this.Text81.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(1.1, 10.6, 5.7, 0.5);
            this.Text81.Enabled = false;
            this.Text81.Guid = "3c0fb2b414d941119421047446dbf799";
            this.Text81.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Right;
            this.Text81.Name = "Text81";
            this.Text81.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text81__GetValue);
            this.Text81.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text81.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text81.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text81.Font = new System.Drawing.Font("Arial", 8F);
            this.Text81.Indicator = null;
            this.Text81.Interaction = null;
            this.Text81.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text81.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text81.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text82
            // 
            this.Text82 = new Stimulsoft.Report.Components.StiText();
            this.Text82.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(1.1, 11.1, 5.7, 0.5);
            this.Text82.Guid = "a730483422e94b5d9476bd22e399ea73";
            this.Text82.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Right;
            this.Text82.Name = "Text82";
            this.Text82.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text82__GetValue);
            this.Text82.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text82.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text82.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text82.Font = new System.Drawing.Font("Arial", 8F);
            this.Text82.Indicator = null;
            this.Text82.Interaction = null;
            this.Text82.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text82.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text82.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text83
            // 
            this.Text83 = new Stimulsoft.Report.Components.StiText();
            this.Text83.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(1.1, 11.6, 5.7, 0.5);
            this.Text83.Enabled = false;
            this.Text83.Guid = "3c2c3347969147cc84a3c9d16c7ac316";
            this.Text83.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Right;
            this.Text83.Name = "Text83";
            this.Text83.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text83__GetValue);
            this.Text83.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text83.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text83.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text83.Font = new System.Drawing.Font("Arial", 8F);
            this.Text83.Indicator = null;
            this.Text83.Interaction = null;
            this.Text83.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text83.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text83.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text84
            // 
            this.Text84 = new Stimulsoft.Report.Components.StiText();
            this.Text84.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(3.9, 12.1, 2.9, 0.5);
            this.Text84.Guid = "f669fa1fe59b4141b59a10077469ec4e";
            this.Text84.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text84.Name = "Text84";
            this.Text84.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text84__GetValue);
            this.Text84.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text84.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text84.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text84.Font = new System.Drawing.Font("Arial", 8F);
            this.Text84.Indicator = null;
            this.Text84.Interaction = null;
            this.Text84.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text84.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text84.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text85
            // 
            this.Text85 = new Stimulsoft.Report.Components.StiText();
            this.Text85.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(1.1, 12.1, 2.8, 0.5);
            this.Text85.Guid = "dbd700a54b474529897a9369f05cafe2";
            this.Text85.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text85.Name = "Text85";
            this.Text85.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text85__GetValue);
            this.Text85.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text85.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text85.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text85.Font = new System.Drawing.Font("Arial", 8F);
            this.Text85.Indicator = null;
            this.Text85.Interaction = null;
            this.Text85.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text85.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text85.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text86
            // 
            this.Text86 = new Stimulsoft.Report.Components.StiText();
            this.Text86.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(8.35, 1.8, 2.8, 0.5);
            this.Text86.Name = "Text86";
            this.Text86.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text86__GetValue);
            this.Text86.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text86.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text86.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text86.Font = new System.Drawing.Font("Arial", 8F);
            this.Text86.Guid = null;
            this.Text86.Indicator = null;
            this.Text86.Interaction = null;
            this.Text86.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text86.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text86.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text87
            // 
            this.Text87 = new Stimulsoft.Report.Components.StiText();
            this.Text87.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(11.15, 1.8, 2.9, 0.5);
            this.Text87.Enabled = false;
            this.Text87.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Right;
            this.Text87.Name = "Text87";
            this.Text87.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text87__GetValue);
            this.Text87.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text87.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text87.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text87.Font = new System.Drawing.Font("Arial", 8F);
            this.Text87.Guid = null;
            this.Text87.Indicator = null;
            this.Text87.Interaction = null;
            this.Text87.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text87.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text87.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text88
            // 
            this.Text88 = new Stimulsoft.Report.Components.StiText();
            this.Text88.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(8.35, 2.3, 2.8, 0.5);
            this.Text88.Name = "Text88";
            this.Text88.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text88__GetValue);
            this.Text88.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text88.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text88.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text88.Font = new System.Drawing.Font("Arial", 8F);
            this.Text88.Guid = null;
            this.Text88.Indicator = null;
            this.Text88.Interaction = null;
            this.Text88.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text88.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text88.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text89
            // 
            this.Text89 = new Stimulsoft.Report.Components.StiText();
            this.Text89.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(11.15, 2.3, 2.9, 0.5);
            this.Text89.Enabled = false;
            this.Text89.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Right;
            this.Text89.Name = "Text89";
            this.Text89.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text89__GetValue);
            this.Text89.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text89.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text89.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text89.Font = new System.Drawing.Font("Arial", 8F);
            this.Text89.Guid = null;
            this.Text89.Indicator = null;
            this.Text89.Interaction = null;
            this.Text89.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text89.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text89.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text90
            // 
            this.Text90 = new Stimulsoft.Report.Components.StiText();
            this.Text90.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(8.35, 2.8, 2.8, 0.5);
            this.Text90.Name = "Text90";
            this.Text90.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text90__GetValue);
            this.Text90.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text90.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text90.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text90.Font = new System.Drawing.Font("Arial", 8F);
            this.Text90.Guid = null;
            this.Text90.Indicator = null;
            this.Text90.Interaction = null;
            this.Text90.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text90.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text90.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text91
            // 
            this.Text91 = new Stimulsoft.Report.Components.StiText();
            this.Text91.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(11.15, 2.8, 2.9, 0.5);
            this.Text91.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Right;
            this.Text91.Name = "Text91";
            this.Text91.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text91__GetValue);
            this.Text91.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text91.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text91.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text91.Font = new System.Drawing.Font("Arial", 8F);
            this.Text91.Guid = null;
            this.Text91.Indicator = null;
            this.Text91.Interaction = null;
            this.Text91.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text91.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text91.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text92
            // 
            this.Text92 = new Stimulsoft.Report.Components.StiText();
            this.Text92.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(1.1, 1.8, 2.8, 0.5);
            this.Text92.Guid = "0381584e4c5a4a3db50184f405c4d8c1";
            this.Text92.Name = "Text92";
            this.Text92.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text92__GetValue);
            this.Text92.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text92.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text92.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text92.Font = new System.Drawing.Font("Arial", 8F);
            this.Text92.Indicator = null;
            this.Text92.Interaction = null;
            this.Text92.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text92.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text92.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text93
            // 
            this.Text93 = new Stimulsoft.Report.Components.StiText();
            this.Text93.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(3.9, 1.8, 2.9, 0.5);
            this.Text93.Enabled = false;
            this.Text93.Guid = "ce113f7bbacf43649839002299608d74";
            this.Text93.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Right;
            this.Text93.Name = "Text93";
            this.Text93.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text93__GetValue);
            this.Text93.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text93.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text93.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text93.Font = new System.Drawing.Font("Arial", 8F);
            this.Text93.Indicator = null;
            this.Text93.Interaction = null;
            this.Text93.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text93.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text93.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text94
            // 
            this.Text94 = new Stimulsoft.Report.Components.StiText();
            this.Text94.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(1.1, 2.3, 2.8, 0.5);
            this.Text94.Guid = "2fe04678b7a245efbf578375d6745652";
            this.Text94.Name = "Text94";
            this.Text94.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text94__GetValue);
            this.Text94.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text94.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text94.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text94.Font = new System.Drawing.Font("Arial", 8F);
            this.Text94.Indicator = null;
            this.Text94.Interaction = null;
            this.Text94.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text94.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text94.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text95
            // 
            this.Text95 = new Stimulsoft.Report.Components.StiText();
            this.Text95.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(3.9, 2.3, 2.9, 0.5);
            this.Text95.Enabled = false;
            this.Text95.Guid = "f1d2634ff6b6423f828ff4be5cc7608b";
            this.Text95.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Right;
            this.Text95.Name = "Text95";
            this.Text95.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text95__GetValue);
            this.Text95.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text95.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text95.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text95.Font = new System.Drawing.Font("Arial", 8F);
            this.Text95.Indicator = null;
            this.Text95.Interaction = null;
            this.Text95.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text95.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text95.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text96
            // 
            this.Text96 = new Stimulsoft.Report.Components.StiText();
            this.Text96.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(1.1, 2.8, 2.8, 0.5);
            this.Text96.Guid = "61cdb3fa66e44368b09965c93c378503";
            this.Text96.Name = "Text96";
            this.Text96.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text96__GetValue);
            this.Text96.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text96.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text96.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text96.Font = new System.Drawing.Font("Arial", 8F);
            this.Text96.Indicator = null;
            this.Text96.Interaction = null;
            this.Text96.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text96.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text96.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text97
            // 
            this.Text97 = new Stimulsoft.Report.Components.StiText();
            this.Text97.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(3.9, 2.8, 2.9, 0.5);
            this.Text97.Guid = "24aeacc100c94536a527947402b2bc98";
            this.Text97.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Right;
            this.Text97.Name = "Text97";
            this.Text97.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text97__GetValue);
            this.Text97.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text97.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text97.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text97.Font = new System.Drawing.Font("Arial", 8F);
            this.Text97.Indicator = null;
            this.Text97.Interaction = null;
            this.Text97.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text97.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text97.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text98
            // 
            this.Text98 = new Stimulsoft.Report.Components.StiText();
            this.Text98.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(18.6, 11.8, 1.6, 0.5);
            this.Text98.Guid = "9b0b8193a2cb4bd4ade2fae386052911";
            this.Text98.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text98.Name = "Text98";
            this.Text98.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text98__GetValue);
            this.Text98.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text98.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text98.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text98.Font = new System.Drawing.Font("Arial", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
            this.Text98.Indicator = null;
            this.Text98.Interaction = null;
            this.Text98.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text98.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text98.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text99
            // 
            this.Text99 = new Stimulsoft.Report.Components.StiText();
            this.Text99.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(18.6, 7.4, 1.6, 0.5);
            this.Text99.Guid = "bb89b98cb1cd462ebbc3e1b13622227c";
            this.Text99.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text99.Name = "Text99";
            this.Text99.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text99__GetValue);
            this.Text99.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text99.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text99.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text99.Font = new System.Drawing.Font("Arial", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
            this.InitializeComponent3();
        }
        
        public void InitializeComponent3()
        {
            this.Text99.Indicator = null;
            this.Text99.Interaction = null;
            this.Text99.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text99.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text99.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text100
            // 
            this.Text100 = new Stimulsoft.Report.Components.StiText();
            this.Text100.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(18.6, 2.9, 1.6, 0.5);
            this.Text100.Guid = "cbefc9e7e1634aa19e9fe143ebd83362";
            this.Text100.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text100.Name = "Text100";
            this.Text100.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text100__GetValue);
            this.Text100.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text100.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text100.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text100.Font = new System.Drawing.Font("Arial", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
            this.Text100.Indicator = null;
            this.Text100.Interaction = null;
            this.Text100.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text100.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text100.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            this.PageHeaderBand1.Guid = null;
            this.PageHeaderBand1.Interaction = null;
            this.Page1.ExcelSheetValue = null;
            this.Page1.Interaction = null;
            this.Page1.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Page1_Watermark = new Stimulsoft.Report.Components.StiWatermark();
            this.Page1_Watermark.Font = new System.Drawing.Font("Arial", 100F);
            this.Page1_Watermark.Image = null;
            this.Page1_Watermark.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.FromArgb(50, 0, 0, 0));
            this.吉林省医疗机构门诊收费专用票据_PrinterSettings = new Stimulsoft.Report.Print.StiPrinterSettings();
            this.PrinterSettings = this.吉林省医疗机构门诊收费专用票据_PrinterSettings;
            this.Page1.Report = this;
            this.Page1.Watermark = this.Page1_Watermark;
            this.PageHeaderBand1.Page = this.Page1;
            this.PageHeaderBand1.Parent = this.Page1;
            this.Text2.Page = this.Page1;
            this.Text2.Parent = this.PageHeaderBand1;
            this.Text3.Page = this.Page1;
            this.Text3.Parent = this.PageHeaderBand1;
            this.Text4.Page = this.Page1;
            this.Text4.Parent = this.PageHeaderBand1;
            this.Text5.Page = this.Page1;
            this.Text5.Parent = this.PageHeaderBand1;
            this.Text6.Page = this.Page1;
            this.Text6.Parent = this.PageHeaderBand1;
            this.Text7.Page = this.Page1;
            this.Text7.Parent = this.PageHeaderBand1;
            this.Text8.Page = this.Page1;
            this.Text8.Parent = this.PageHeaderBand1;
            this.Text9.Page = this.Page1;
            this.Text9.Parent = this.PageHeaderBand1;
            this.Text10.Page = this.Page1;
            this.Text10.Parent = this.PageHeaderBand1;
            this.Text11.Page = this.Page1;
            this.Text11.Parent = this.PageHeaderBand1;
            this.Text12.Page = this.Page1;
            this.Text12.Parent = this.PageHeaderBand1;
            this.Text13.Page = this.Page1;
            this.Text13.Parent = this.PageHeaderBand1;
            this.Text14.Page = this.Page1;
            this.Text14.Parent = this.PageHeaderBand1;
            this.Text15.Page = this.Page1;
            this.Text15.Parent = this.PageHeaderBand1;
            this.Text16.Page = this.Page1;
            this.Text16.Parent = this.PageHeaderBand1;
            this.Text1.Page = this.Page1;
            this.Text1.Parent = this.PageHeaderBand1;
            this.Text17.Page = this.Page1;
            this.Text17.Parent = this.PageHeaderBand1;
            this.Text18.Page = this.Page1;
            this.Text18.Parent = this.PageHeaderBand1;
            this.Text19.Page = this.Page1;
            this.Text19.Parent = this.PageHeaderBand1;
            this.Text20.Page = this.Page1;
            this.Text20.Parent = this.PageHeaderBand1;
            this.Text21.Page = this.Page1;
            this.Text21.Parent = this.PageHeaderBand1;
            this.Text22.Page = this.Page1;
            this.Text22.Parent = this.PageHeaderBand1;
            this.Text23.Page = this.Page1;
            this.Text23.Parent = this.PageHeaderBand1;
            this.Text24.Page = this.Page1;
            this.Text24.Parent = this.PageHeaderBand1;
            this.Text25.Page = this.Page1;
            this.Text25.Parent = this.PageHeaderBand1;
            this.Text26.Page = this.Page1;
            this.Text26.Parent = this.PageHeaderBand1;
            this.Text27.Page = this.Page1;
            this.Text27.Parent = this.PageHeaderBand1;
            this.Text28.Page = this.Page1;
            this.Text28.Parent = this.PageHeaderBand1;
            this.Text29.Page = this.Page1;
            this.Text29.Parent = this.PageHeaderBand1;
            this.Text30.Page = this.Page1;
            this.Text30.Parent = this.PageHeaderBand1;
            this.Text31.Page = this.Page1;
            this.Text31.Parent = this.PageHeaderBand1;
            this.Text32.Page = this.Page1;
            this.Text32.Parent = this.PageHeaderBand1;
            this.Text33.Page = this.Page1;
            this.Text33.Parent = this.PageHeaderBand1;
            this.Text34.Page = this.Page1;
            this.Text34.Parent = this.PageHeaderBand1;
            this.Text35.Page = this.Page1;
            this.Text35.Parent = this.PageHeaderBand1;
            this.Text36.Page = this.Page1;
            this.Text36.Parent = this.PageHeaderBand1;
            this.Text37.Page = this.Page1;
            this.Text37.Parent = this.PageHeaderBand1;
            this.Text38.Page = this.Page1;
            this.Text38.Parent = this.PageHeaderBand1;
            this.Text39.Page = this.Page1;
            this.Text39.Parent = this.PageHeaderBand1;
            this.Text40.Page = this.Page1;
            this.Text40.Parent = this.PageHeaderBand1;
            this.Text41.Page = this.Page1;
            this.Text41.Parent = this.PageHeaderBand1;
            this.Text42.Page = this.Page1;
            this.Text42.Parent = this.PageHeaderBand1;
            this.Text43.Page = this.Page1;
            this.Text43.Parent = this.PageHeaderBand1;
            this.Text44.Page = this.Page1;
            this.Text44.Parent = this.PageHeaderBand1;
            this.Text45.Page = this.Page1;
            this.Text45.Parent = this.PageHeaderBand1;
            this.Text46.Page = this.Page1;
            this.Text46.Parent = this.PageHeaderBand1;
            this.Text47.Page = this.Page1;
            this.Text47.Parent = this.PageHeaderBand1;
            this.Text48.Page = this.Page1;
            this.Text48.Parent = this.PageHeaderBand1;
            this.Text49.Page = this.Page1;
            this.Text49.Parent = this.PageHeaderBand1;
            this.Text50.Page = this.Page1;
            this.Text50.Parent = this.PageHeaderBand1;
            this.Text51.Page = this.Page1;
            this.Text51.Parent = this.PageHeaderBand1;
            this.Text52.Page = this.Page1;
            this.Text52.Parent = this.PageHeaderBand1;
            this.Text53.Page = this.Page1;
            this.Text53.Parent = this.PageHeaderBand1;
            this.Text54.Page = this.Page1;
            this.Text54.Parent = this.PageHeaderBand1;
            this.Text55.Page = this.Page1;
            this.Text55.Parent = this.PageHeaderBand1;
            this.Text56.Page = this.Page1;
            this.Text56.Parent = this.PageHeaderBand1;
            this.Text57.Page = this.Page1;
            this.Text57.Parent = this.PageHeaderBand1;
            this.Text58.Page = this.Page1;
            this.Text58.Parent = this.PageHeaderBand1;
            this.Text59.Page = this.Page1;
            this.Text59.Parent = this.PageHeaderBand1;
            this.Text60.Page = this.Page1;
            this.Text60.Parent = this.PageHeaderBand1;
            this.Text61.Page = this.Page1;
            this.Text61.Parent = this.PageHeaderBand1;
            this.Text62.Page = this.Page1;
            this.Text62.Parent = this.PageHeaderBand1;
            this.Text63.Page = this.Page1;
            this.Text63.Parent = this.PageHeaderBand1;
            this.Text64.Page = this.Page1;
            this.Text64.Parent = this.PageHeaderBand1;
            this.Text65.Page = this.Page1;
            this.Text65.Parent = this.PageHeaderBand1;
            this.Text66.Page = this.Page1;
            this.Text66.Parent = this.PageHeaderBand1;
            this.Text67.Page = this.Page1;
            this.Text67.Parent = this.PageHeaderBand1;
            this.Text68.Page = this.Page1;
            this.Text68.Parent = this.PageHeaderBand1;
            this.Text69.Page = this.Page1;
            this.Text69.Parent = this.PageHeaderBand1;
            this.Text70.Page = this.Page1;
            this.Text70.Parent = this.PageHeaderBand1;
            this.Text71.Page = this.Page1;
            this.Text71.Parent = this.PageHeaderBand1;
            this.Text72.Page = this.Page1;
            this.Text72.Parent = this.PageHeaderBand1;
            this.Text73.Page = this.Page1;
            this.Text73.Parent = this.PageHeaderBand1;
            this.Text74.Page = this.Page1;
            this.Text74.Parent = this.PageHeaderBand1;
            this.Text75.Page = this.Page1;
            this.Text75.Parent = this.PageHeaderBand1;
            this.Text76.Page = this.Page1;
            this.Text76.Parent = this.PageHeaderBand1;
            this.Text77.Page = this.Page1;
            this.Text77.Parent = this.PageHeaderBand1;
            this.Text78.Page = this.Page1;
            this.Text78.Parent = this.PageHeaderBand1;
            this.Text79.Page = this.Page1;
            this.Text79.Parent = this.PageHeaderBand1;
            this.Text80.Page = this.Page1;
            this.Text80.Parent = this.PageHeaderBand1;
            this.Text81.Page = this.Page1;
            this.Text81.Parent = this.PageHeaderBand1;
            this.Text82.Page = this.Page1;
            this.Text82.Parent = this.PageHeaderBand1;
            this.Text83.Page = this.Page1;
            this.Text83.Parent = this.PageHeaderBand1;
            this.Text84.Page = this.Page1;
            this.Text84.Parent = this.PageHeaderBand1;
            this.Text85.Page = this.Page1;
            this.Text85.Parent = this.PageHeaderBand1;
            this.Text86.Page = this.Page1;
            this.Text86.Parent = this.PageHeaderBand1;
            this.Text87.Page = this.Page1;
            this.Text87.Parent = this.PageHeaderBand1;
            this.Text88.Page = this.Page1;
            this.Text88.Parent = this.PageHeaderBand1;
            this.Text89.Page = this.Page1;
            this.Text89.Parent = this.PageHeaderBand1;
            this.Text90.Page = this.Page1;
            this.Text90.Parent = this.PageHeaderBand1;
            this.Text91.Page = this.Page1;
            this.Text91.Parent = this.PageHeaderBand1;
            this.Text92.Page = this.Page1;
            this.Text92.Parent = this.PageHeaderBand1;
            this.Text93.Page = this.Page1;
            this.Text93.Parent = this.PageHeaderBand1;
            this.Text94.Page = this.Page1;
            this.Text94.Parent = this.PageHeaderBand1;
            this.Text95.Page = this.Page1;
            this.Text95.Parent = this.PageHeaderBand1;
            this.Text96.Page = this.Page1;
            this.Text96.Parent = this.PageHeaderBand1;
            this.Text97.Page = this.Page1;
            this.Text97.Parent = this.PageHeaderBand1;
            this.Text98.Page = this.Page1;
            this.Text98.Parent = this.PageHeaderBand1;
            this.Text99.Page = this.Page1;
            this.Text99.Parent = this.PageHeaderBand1;
            this.Text100.Page = this.Page1;
            this.Text100.Parent = this.PageHeaderBand1;
            // 
            // Add to PageHeaderBand1.Components
            // 
            this.PageHeaderBand1.Components.Clear();
            this.PageHeaderBand1.Components.AddRange(new Stimulsoft.Report.Components.StiComponent[] {
                        this.Text2,
                        this.Text3,
                        this.Text4,
                        this.Text5,
                        this.Text6,
                        this.Text7,
                        this.Text8,
                        this.Text9,
                        this.Text10,
                        this.Text11,
                        this.Text12,
                        this.Text13,
                        this.Text14,
                        this.Text15,
                        this.Text16,
                        this.Text1,
                        this.Text17,
                        this.Text18,
                        this.Text19,
                        this.Text20,
                        this.Text21,
                        this.Text22,
                        this.Text23,
                        this.Text24,
                        this.Text25,
                        this.Text26,
                        this.Text27,
                        this.Text28,
                        this.Text29,
                        this.Text30,
                        this.Text31,
                        this.Text32,
                        this.Text33,
                        this.Text34,
                        this.Text35,
                        this.Text36,
                        this.Text37,
                        this.Text38,
                        this.Text39,
                        this.Text40,
                        this.Text41,
                        this.Text42,
                        this.Text43,
                        this.Text44,
                        this.Text45,
                        this.Text46,
                        this.Text47,
                        this.Text48,
                        this.Text49,
                        this.Text50,
                        this.Text51,
                        this.Text52,
                        this.Text53,
                        this.Text54,
                        this.Text55,
                        this.Text56,
                        this.Text57,
                        this.Text58,
                        this.Text59,
                        this.Text60,
                        this.Text61,
                        this.Text62,
                        this.Text63,
                        this.Text64,
                        this.Text65,
                        this.Text66,
                        this.Text67,
                        this.Text68,
                        this.Text69,
                        this.Text70,
                        this.Text71,
                        this.Text72,
                        this.Text73,
                        this.Text74,
                        this.Text75,
                        this.Text76,
                        this.Text77,
                        this.Text78,
                        this.Text79,
                        this.Text80,
                        this.Text81,
                        this.Text82,
                        this.Text83,
                        this.Text84,
                        this.Text85,
                        this.Text86,
                        this.Text87,
                        this.Text88,
                        this.Text89,
                        this.Text90,
                        this.Text91,
                        this.Text92,
                        this.Text93,
                        this.Text94,
                        this.Text95,
                        this.Text96,
                        this.Text97,
                        this.Text98,
                        this.Text99,
                        this.Text100});
            // 
            // Add to Page1.Components
            // 
            this.Page1.Components.Clear();
            this.Page1.Components.AddRange(new Stimulsoft.Report.Components.StiComponent[] {
                        this.PageHeaderBand1});
            // 
            // Add to Pages
            // 
            this.Pages.Clear();
            this.Pages.AddRange(new Stimulsoft.Report.Components.StiPage[] {
                        this.Page1});
        }
        #endregion StiReport Designer generated code - do not modify
    }
}
</Script>
  <ScriptLanguage>CSharp</ScriptLanguage>
  <Styles isList="true" count="0" />
</StiSerializer>