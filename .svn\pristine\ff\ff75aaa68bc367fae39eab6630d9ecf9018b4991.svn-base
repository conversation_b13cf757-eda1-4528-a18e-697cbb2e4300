﻿/**  版本信息模板在安装目录下，可自行修改。
* B_Emr_Bl.cs
*
* 功 能： N/A
* 类 名： B_Emr_Bl
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2016/8/22 3:39:01   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
using System.Data;
using System.Collections.Generic;

using ModelOld;
namespace BLLOld
{
	/// <summary>
	/// B_Emr_Bl
	/// </summary>
	public partial class B_Emr_Bl
	{
		private readonly DAL.D_Emr_Bl dal=new DAL.D_Emr_Bl();
		public B_Emr_Bl()
		{}
		#region  BasicMethod
		/// <summary>
		/// 是否存在该记录
		/// </summary>
		public bool Exists(long id)
		{
			return dal.Exists(id);
		}

        
		/// <summary>
		/// 增加一条数据
		/// </summary>
        public bool Add(ref ModelOld.M_Emr_Bl model)
		{
            return dal.Add(ref model);
		}
        public int GetOneManMaxId(string strWhere)
        {
            return dal.GetOneManMaxId(strWhere);
        }
		/// <summary>
		/// 更新一条数据
		/// </summary>
		public bool Update(ModelOld.M_Emr_Bl model)
		{
			return dal.Update(model);
		}

		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool Delete(long id)
		{
			
			return dal.Delete(id);
		}
		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool DeleteList(string idlist )
		{
			return dal.DeleteList(idlist );
		}

		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public ModelOld.M_Emr_Bl GetModel(long id)
		{
			
			return dal.GetModel(id);
		}

		/// <summary>
		/// 得到一个对象实体，从缓存中
		/// </summary>
        //public ModelOld.M_Emr_Bl GetModelByCache(long id)
        //{
			
        //    string CacheKey = "M_Emr_BlModel-" + id;
        //    object objModel = Common.DataCache.GetCache(CacheKey);
        //    if (objModel == null)
        //    {
        //        try
        //        {
        //            objModel = dal.GetModel(id);
        //            if (objModel != null)
        //            {
        //                int ModelCache = Common.ConfigHelper.GetConfigInt("ModelCache");
        //                Common.DataCache.SetCache(CacheKey, objModel, DateTime.Now.AddMinutes(ModelCache), TimeSpan.Zero);
        //            }
        //        }
        //        catch{}
        //    }
        //    return (ModelOld.M_Emr_Bl)objModel;
        //}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetList(string strWhere)
		{
			return dal.GetList(strWhere);
		}

        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetListSkMb(string strWhere)
        {
            return dal.GetListSkMb(strWhere);
        }
        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetListnode(string strWhere)
        {
            return dal.GetListnode(strWhere);
        }

        public DataSet GetListblandmb(string strWhere)
        {
            return dal.GetListblandmb(strWhere);
        }
		/// <summary>
		/// 获得前几行数据
		/// </summary>
		public DataSet GetList(int Top,string strWhere,string filedOrder)
		{
			return dal.GetList(Top,strWhere,filedOrder);
		}
		/// <summary>
		/// 获得数据列表
		/// </summary>
		public List<ModelOld.M_Emr_Bl> GetModelList(string strWhere)
		{
			DataSet ds = dal.GetListnode(strWhere);
			return DataTableToList(ds.Tables[0]);
		}
		/// <summary>
		/// 获得数据列表
		/// </summary>
		public List<ModelOld.M_Emr_Bl> DataTableToList(DataTable dt)
		{
			List<ModelOld.M_Emr_Bl> modelList = new List<ModelOld.M_Emr_Bl>();
			int rowsCount = dt.Rows.Count;
			if (rowsCount > 0)
			{
				ModelOld.M_Emr_Bl model;
				for (int n = 0; n < rowsCount; n++)
				{
					model = dal.DataRowToModel(dt.Rows[n]);
					if (model != null)
					{
						modelList.Add(model);
					}
				}
			}
			return modelList;
		}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetAllList()
		{
			return GetList("");
		}

		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public int GetRecordCount(string strWhere)
		{
			return dal.GetRecordCount(strWhere);
		}
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
		{
			return dal.GetListByPage( strWhere,  orderby,  startIndex,  endIndex);
		}
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		//public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		//{
			//return dal.GetList(PageSize,PageIndex,strWhere);
		//}

		#endregion  BasicMethod
		#region  ExtensionMethod

		#endregion  ExtensionMethod
	}
}

