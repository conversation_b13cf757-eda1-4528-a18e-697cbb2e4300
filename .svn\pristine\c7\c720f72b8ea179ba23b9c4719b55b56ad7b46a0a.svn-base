﻿<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<StiSerializer version="1.02" type="Net" application="StiReport">
  <Dictionary Ref="1" type="Dictionary" isKey="true">
    <BusinessObjects isList="true" count="0" />
    <Databases isList="true" count="0" />
    <DataSources isList="true" count="0" />
    <Relations isList="true" count="0" />
    <Report isRef="0" />
    <Variables isList="true" count="12">
      <value>,打印时间,打印时间,System.String,,False,False</value>
      <value>,标题,标题,System.String,,False,False</value>
      <value>,月结时间,月结时间,System.String,,False,False</value>
      <value>,大写,大写,System.String,,False,False</value>
      <value>,月结单位,月结单位,System.String,,False,False</value>
      <value>,可报费用,可报费用,System.String,,False,False</value>
      <value>,门诊统筹人次,门诊统筹人次,System.String,,False,False</value>
      <value>,门诊统筹补偿金额,门诊统筹补偿金额,System.String,,False,False</value>
      <value>,家庭账户补偿人次,家庭账户补偿人次,System.String,,False,False</value>
      <value>,家庭账户补偿金额,家庭账户补偿金额,System.String,,False,False</value>
      <value>,补偿合计,补偿合计,System.String,,False,False</value>
      <value>,总费用,总费用,System.String,,False,False</value>
    </Variables>
  </Dictionary>
  <EngineVersion>EngineV2</EngineVersion>
  <GlobalizationStrings isList="true" count="0" />
  <MetaTags isList="true" count="0" />
  <Pages isList="true" count="1">
    <Page1 Ref="2" type="Page" isKey="true">
      <Border>None;Black;2;Solid;False;4;Black</Border>
      <Brush>Transparent</Brush>
      <Components isList="true" count="1">
        <ReportTitleBand1 Ref="3" type="ReportTitleBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,0.4,19,7.5</ClientRectangle>
          <Components isList="true" count="20">
            <Text1 Ref="4" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,19,1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>黑体,15,Regular,Point,False,134</Font>
              <Guid>0a40c337a7e448f084c142dd687eb708</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text1</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{标题}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text1>
            <Text2 Ref="5" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>13,1,6,1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,0</Font>
              <Guid>045ea49074734b868ffd5dcd4931e4cc</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text2</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{打印时间}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text2>
            <Text3 Ref="6" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,1,6,1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,0</Font>
              <Guid>e927ffebf6c84e24ac9e07dcbd0864eb</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text3</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>月结单位：{月结单位}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text3>
            <Text4 Ref="7" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>6,1,7,1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,0</Font>
              <Guid>618f859979864223bfe6402aa2e24d9c</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text4</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{月结时间}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text4>
            <Text5 Ref="8" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,2,3.8,1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9</Font>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text5</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>医药费用总额</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text5>
            <Text6 Ref="9" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>3.8,2,3.8,1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9</Font>
              <Guid>8d12a73b0f4840a7a81e8cb66de7534e</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text6</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>补偿可报金额</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text6>
            <Text8 Ref="10" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>7.6,2,7.6,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9</Font>
              <Guid>456f18bc11e449d79feb69b9c1fb62a4</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text8</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>家庭账户补偿</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text8>
            <Text9 Ref="11" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>15.2,2,3.8,1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9</Font>
              <Guid>151c7b0dbb1b45aaae2eefe3615e7bc9</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text9</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>补偿合计</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text9>
            <Text10 Ref="12" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>7.6,2.5,3.8,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9</Font>
              <Guid>4577760fa5c84ed2a9e586e508e21011</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text10</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>人数</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text10>
            <Text11 Ref="13" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>11.4,2.5,3.8,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9</Font>
              <Guid>df9d5b4c5ed4410395046aa92093a578</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text11</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>金额</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text11>
            <Text14 Ref="14" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,3,3.8,1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9</Font>
              <Guid>5d8b013de07b483e9a85454ecdabae54</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text14</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{总费用}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text14>
            <Text15 Ref="15" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>3.8,3,3.8,1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9</Font>
              <Guid>d5a23c7db0f14c62b0ef18ab0b71867b</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text15</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{可报费用}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text15>
            <Text16 Ref="16" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>7.6,3,3.8,1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9</Font>
              <Guid>6ac7e6733d264b4f900773746022d3db</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text16</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{门诊统筹人次}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text16>
            <Text18 Ref="17" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>15.2,3,3.8,1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9</Font>
              <Guid>317999ee88104580a94e86c3574f60c9</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text18</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{补偿合计}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text18>
            <Text19 Ref="18" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>11.4,3,3.8,1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9</Font>
              <Guid>ad224b847bba4fe78a06dcad146925ca</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text19</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{门诊统筹补偿金额}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text19>
            <Text21 Ref="19" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,4,3.8,1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9</Font>
              <Guid>fdbb3cb1ce344ac7a4f6a1264b142a92</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text21</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>补偿金额（大写）</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text21>
            <Text22 Ref="20" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>3.8,4,15.2,1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9</Font>
              <Guid>4190c63a6a784967bb36765fed53d6c6</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text22</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{大写}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text22>
            <Text23 Ref="21" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>1,5,3.8,1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9</Font>
              <Guid>4492143535324b70bdb4e021984eacca</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text23</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>经办人：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text23>
            <Text24 Ref="22" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>6.6,5,3.8,1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9</Font>
              <Guid>b4c31fb2ce12491d9edc7cadbec3560c</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text24</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>审核人：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text24>
            <Text25 Ref="23" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>11.6,5,3.8,1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9</Font>
              <Guid>055e436f5b6f42c9ae6ccb494d73cca2</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text25</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>领导审批：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text25>
          </Components>
          <Conditions isList="true" count="0" />
          <Name>ReportTitleBand1</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
        </ReportTitleBand1>
      </Components>
      <Conditions isList="true" count="0" />
      <Guid>2fb177867e564de28323a8c8ccfdad91</Guid>
      <Margins>1,1,1,1</Margins>
      <Name>Page1</Name>
      <PageHeight>9.9</PageHeight>
      <PageWidth>21</PageWidth>
      <Report isRef="0" />
      <Watermark Ref="24" type="Stimulsoft.Report.Components.StiWatermark" isKey="true">
        <Font>Arial,100</Font>
        <TextBrush>[50:0:0:0]</TextBrush>
      </Watermark>
    </Page1>
  </Pages>
  <PrinterSettings Ref="25" type="Stimulsoft.Report.Print.StiPrinterSettings" isKey="true" />
  <ReferencedAssemblies isList="true" count="8">
    <value>System.Dll</value>
    <value>System.Drawing.Dll</value>
    <value>System.Windows.Forms.Dll</value>
    <value>System.Data.Dll</value>
    <value>System.Xml.Dll</value>
    <value>Stimulsoft.Controls.Dll</value>
    <value>Stimulsoft.Base.Dll</value>
    <value>Stimulsoft.Report.Dll</value>
  </ReferencedAssemblies>
  <ReportAlias>门诊统筹月结</ReportAlias>
  <ReportChanged>7/2/2015 9:07:03 AM</ReportChanged>
  <ReportCreated>10/13/2012 10:39:58 AM</ReportCreated>
  <ReportFile>C:\Users\<USER>\Desktop\his20101\his2010\Rpt\门诊统筹月结（丰南个人账户）.mrt</ReportFile>
  <ReportGuid>0b74639365034acea46f9d851e7e47ad</ReportGuid>
  <ReportName>门诊统筹月结</ReportName>
  <ReportUnit>Centimeters</ReportUnit>
  <ReportVersion>2011.2.1026</ReportVersion>
  <Script>using System;
using System.Drawing;
using System.Windows.Forms;
using System.Data;
using Stimulsoft.Controls;
using Stimulsoft.Base.Drawing;
using Stimulsoft.Report;
using Stimulsoft.Report.Dialogs;
using Stimulsoft.Report.Components;

namespace Reports
{
    public class Report : Stimulsoft.Report.StiReport
    {
        public Report()        {
            this.InitializeComponent();
        }

        #region StiReport Designer generated code - do not modify
        #endregion StiReport Designer generated code - do not modify
    }
}
</Script>
  <ScriptLanguage>CSharp</ScriptLanguage>
  <Styles isList="true" count="0" />
</StiSerializer>