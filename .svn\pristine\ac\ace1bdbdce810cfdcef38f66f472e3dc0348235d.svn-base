﻿/**  版本信息模板在安装目录下，可自行修改。
* D_DRYB_MzSf.cs
*
* 功 能： N/A
* 类 名： D_DRYB_MzSf
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2016/12/20 9:52:06   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using System.Collections;

namespace DAL
{
	/// <summary>
	/// 数据访问类:D_DRYB_MzSf
	/// </summary>
	public partial class D_DRYB_MzSf
	{
		public D_DRYB_MzSf()
		{}
		#region  BasicMethod

		/// <summary>
		/// 得到最大ID
		/// </summary>
		public int GetMaxId()
		{
		return HisVar.HisVar.Sqldal.GetMaxID("Id", "DRYB_MzSf"); 
		}

		/// <summary>
		/// 是否存在该记录
		/// </summary>
		public bool Exists(int Id)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) from DRYB_MzSf");
			strSql.Append(" where Id=@Id");
			SqlParameter[] parameters = {
					new SqlParameter("@Id", SqlDbType.Int,4)
			};
			parameters[0].Value = Id;

			return HisVar.HisVar.Sqldal.Exists(strSql.ToString(),parameters);
		}
       

		/// <summary>
		/// 增加一条数据
		/// </summary>
		public int Add(Model.M_DRYB_MzSf model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("insert into DRYB_MzSf(");
            strSql.Append("BcFhJbYlbxFyJe,BcFhJbYlbxWZfJe,BcGrzhZfJe,BcGwyBzZfJe,BcJrDbBf,BcTcZfJe,BcXjZfJe,JshICYe,YlFyZe,BnZyCs,BcQfxBz,MzZy_Code,Ry_Name,Lb,YbJz_Code,Jsr_Code,Js_Zt,His_Rj_Zt)");
			strSql.Append(" values (");
            strSql.Append("@BcFhJbYlbxFyJe,@BcFhJbYlbxWZfJe,@BcGrzhZfJe,@BcGwyBzZfJe,@BcJrDbBf,@BcTcZfJe,@BcXjZfJe,@JshICYe,@YlFyZe,@BnZyCs,@BcQfxBz,@MzZy_Code,@Ry_Name,@Lb,@YbJz_Code,@Jsr_Code,@Js_Zt,@His_Rj_Zt)");
			strSql.Append(";select @@IDENTITY");
			SqlParameter[] parameters = {
					new SqlParameter("@BcFhJbYlbxFyJe", SqlDbType.Decimal,9),
					new SqlParameter("@BcFhJbYlbxWZfJe", SqlDbType.Decimal,9),
					new SqlParameter("@BcGrzhZfJe", SqlDbType.Decimal,9),
					new SqlParameter("@BcGwyBzZfJe", SqlDbType.Decimal,9),
					new SqlParameter("@BcJrDbBf", SqlDbType.Decimal,9),
					new SqlParameter("@BcTcZfJe", SqlDbType.Decimal,9),
					new SqlParameter("@BcXjZfJe", SqlDbType.Decimal,9),
					new SqlParameter("@JshICYe", SqlDbType.Decimal,9),
					new SqlParameter("@YlFyZe", SqlDbType.Decimal,9),
					new SqlParameter("@BnZyCs", SqlDbType.Decimal,9),
					new SqlParameter("@BcQfxBz", SqlDbType.Decimal,9),
					new SqlParameter("@MzZy_Code", SqlDbType.Char,18),
					new SqlParameter("@Ry_Name", SqlDbType.VarChar,50),
					new SqlParameter("@Lb", SqlDbType.VarChar,4),
					new SqlParameter("@YbJz_Code", SqlDbType.VarChar,12),
					new SqlParameter("@Jsr_Code", SqlDbType.Char,7),
					new SqlParameter("@Js_Zt", SqlDbType.Bit,1),
                    new SqlParameter("@His_Rj_Zt", SqlDbType.Bit,1)};
			parameters[0].Value = model.BcFhJbYlbxFyJe;
			parameters[1].Value = model.BcFhJbYlbxWZfJe;
			parameters[2].Value = model.BcGrzhZfJe;
			parameters[3].Value = model.BcGwyBzZfJe;
			parameters[4].Value = model.BcJrDbBf;
			parameters[5].Value = model.BcTcZfJe;
			parameters[6].Value = model.BcXjZfJe;
			parameters[7].Value = model.JshICYe;
			parameters[8].Value = model.YlFyZe;
			parameters[9].Value = model.BnZyCs;
			parameters[10].Value = model.BcQfxBz;
			parameters[11].Value = model.MzZy_Code;
			parameters[12].Value = model.Ry_Name;
			parameters[13].Value = model.Lb;
			parameters[14].Value = model.YbJz_Code;
			parameters[15].Value = model.Jsr_Code;
			parameters[16].Value = model.Js_Zt;
            parameters[17].Value = model.His_Rj_Zt;

			object obj = HisVar.HisVar.Sqldal.GetSingle(strSql.ToString(),parameters);
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}
		/// <summary>
		/// 更新一条数据
		/// </summary>
		public bool Update(Model.M_DRYB_MzSf model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("update DRYB_MzSf set ");
			strSql.Append("BcFhJbYlbxFyJe=@BcFhJbYlbxFyJe,");
			strSql.Append("BcFhJbYlbxWZfJe=@BcFhJbYlbxWZfJe,");
			strSql.Append("BcGrzhZfJe=@BcGrzhZfJe,");
			strSql.Append("BcGwyBzZfJe=@BcGwyBzZfJe,");
			strSql.Append("BcJrDbBf=@BcJrDbBf,");
			strSql.Append("BcTcZfJe=@BcTcZfJe,");
			strSql.Append("BcXjZfJe=@BcXjZfJe,");
			strSql.Append("JshICYe=@JshICYe,");
			strSql.Append("YlFyZe=@YlFyZe,");
			strSql.Append("BnZyCs=@BnZyCs,");
			strSql.Append("BcQfxBz=@BcQfxBz,");
			strSql.Append("MzZy_Code=@MzZy_Code,");
			strSql.Append("Ry_Name=@Ry_Name,");
			strSql.Append("Lb=@Lb,");
			strSql.Append("YbJz_Code=@YbJz_Code,");
			strSql.Append("Jsr_Code=@Jsr_Code,");
			strSql.Append("Js_Zt=@Js_Zt");
			strSql.Append(" where Id=@Id");
			SqlParameter[] parameters = {
					new SqlParameter("@BcFhJbYlbxFyJe", SqlDbType.Decimal,9),
					new SqlParameter("@BcFhJbYlbxWZfJe", SqlDbType.Decimal,9),
					new SqlParameter("@BcGrzhZfJe", SqlDbType.Decimal,9),
					new SqlParameter("@BcGwyBzZfJe", SqlDbType.Decimal,9),
					new SqlParameter("@BcJrDbBf", SqlDbType.Decimal,9),
					new SqlParameter("@BcTcZfJe", SqlDbType.Decimal,9),
					new SqlParameter("@BcXjZfJe", SqlDbType.Decimal,9),
					new SqlParameter("@JshICYe", SqlDbType.Decimal,9),
					new SqlParameter("@YlFyZe", SqlDbType.Decimal,9),
					new SqlParameter("@BnZyCs", SqlDbType.Decimal,9),
					new SqlParameter("@BcQfxBz", SqlDbType.Decimal,9),
					new SqlParameter("@MzZy_Code", SqlDbType.Char,18),
					new SqlParameter("@Ry_Name", SqlDbType.VarChar,50),
					new SqlParameter("@Lb", SqlDbType.VarChar,4),
					new SqlParameter("@YbJz_Code", SqlDbType.VarChar,12),
					new SqlParameter("@Jsr_Code", SqlDbType.Char,7),
					new SqlParameter("@Js_Zt", SqlDbType.Bit,1),
					new SqlParameter("@Id", SqlDbType.Int,4)};
			parameters[0].Value = model.BcFhJbYlbxFyJe;
			parameters[1].Value = model.BcFhJbYlbxWZfJe;
			parameters[2].Value = model.BcGrzhZfJe;
			parameters[3].Value = model.BcGwyBzZfJe;
			parameters[4].Value = model.BcJrDbBf;
			parameters[5].Value = model.BcTcZfJe;
			parameters[6].Value = model.BcXjZfJe;
			parameters[7].Value = model.JshICYe;
			parameters[8].Value = model.YlFyZe;
			parameters[9].Value = model.BnZyCs;
			parameters[10].Value = model.BcQfxBz;
			parameters[11].Value = model.MzZy_Code;
			parameters[12].Value = model.Ry_Name;
			parameters[13].Value = model.Lb;
			parameters[14].Value = model.YbJz_Code;
			parameters[15].Value = model.Jsr_Code;
			parameters[16].Value = model.Js_Zt;
			parameters[17].Value = model.Id;

			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

        /// <summary>
		/// 更新一条数据
		/// </summary>
        public bool UpdateById(string Js_ids,string Ht_ids, string YbJz_Code)
        {
            
            ArrayList arry = new ArrayList();
            arry.Add("UPDATE DRYB_MzSf SET YbJz_Code = '" + YbJz_Code + "' WHERE Id IN (" + Js_ids + ")");
            arry.Add("UPDATE DRYB_MzSf_Ht SET YbJz_Code = '" + YbJz_Code + "' WHERE Id IN (" + Ht_ids + ")");
            try
            {
                HisVar.HisVar.Sqldal.ExecuteSqlTran(arry);
                return true;
            }
            catch (Exception e)
            {
                return false;
            }
        }


		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool Delete(int Id)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from DRYB_MzSf ");
			strSql.Append(" where Id=@Id");
			SqlParameter[] parameters = {
					new SqlParameter("@Id", SqlDbType.Int,4)
			};
			parameters[0].Value = Id;

			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 批量删除数据
		/// </summary>
		public bool DeleteList(string Idlist )
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from DRYB_MzSf ");
			strSql.Append(" where Id in ("+Idlist + ")  ");
			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString());
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public Model.M_DRYB_MzSf GetModel(int Id)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select  top 1 Id,BcFhJbYlbxFyJe,BcFhJbYlbxWZfJe,BcGrzhZfJe,BcGwyBzZfJe,BcJrDbBf,BcTcZfJe,BcXjZfJe,JshICYe,YlFyZe,BnZyCs,BcQfxBz,MzZy_Code,Ry_Name,Lb,YbJz_Code,Jsr_Code,Js_Zt from DRYB_MzSf ");
			strSql.Append(" where Id=@Id");
			SqlParameter[] parameters = {
					new SqlParameter("@Id", SqlDbType.Int,4)
			};
			parameters[0].Value = Id;

			Model.M_DRYB_MzSf model=new Model.M_DRYB_MzSf();
			DataSet ds=HisVar.HisVar.Sqldal.Query(strSql.ToString(),parameters);
			if(ds.Tables[0].Rows.Count>0)
			{
				return DataRowToModel(ds.Tables[0].Rows[0]);
			}
			else
			{
				return null;
			}
		}
        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public Model.M_DRYB_MzSf GetModelByCondition(string where)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  top 1 Id,BcFhJbYlbxFyJe,BcFhJbYlbxWZfJe,BcGrzhZfJe,BcGwyBzZfJe,BcJrDbBf,BcTcZfJe,BcXjZfJe,JshICYe,YlFyZe,BnZyCs,BcQfxBz,MzZy_Code,Ry_Name,Lb,YbJz_Code,Jsr_Code,Js_Zt from DRYB_MzSf ");


            if (!string.IsNullOrEmpty(where))
            {
                strSql.Append(" where " + where);
            }

            Model.M_DRYB_MzSf model = new Model.M_DRYB_MzSf();
            DataSet ds = HisVar.HisVar.Sqldal.Query(strSql.ToString());
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }

		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public Model.M_DRYB_MzSf DataRowToModel(DataRow row)
		{
			Model.M_DRYB_MzSf model=new Model.M_DRYB_MzSf();
			if (row != null)
			{
				if(row["Id"]!=null && row["Id"].ToString()!="")
				{
					model.Id=int.Parse(row["Id"].ToString());
				}
				if(row["BcFhJbYlbxFyJe"]!=null && row["BcFhJbYlbxFyJe"].ToString()!="")
				{
					model.BcFhJbYlbxFyJe=decimal.Parse(row["BcFhJbYlbxFyJe"].ToString());
				}
				if(row["BcFhJbYlbxWZfJe"]!=null && row["BcFhJbYlbxWZfJe"].ToString()!="")
				{
					model.BcFhJbYlbxWZfJe=decimal.Parse(row["BcFhJbYlbxWZfJe"].ToString());
				}
				if(row["BcGrzhZfJe"]!=null && row["BcGrzhZfJe"].ToString()!="")
				{
					model.BcGrzhZfJe=decimal.Parse(row["BcGrzhZfJe"].ToString());
				}
				if(row["BcGwyBzZfJe"]!=null && row["BcGwyBzZfJe"].ToString()!="")
				{
					model.BcGwyBzZfJe=decimal.Parse(row["BcGwyBzZfJe"].ToString());
				}
				if(row["BcJrDbBf"]!=null && row["BcJrDbBf"].ToString()!="")
				{
					model.BcJrDbBf=decimal.Parse(row["BcJrDbBf"].ToString());
				}
				if(row["BcTcZfJe"]!=null && row["BcTcZfJe"].ToString()!="")
				{
					model.BcTcZfJe=decimal.Parse(row["BcTcZfJe"].ToString());
				}
				if(row["BcXjZfJe"]!=null && row["BcXjZfJe"].ToString()!="")
				{
					model.BcXjZfJe=decimal.Parse(row["BcXjZfJe"].ToString());
				}
				if(row["JshICYe"]!=null && row["JshICYe"].ToString()!="")
				{
					model.JshICYe=decimal.Parse(row["JshICYe"].ToString());
				}
				if(row["YlFyZe"]!=null && row["YlFyZe"].ToString()!="")
				{
					model.YlFyZe=decimal.Parse(row["YlFyZe"].ToString());
				}
				if(row["BnZyCs"]!=null && row["BnZyCs"].ToString()!="")
				{
					model.BnZyCs=decimal.Parse(row["BnZyCs"].ToString());
				}
				if(row["BcQfxBz"]!=null && row["BcQfxBz"].ToString()!="")
				{
					model.BcQfxBz=decimal.Parse(row["BcQfxBz"].ToString());
				}
				if(row["MzZy_Code"]!=null)
				{
					model.MzZy_Code=row["MzZy_Code"].ToString();
				}
				if(row["Ry_Name"]!=null)
				{
					model.Ry_Name=row["Ry_Name"].ToString();
				}
				if(row["Lb"]!=null)
				{
					model.Lb=row["Lb"].ToString();
				}
				if(row["YbJz_Code"]!=null)
				{
					model.YbJz_Code=row["YbJz_Code"].ToString();
				}
				if(row["Jsr_Code"]!=null)
				{
					model.Jsr_Code=row["Jsr_Code"].ToString();
				}
				if(row["Js_Zt"]!=null && row["Js_Zt"].ToString()!="")
				{
					if((row["Js_Zt"].ToString()=="1")||(row["Js_Zt"].ToString().ToLower()=="true"))
					{
						model.Js_Zt=true;
					}
					else
					{
						model.Js_Zt=false;
					}
				}
			}
			return model;
		}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetList(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
            strSql.Append("select * from (select id, BcGrzhZfJe,BcTcZfJe,BcXjZfJe ,MzZy_Code,Ry_Name,Lb,YbJz_Code,DRYB_MzSf.Jsr_Code ,JSR_NAME,Lr_Date, '结算' AS Tb_Lb from DRYB_MzSf ,ZD_YYJSR WHERE   DRYB_MzSf.Jsr_Code = ZD_YYJSR.JSR_CODE ");
            strSql.Append(" union all SELECT id, -BcGrzhZfJe,-BcTcZfJe,-BcXjZfJe,MzZy_Code,Ry_Name,Lb,YbJz_Code,DRYB_MzSf_Ht.Jsr_Code ,JSR_NAME,Lr_Date,'回退' AS Tb_Lb FROM DRYB_MzSf_Ht,ZD_YYJSR WHERE   DRYB_MzSf_ht.Jsr_Code = ZD_YYJSR.JSR_CODE )a");

			if(strWhere.Trim()!="")
			{
                strSql.Append(" where  " + strWhere);
			}
            strSql.Append(" ORDER BY a.MzZy_Code ,a.Lr_Date ");
			return HisVar.HisVar.Sqldal.Query(strSql.ToString());
		}
                
		/// <summary>
		/// 获得前几行数据
		/// </summary>
		public DataSet GetList(int Top,string strWhere,string filedOrder)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select ");
			if(Top>0)
			{
				strSql.Append(" top "+Top.ToString());
			}
			strSql.Append(" Id,BcFhJbYlbxFyJe,BcFhJbYlbxWZfJe,BcGrzhZfJe,BcGwyBzZfJe,BcJrDbBf,BcTcZfJe,BcXjZfJe,JshICYe,YlFyZe,BnZyCs,BcQfxBz,MzZy_Code,Ry_Name,Lb,YbJz_Code,Jsr_Code,Js_Zt ");
			strSql.Append(" FROM DRYB_MzSf ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			strSql.Append(" order by " + filedOrder);
			return HisVar.HisVar.Sqldal.Query(strSql.ToString());
		}

		/// <summary>
		/// 获取记录总数
		/// </summary>
		public int GetRecordCount(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) FROM DRYB_MzSf ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			object obj = HisVar.HisVar.Sqldal.GetSingle(strSql.ToString());
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}
        /// <summary>
        /// 报销笔数
        /// </summary>
        /// <param name="YbJz_Code"></param>
        /// <returns></returns>

        public int GetBaoXiaoCount(string YbJz_Code)
        {
            if (string.IsNullOrEmpty(YbJz_Code))
            {
                return 0;
            }
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT COUNT(1) FROM DRYB_MzSf WHERE YbJz_Code = '" + YbJz_Code + "' ");

            object obj = HisVar.HisVar.Sqldal.GetSingle(strSql.ToString());
            if (obj == null)
            {
                return 0;
            }
            else
            {
                return Convert.ToInt32(obj);
            }
        }
        /// <summary>
        /// 日结后笔数
        /// </summary>
        /// <param name="YbJz_Code"></param>
        /// <returns></returns>
        public int GetHisRiJieCount(string YbJz_Code)
        {
            if (string.IsNullOrEmpty(YbJz_Code))
            {
                return 0;
            }
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT COUNT(1) FROM DRYB_MzSf WHERE His_Rj_Zt = 1 AND Js_Zt = 1 AND YbJz_Code = '" + YbJz_Code + "'");

            object obj = HisVar.HisVar.Sqldal.GetSingle(strSql.ToString());
            if (obj == null)
            {
                return 0;
            }
            else
            {
                return Convert.ToInt32(obj);
            }
        }

        /// <summary>
        /// 实际笔数
        /// </summary>
        /// <param name="YbJz_Code"></param>
        /// <returns></returns>
        public int GetShiJiCount(string YbJz_Code)
        {
            if (string.IsNullOrEmpty(YbJz_Code))
            {
                return 0;
            }
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT COUNT(1) FROM DRYB_MzSf WHERE  Js_Zt = 1 AND YbJz_Code = '" + YbJz_Code + "'");

            object obj = HisVar.HisVar.Sqldal.GetSingle(strSql.ToString());
            if (obj == null)
            {
                return 0;
            }
            else
            {
                return Convert.ToInt32(obj);
            }
        }

        /// <summary>
        /// 报销金额
        /// </summary>
        /// <returns></returns>
        public Decimal GetBaoXiaoMoney(string YbJz_Code)
        {
            if (string.IsNullOrEmpty(YbJz_Code))
            {
                return 0;
            }
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT SUM(BcTcZfJe) FROM DRYB_MzSf WHERE YbJz_Code = '" + YbJz_Code + "'");

            object obj = HisVar.HisVar.Sqldal.GetSingle(strSql.ToString());
            if (obj == null)
            {
                return 0;
            }
            else
            {
                return Convert.ToDecimal(obj);
            }
        }

        /// <summary>
        /// 实际金额
        /// </summary>
        /// <param name="YbJz_Code"></param>
        /// <returns></returns>
        public Decimal GetShiJiMoney(string YbJz_Code)
        {
            if (string.IsNullOrEmpty(YbJz_Code))
            {
                return 0;
            }
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT SUM(BcTcZfJe) FROM DRYB_MzSf WHERE Js_Zt = 1 AND YbJz_Code = '" + YbJz_Code + "'");

            object obj = HisVar.HisVar.Sqldal.GetSingle(strSql.ToString());
            if (obj == null)
            {
                return 0;
            }
            else
            {
                return Convert.ToDecimal(obj);
            }
        }

        /// <summary>
        /// 日结后报销金额
        /// </summary>
        /// <param name="YbJz_Code"></param>
        /// <returns></returns>
        public Decimal GetHisRiJieBaoXiaoMoney(string YbJz_Code)
        {
            if (string.IsNullOrEmpty(YbJz_Code))
            {
                return 0;
            }
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT SUM(BcTcZfJe) FROM DRYB_MzSf WHERE His_Rj_Zt = 1 AND Js_Zt = 1 AND YbJz_Code = '" + YbJz_Code + "'");

            object obj = HisVar.HisVar.Sqldal.GetSingle(strSql.ToString());
            if (obj == null)
            {
                return 0;
            }
            else
            {
                return Convert.ToDecimal(obj);
            }
        }

        /// <summary>
        /// 日结后个人账户支付金额
        /// </summary>
        /// <param name="YbJz_Code"></param>
        /// <returns></returns>
        public Decimal GetHisRiJieGeRenZhangHuZhiFuMoney(string YbJz_Code)
        {
            if (string.IsNullOrEmpty(YbJz_Code))
            {
                return 0;
            }
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT SUM(BcGrzhZfJe) FROM DRYB_MzSf WHERE His_Rj_Zt = 1 AND Js_Zt = 1 AND YbJz_Code = '" + YbJz_Code + "'");

            object obj = HisVar.HisVar.Sqldal.GetSingle(strSql.ToString());
            if (obj == null)
            {
                return 0;
            }
            else
            {
                return Convert.ToDecimal(obj);
            }
        }

        /// <summary>
        /// 日结后现金
        /// </summary>
        /// <param name="YbJz_Code"></param>
        /// <returns></returns>
        public Decimal GetHisRiJieXianJinMoney(string YbJz_Code)
        {
            if (string.IsNullOrEmpty(YbJz_Code))
            {
                return 0;
            }
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT SUM(BcXjZfJe) FROM DRYB_MzSf WHERE His_Rj_Zt = 1 AND Js_Zt = 1 AND YbJz_Code = '" + YbJz_Code + "'");

            object obj = HisVar.HisVar.Sqldal.GetSingle(strSql.ToString());
            if (obj == null)
            {
                return 0;
            }
            else
            {
                return Convert.ToDecimal(obj);
            }
        }


		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("SELECT * FROM ( ");
			strSql.Append(" SELECT ROW_NUMBER() OVER (");
			if (!string.IsNullOrEmpty(orderby.Trim()))
			{
				strSql.Append("order by T." + orderby );
			}
			else
			{
				strSql.Append("order by T.Id desc");
			}
			strSql.Append(")AS Row, T.*  from DRYB_MzSf T ");
			if (!string.IsNullOrEmpty(strWhere.Trim()))
			{
				strSql.Append(" WHERE " + strWhere);
			}
			strSql.Append(" ) TT");
			strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
			return HisVar.HisVar.Sqldal.Query(strSql.ToString());
		}

		/*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "DRYB_MzSf";
			parameters[1].Value = "Id";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return HisVar.HisVar.Sqldal.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

		#endregion  BasicMethod
		#region  ExtensionMethod

		#endregion  ExtensionMethod
	}
}

