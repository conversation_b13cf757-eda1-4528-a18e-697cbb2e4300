﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class TestSq1
    Inherits HisControl.BaseForm

    'Form 重写 Dispose，以清理组件列表。
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Windows 窗体设计器所必需的
    Private components As System.ComponentModel.IContainer

    '注意: 以下过程是 Windows 窗体设计器所必需的
    '可以使用 Windows 窗体设计器修改它。
    '不要使用代码编辑器修改它。
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.Panel1 = New System.Windows.Forms.Panel()
        Me.SendJsr = New CustomControl.MyDtComobo()
        Me.BtnDel = New CustomControl.MyButton()
        Me.BtnSx = New CustomControl.MyButton()
        Me.BtnSave = New CustomControl.MyButton()
        Me.BtnClear = New CustomControl.MyButton()
        Me.RyName = New CustomControl.MyTextBox()
        Me.TestXm = New CustomControl.MyDtComobo()
        Me.BtnOK = New CustomControl.MyButton()
        Me.TestCode = New CustomControl.MyTextBox()
        Me.BtnRead = New CustomControl.MyButton()
        Me.RySfzh = New CustomControl.MyTextBox()
        Me.BtnPrint = New CustomControl.MyButton()
        Me.RySex = New CustomControl.MySingleComobo()
        Me.JkkNo = New CustomControl.MyTextBox()
        Me.RyAge = New CustomControl.MyNumericEdit()
        Me.RyAgeMonth = New CustomControl.MyNumericEdit()
        Me.Label1 = New System.Windows.Forms.Label()
        Me.RyAgeWeek = New CustomControl.MyNumericEdit()
        Me.MyGrid1 = New CustomControl.MyGrid()
        Me.MyDateEdit1 = New CustomControl.MyDateEdit()
        Me.MyDateEdit2 = New CustomControl.MyDateEdit()
        Me.BtnQuery = New CustomControl.MyButton()
        Me.TestState = New CustomControl.MySingleComobo()
        Me.Panel1.SuspendLayout()
        Me.SuspendLayout()
        '
        'Panel1
        '
        Me.Panel1.Controls.Add(Me.TestState)
        Me.Panel1.Controls.Add(Me.BtnQuery)
        Me.Panel1.Controls.Add(Me.MyDateEdit2)
        Me.Panel1.Controls.Add(Me.MyDateEdit1)
        Me.Panel1.Controls.Add(Me.SendJsr)
        Me.Panel1.Controls.Add(Me.BtnDel)
        Me.Panel1.Controls.Add(Me.BtnSx)
        Me.Panel1.Controls.Add(Me.BtnSave)
        Me.Panel1.Controls.Add(Me.BtnClear)
        Me.Panel1.Controls.Add(Me.RyName)
        Me.Panel1.Controls.Add(Me.TestXm)
        Me.Panel1.Controls.Add(Me.BtnOK)
        Me.Panel1.Controls.Add(Me.TestCode)
        Me.Panel1.Controls.Add(Me.BtnRead)
        Me.Panel1.Controls.Add(Me.RySfzh)
        Me.Panel1.Controls.Add(Me.BtnPrint)
        Me.Panel1.Controls.Add(Me.RySex)
        Me.Panel1.Controls.Add(Me.JkkNo)
        Me.Panel1.Controls.Add(Me.RyAge)
        Me.Panel1.Controls.Add(Me.RyAgeMonth)
        Me.Panel1.Controls.Add(Me.Label1)
        Me.Panel1.Controls.Add(Me.RyAgeWeek)
        Me.Panel1.Dock = System.Windows.Forms.DockStyle.Top
        Me.Panel1.Location = New System.Drawing.Point(0, 0)
        Me.Panel1.Name = "Panel1"
        Me.Panel1.Size = New System.Drawing.Size(1224, 109)
        Me.Panel1.TabIndex = 0
        '
        'SendJsr
        '
        Me.SendJsr.Captain = "申 请 人"
        Me.SendJsr.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.SendJsr.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.SendJsr.CaptainWidth = 60.0!
        Me.SendJsr.DataSource = Nothing
        Me.SendJsr.ItemHeight = 18
        Me.SendJsr.ItemTextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.SendJsr.Location = New System.Drawing.Point(411, 71)
        Me.SendJsr.MaximumSize = New System.Drawing.Size(10000, 20)
        Me.SendJsr.MinimumSize = New System.Drawing.Size(0, 20)
        Me.SendJsr.Name = "SendJsr"
        Me.SendJsr.ReadOnly = False
        Me.SendJsr.Size = New System.Drawing.Size(192, 20)
        Me.SendJsr.TabIndex = 28
        Me.SendJsr.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        '
        'BtnDel
        '
        Me.BtnDel.ButtonImageSize = CustomControl.MyButton.imageSize.small
        Me.BtnDel.DialogResult = System.Windows.Forms.DialogResult.None
        Me.BtnDel.Location = New System.Drawing.Point(1008, 69)
        Me.BtnDel.Name = "BtnDel"
        Me.BtnDel.Size = New System.Drawing.Size(79, 26)
        Me.BtnDel.TabIndex = 27
        Me.BtnDel.Text = "删除"
        '
        'BtnSx
        '
        Me.BtnSx.ButtonImageSize = CustomControl.MyButton.imageSize.small
        Me.BtnSx.DialogResult = System.Windows.Forms.DialogResult.None
        Me.BtnSx.Location = New System.Drawing.Point(1008, 37)
        Me.BtnSx.Name = "BtnSx"
        Me.BtnSx.Size = New System.Drawing.Size(79, 26)
        Me.BtnSx.TabIndex = 26
        Me.BtnSx.Text = "刷新"
        '
        'BtnSave
        '
        Me.BtnSave.ButtonImageSize = CustomControl.MyButton.imageSize.small
        Me.BtnSave.DialogResult = System.Windows.Forms.DialogResult.None
        Me.BtnSave.Location = New System.Drawing.Point(838, 37)
        Me.BtnSave.Name = "BtnSave"
        Me.BtnSave.Size = New System.Drawing.Size(79, 26)
        Me.BtnSave.TabIndex = 24
        Me.BtnSave.Tag = "保存"
        Me.BtnSave.Text = "保存"
        '
        'BtnClear
        '
        Me.BtnClear.ButtonImageSize = CustomControl.MyButton.imageSize.small
        Me.BtnClear.DialogResult = System.Windows.Forms.DialogResult.None
        Me.BtnClear.Location = New System.Drawing.Point(923, 69)
        Me.BtnClear.Name = "BtnClear"
        Me.BtnClear.Size = New System.Drawing.Size(79, 26)
        Me.BtnClear.TabIndex = 23
        Me.BtnClear.Text = "清空"
        '
        'RyName
        '
        Me.RyName.Captain = "姓    名"
        Me.RyName.CaptainBackColor = System.Drawing.Color.Transparent
        Me.RyName.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.RyName.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.RyName.CaptainWidth = 60.0!
        Me.RyName.ContentForeColor = System.Drawing.Color.Black
        Me.RyName.DisabledForeColor = System.Drawing.Color.FromArgb(CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer))
        Me.RyName.Location = New System.Drawing.Point(37, 41)
        Me.RyName.Multiline = False
        Me.RyName.Name = "RyName"
        Me.RyName.PasswordChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.RyName.ReadOnly = False
        Me.RyName.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.RyName.SelectionStart = 0
        Me.RyName.SelectStart = 0
        Me.RyName.Size = New System.Drawing.Size(180, 22)
        Me.RyName.TabIndex = 1
        Me.RyName.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.RyName.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle
        '
        'TestXm
        '
        Me.TestXm.Captain = "申请项目"
        Me.TestXm.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.TestXm.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.TestXm.CaptainWidth = 60.0!
        Me.TestXm.DataSource = Nothing
        Me.TestXm.ItemHeight = 18
        Me.TestXm.ItemTextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.TestXm.Location = New System.Drawing.Point(609, 71)
        Me.TestXm.MaximumSize = New System.Drawing.Size(10000, 20)
        Me.TestXm.MinimumSize = New System.Drawing.Size(0, 20)
        Me.TestXm.Name = "TestXm"
        Me.TestXm.ReadOnly = False
        Me.TestXm.Size = New System.Drawing.Size(192, 20)
        Me.TestXm.TabIndex = 9
        Me.TestXm.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        '
        'BtnOK
        '
        Me.BtnOK.ButtonImageSize = CustomControl.MyButton.imageSize.small
        Me.BtnOK.DialogResult = System.Windows.Forms.DialogResult.None
        Me.BtnOK.Location = New System.Drawing.Point(838, 69)
        Me.BtnOK.Name = "BtnOK"
        Me.BtnOK.Size = New System.Drawing.Size(79, 26)
        Me.BtnOK.TabIndex = 10
        Me.BtnOK.Tag = "申请"
        Me.BtnOK.Text = "申请"
        '
        'TestCode
        '
        Me.TestCode.Captain = "检验编码"
        Me.TestCode.CaptainBackColor = System.Drawing.Color.Transparent
        Me.TestCode.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.TestCode.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.TestCode.CaptainWidth = 60.0!
        Me.TestCode.ContentForeColor = System.Drawing.Color.Black
        Me.TestCode.DisabledForeColor = System.Drawing.Color.FromArgb(CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer))
        Me.TestCode.Location = New System.Drawing.Point(37, 11)
        Me.TestCode.Multiline = False
        Me.TestCode.Name = "TestCode"
        Me.TestCode.PasswordChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.TestCode.ReadOnly = False
        Me.TestCode.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.TestCode.SelectionStart = 0
        Me.TestCode.SelectStart = 0
        Me.TestCode.Size = New System.Drawing.Size(180, 22)
        Me.TestCode.TabIndex = 0
        Me.TestCode.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.TestCode.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle
        '
        'BtnRead
        '
        Me.BtnRead.ButtonImageSize = CustomControl.MyButton.imageSize.small
        Me.BtnRead.DialogResult = System.Windows.Forms.DialogResult.None
        Me.BtnRead.Location = New System.Drawing.Point(223, 13)
        Me.BtnRead.Name = "BtnRead"
        Me.BtnRead.Size = New System.Drawing.Size(42, 22)
        Me.BtnRead.TabIndex = 15
        Me.BtnRead.Text = "读卡"
        '
        'RySfzh
        '
        Me.RySfzh.Captain = "身份证号"
        Me.RySfzh.CaptainBackColor = System.Drawing.Color.Transparent
        Me.RySfzh.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.RySfzh.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.RySfzh.CaptainWidth = 60.0!
        Me.RySfzh.ContentForeColor = System.Drawing.Color.Black
        Me.RySfzh.DisabledForeColor = System.Drawing.Color.FromArgb(CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer))
        Me.RySfzh.Location = New System.Drawing.Point(411, 43)
        Me.RySfzh.Multiline = False
        Me.RySfzh.Name = "RySfzh"
        Me.RySfzh.PasswordChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.RySfzh.ReadOnly = False
        Me.RySfzh.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.RySfzh.SelectionStart = 0
        Me.RySfzh.SelectStart = 0
        Me.RySfzh.Size = New System.Drawing.Size(192, 22)
        Me.RySfzh.TabIndex = 3
        Me.RySfzh.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.RySfzh.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle
        '
        'BtnPrint
        '
        Me.BtnPrint.ButtonImageSize = CustomControl.MyButton.imageSize.small
        Me.BtnPrint.DialogResult = System.Windows.Forms.DialogResult.None
        Me.BtnPrint.Location = New System.Drawing.Point(923, 37)
        Me.BtnPrint.Name = "BtnPrint"
        Me.BtnPrint.Size = New System.Drawing.Size(79, 24)
        Me.BtnPrint.TabIndex = 13
        Me.BtnPrint.Text = "打印"
        '
        'RySex
        '
        Me.RySex.Captain = "性    别"
        Me.RySex.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.RySex.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.RySex.CaptainWidth = 60.0!
        Me.RySex.ComboStyle = C1.Win.C1List.ComboStyleEnum.DropdownList
        Me.RySex.ItemHeight = 16
        Me.RySex.ItemTextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.RySex.Location = New System.Drawing.Point(225, 45)
        Me.RySex.MaximumSize = New System.Drawing.Size(10000, 20)
        Me.RySex.MinimumSize = New System.Drawing.Size(0, 20)
        Me.RySex.Name = "RySex"
        Me.RySex.ReadOnly = False
        Me.RySex.Size = New System.Drawing.Size(180, 20)
        Me.RySex.TabIndex = 2
        Me.RySex.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        '
        'JkkNo
        '
        Me.JkkNo.Captain = "健康卡号"
        Me.JkkNo.CaptainBackColor = System.Drawing.Color.Transparent
        Me.JkkNo.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.JkkNo.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.JkkNo.CaptainWidth = 60.0!
        Me.JkkNo.ContentForeColor = System.Drawing.Color.Black
        Me.JkkNo.DisabledForeColor = System.Drawing.Color.FromArgb(CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer))
        Me.JkkNo.Location = New System.Drawing.Point(609, 44)
        Me.JkkNo.Multiline = False
        Me.JkkNo.Name = "JkkNo"
        Me.JkkNo.PasswordChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.JkkNo.ReadOnly = False
        Me.JkkNo.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.JkkNo.SelectionStart = 0
        Me.JkkNo.SelectStart = 0
        Me.JkkNo.Size = New System.Drawing.Size(192, 22)
        Me.JkkNo.TabIndex = 4
        Me.JkkNo.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.JkkNo.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle
        '
        'RyAge
        '
        Me.RyAge.Captain = "年    龄"
        Me.RyAge.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.RyAge.CaptainWidth = 60.0!
        Me.RyAge.Location = New System.Drawing.Point(37, 69)
        Me.RyAge.MaximumSize = New System.Drawing.Size(100000, 80)
        Me.RyAge.MinimumSize = New System.Drawing.Size(0, 20)
        Me.RyAge.Name = "RyAge"
        Me.RyAge.NumFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.RyAge.ReadOnly = False
        Me.RyAge.Size = New System.Drawing.Size(151, 20)
        Me.RyAge.TabIndex = 5
        Me.RyAge.ValueIsDbNull = False
        '
        'RyAgeMonth
        '
        Me.RyAgeMonth.Captain = "岁"
        Me.RyAgeMonth.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.RyAgeMonth.CaptainWidth = 20.0!
        Me.RyAgeMonth.Location = New System.Drawing.Point(194, 69)
        Me.RyAgeMonth.MaximumSize = New System.Drawing.Size(100000, 80)
        Me.RyAgeMonth.MinimumSize = New System.Drawing.Size(0, 20)
        Me.RyAgeMonth.Name = "RyAgeMonth"
        Me.RyAgeMonth.NumFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.RyAgeMonth.ReadOnly = False
        Me.RyAgeMonth.Size = New System.Drawing.Size(74, 20)
        Me.RyAgeMonth.TabIndex = 6
        Me.RyAgeMonth.ValueIsDbNull = False
        '
        'Label1
        '
        Me.Label1.AutoSize = True
        Me.Label1.Location = New System.Drawing.Point(352, 74)
        Me.Label1.Name = "Label1"
        Me.Label1.Size = New System.Drawing.Size(17, 12)
        Me.Label1.TabIndex = 9
        Me.Label1.Text = "周"
        '
        'RyAgeWeek
        '
        Me.RyAgeWeek.Captain = "月"
        Me.RyAgeWeek.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.RyAgeWeek.CaptainWidth = 20.0!
        Me.RyAgeWeek.Location = New System.Drawing.Point(274, 69)
        Me.RyAgeWeek.MaximumSize = New System.Drawing.Size(100000, 80)
        Me.RyAgeWeek.MinimumSize = New System.Drawing.Size(0, 20)
        Me.RyAgeWeek.Name = "RyAgeWeek"
        Me.RyAgeWeek.NumFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.RyAgeWeek.ReadOnly = False
        Me.RyAgeWeek.Size = New System.Drawing.Size(72, 20)
        Me.RyAgeWeek.TabIndex = 7
        Me.RyAgeWeek.ValueIsDbNull = False
        '
        'MyGrid1
        '
        Me.MyGrid1.CanCustomCol = False
        Me.MyGrid1.Col = 0
        Me.MyGrid1.ColumnFooters = False
        Me.MyGrid1.DirectionAfterEnter = C1.Win.C1TrueDBGrid.DirectionAfterEnterEnum.MoveRight
        Me.MyGrid1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.MyGrid1.FetchRowStyles = False
        Me.MyGrid1.Location = New System.Drawing.Point(0, 109)
        Me.MyGrid1.Margin = New System.Windows.Forms.Padding(0)
        Me.MyGrid1.MarqueeStyle = C1.Win.C1TrueDBGrid.MarqueeEnum.HighlightRow
        Me.MyGrid1.Name = "MyGrid1"
        Me.MyGrid1.Size = New System.Drawing.Size(1224, 473)
        Me.MyGrid1.TabAction = C1.Win.C1TrueDBGrid.TabActionEnum.ControlNavigation
        Me.MyGrid1.TabIndex = 1
        Me.MyGrid1.Xmlpath = Nothing
        '
        'MyDateEdit1
        '
        Me.MyDateEdit1.Captain = "申请时间"
        Me.MyDateEdit1.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.MyDateEdit1.CaptainWidth = 60.0!
        Me.MyDateEdit1.InitialSelection = C1.Win.C1Input.InitialSelectionEnum.CaretAtEnd
        Me.MyDateEdit1.Location = New System.Drawing.Point(411, 13)
        Me.MyDateEdit1.MaximumSize = New System.Drawing.Size(100000000, 20)
        Me.MyDateEdit1.MinimumSize = New System.Drawing.Size(0, 20)
        Me.MyDateEdit1.Name = "MyDateEdit1"
        Me.MyDateEdit1.Size = New System.Drawing.Size(192, 20)
        Me.MyDateEdit1.TabIndex = 29
        Me.MyDateEdit1.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.MyDateEdit1.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
        Me.MyDateEdit1.VisibleButtons = C1.Win.C1Input.DropDownControlButtonFlags.DropDown
        '
        'MyDateEdit2
        '
        Me.MyDateEdit2.Captain = "至"
        Me.MyDateEdit2.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.MyDateEdit2.CaptainWidth = 60.0!
        Me.MyDateEdit2.InitialSelection = C1.Win.C1Input.InitialSelectionEnum.CaretAtEnd
        Me.MyDateEdit2.Location = New System.Drawing.Point(609, 13)
        Me.MyDateEdit2.MaximumSize = New System.Drawing.Size(100000000, 20)
        Me.MyDateEdit2.MinimumSize = New System.Drawing.Size(0, 20)
        Me.MyDateEdit2.Name = "MyDateEdit2"
        Me.MyDateEdit2.Size = New System.Drawing.Size(192, 20)
        Me.MyDateEdit2.TabIndex = 30
        Me.MyDateEdit2.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.MyDateEdit2.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
        Me.MyDateEdit2.VisibleButtons = C1.Win.C1Input.DropDownControlButtonFlags.DropDown
        '
        'BtnQuery
        '
        Me.BtnQuery.ButtonImageSize = CustomControl.MyButton.imageSize.small
        Me.BtnQuery.DialogResult = System.Windows.Forms.DialogResult.None
        Me.BtnQuery.Location = New System.Drawing.Point(838, 5)
        Me.BtnQuery.Name = "BtnQuery"
        Me.BtnQuery.Size = New System.Drawing.Size(79, 26)
        Me.BtnQuery.TabIndex = 31
        Me.BtnQuery.Tag = "查询"
        Me.BtnQuery.Text = "查询"
        '
        'TestState
        '
        Me.TestState.Captain = "状态"
        Me.TestState.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.TestState.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.TestState.CaptainWidth = 40.0!
        Me.TestState.ComboStyle = C1.Win.C1List.ComboStyleEnum.DropdownList
        Me.TestState.ItemHeight = 16
        Me.TestState.ItemTextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.TestState.Location = New System.Drawing.Point(271, 13)
        Me.TestState.MaximumSize = New System.Drawing.Size(10000, 20)
        Me.TestState.MinimumSize = New System.Drawing.Size(0, 20)
        Me.TestState.Name = "TestState"
        Me.TestState.ReadOnly = False
        Me.TestState.Size = New System.Drawing.Size(134, 20)
        Me.TestState.TabIndex = 32
        Me.TestState.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        '
        'TestSq1
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 12.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(1224, 582)
        Me.Controls.Add(Me.MyGrid1)
        Me.Controls.Add(Me.Panel1)
        Me.Name = "TestSq1"
        Me.Text = "TestSq1"
        Me.Panel1.ResumeLayout(False)
        Me.Panel1.PerformLayout()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents Panel1 As System.Windows.Forms.Panel
    Friend WithEvents BtnOK As CustomControl.MyButton
    Friend WithEvents TestCode As CustomControl.MyTextBox
    Friend WithEvents BtnRead As CustomControl.MyButton
    Friend WithEvents RySfzh As CustomControl.MyTextBox
    Friend WithEvents BtnPrint As CustomControl.MyButton
    Friend WithEvents RySex As CustomControl.MySingleComobo
    Friend WithEvents JkkNo As CustomControl.MyTextBox
    Friend WithEvents RyAge As CustomControl.MyNumericEdit
    Friend WithEvents RyAgeMonth As CustomControl.MyNumericEdit
    Friend WithEvents Label1 As System.Windows.Forms.Label
    Friend WithEvents RyAgeWeek As CustomControl.MyNumericEdit
    Friend WithEvents MyGrid1 As CustomControl.MyGrid
    Friend WithEvents TestXm As CustomControl.MyDtComobo
    Friend WithEvents RyName As CustomControl.MyTextBox
    Friend WithEvents BtnClear As CustomControl.MyButton
    Friend WithEvents BtnSave As CustomControl.MyButton
    Friend WithEvents BtnDel As CustomControl.MyButton
    Friend WithEvents BtnSx As CustomControl.MyButton
    Friend WithEvents SendJsr As CustomControl.MyDtComobo
    Friend WithEvents BtnQuery As CustomControl.MyButton
    Friend WithEvents MyDateEdit2 As CustomControl.MyDateEdit
    Friend WithEvents MyDateEdit1 As CustomControl.MyDateEdit
    Friend WithEvents TestState As CustomControl.MySingleComobo
End Class
