﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>9.0.30729</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{AD6CA345-D90D-4C1A-A6D7-D3E9119F883C}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <StartupObject>Sub Main</StartupObject>
    <RootNamespace>His2010</RootNamespace>
    <AssemblyName>His2010</AssemblyName>
    <FileAlignment>512</FileAlignment>
    <MyType>WindowsFormsWithCustomSubMain</MyType>
    <TargetFrameworkVersion>v4.0</TargetFrameworkVersion>
    <OptionExplicit>On</OptionExplicit>
    <OptionCompare>Binary</OptionCompare>
    <OptionStrict>Off</OptionStrict>
    <OptionInfer>On</OptionInfer>
    <ManifestCertificateThumbprint>23DDE51CDA06346D628A9E872BF57B16F21DD06A</ManifestCertificateThumbprint>
    <ManifestKeyFile>MyKey.pfx</ManifestKeyFile>
    <GenerateManifests>true</GenerateManifests>
    <SignManifests>true</SignManifests>
    <IsWebBootstrapper>true</IsWebBootstrapper>
    <ApplicationIcon>44副本.ico</ApplicationIcon>
    <ApplicationManifest>My Project\app.manifest</ApplicationManifest>
    <FileUpgradeFlags>
    </FileUpgradeFlags>
    <UpgradeBackupLocation>
    </UpgradeBackupLocation>
    <OldToolsVersion>3.5</OldToolsVersion>
    <TargetFrameworkProfile />
    <PublishUrl>D:\IIS\ZTHis4\</PublishUrl>
    <Install>true</Install>
    <InstallFrom>Web</InstallFrom>
    <UpdateEnabled>true</UpdateEnabled>
    <UpdateMode>Foreground</UpdateMode>
    <UpdateInterval>7</UpdateInterval>
    <UpdateIntervalUnits>Days</UpdateIntervalUnits>
    <UpdatePeriodically>false</UpdatePeriodically>
    <UpdateRequired>false</UpdateRequired>
    <MapFileExtensions>true</MapFileExtensions>
    <InstallUrl>http://192.168.1.100/His2019/</InstallUrl>
    <ProductName>医院管理系统4.0</ProductName>
    <PublisherName>中软智通（唐山）科技有限公司</PublisherName>
    <CreateWebPageOnPublish>true</CreateWebPageOnPublish>
    <WebPage>index.htm</WebPage>
    <OpenBrowserOnPublish>false</OpenBrowserOnPublish>
    <ApplicationRevision>740</ApplicationRevision>
    <ApplicationVersion>4.0.0.%2a</ApplicationVersion>
    <UseApplicationTrust>false</UseApplicationTrust>
    <CreateDesktopShortcut>true</CreateDesktopShortcut>
    <PublishWizardCompleted>true</PublishWizardCompleted>
    <BootstrapperEnabled>true</BootstrapperEnabled>
    <BootstrapperComponentsLocation>Relative</BootstrapperComponentsLocation>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <DefineDebug>true</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <OutputPath>..\output\</OutputPath>
    <DocumentationFile>His2010.xml</DocumentationFile>
    <NoWarn>41999,42016,42017,42018,42019,42020,42021,42022,42032,42036,42353,42354,42355</NoWarn>
    <CodeAnalysisRuleSet>AllRules.ruleset</CodeAnalysisRuleSet>
    <PlatformTarget>x86</PlatformTarget>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <DefineDebug>false</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DocumentationFile>His2010.xml</DocumentationFile>
    <NoWarn>41999,42016,42017,42018,42019,42020,42021,42022,42032,42036,42353,42354,42355</NoWarn>
    <CodeAnalysisRuleSet>AllRules.ruleset</CodeAnalysisRuleSet>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup>
    <SignAssembly>false</SignAssembly>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="C1.C1Excel.4, Version=4.0.20163.212, Culture=neutral, PublicKeyToken=79882d576c6336da, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="C1.C1Pdf.4, Version=4.0.20163.212, Culture=neutral, PublicKeyToken=79882d576c6336da, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="C1.C1Report.4, Version=4.0.20192.375, Culture=neutral, PublicKeyToken=594a0605db190bb9, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="C1.C1Zip.4, Version=4.0.20163.212, Culture=neutral, PublicKeyToken=79882d576c6336da">
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="C1.Win.4, Version=4.0.20163.212, Culture=neutral, PublicKeyToken=944ae1ea0e47ca04, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="C1.Win.C1Command.4, Version=4.0.20163.212, Culture=neutral, PublicKeyToken=e808566f358766d8, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="C1.Win.C1FlexGrid.4, Version=4.0.20163.212, Culture=neutral, PublicKeyToken=79882d576c6336da, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="C1.Win.C1Input.4, Version=4.0.20163.212, Culture=neutral, PublicKeyToken=7e7ff60f0c214f9a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="C1.Win.C1List.4, Version=4.0.20163.212, Culture=neutral, PublicKeyToken=6b24f8f981dbd7bc, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="C1.Win.C1Report.4, Version=4.0.20192.375, Culture=neutral, PublicKeyToken=41780e2fc605e636, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="C1.Win.C1Ribbon.4, Version=4.0.20163.212, Culture=neutral, PublicKeyToken=79882d576c6336da, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="C1.Win.C1SuperTooltip.4, Version=4.0.20163.212, Culture=neutral, PublicKeyToken=79882d576c6336da, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="C1.Win.C1TrueDBGrid.4, Version=4.0.20163.212, Culture=neutral, PublicKeyToken=75ae3fb0e2b1e0da, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="C1.Win.Input.MultiSelect.4, Version=4.0.20222.566, Culture=neutral, PublicKeyToken=79882d576c6336da, processorArchitecture=MSIL" />
    <Reference Include="C1.Win.TreeView.4, Version=4.0.20163.212, Culture=neutral, PublicKeyToken=79882d576c6336da, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="Common.Enum, Version=*******, Culture=neutral, processorArchitecture=x86">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\output\Common.Enum.dll</HintPath>
    </Reference>
    <Reference Include="DCSoft.Writer, Version=1.2014.1217.1, Culture=neutral, PublicKeyToken=2e40e961ea876340, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\output\DCSoft.Writer.dll</HintPath>
    </Reference>
    <Reference Include="DDTek.Oracle, Version=3.5.0.0, Culture=neutral, PublicKeyToken=c84cd5c63851e072, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\Dll\DDTek.Oracle.dll</HintPath>
    </Reference>
    <Reference Include="DDTekOracleDalHelper, Version=1.0.0.3, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\Dll\DDTekOracleDalHelper.dll</HintPath>
    </Reference>
    <Reference Include="EnCode, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\Dll\EnCode.dll</HintPath>
    </Reference>
    <Reference Include="iniOperate">
      <HintPath>..\Dll\iniOperate.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=13.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.13.0.1\lib\net40\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="oledbDalHelper, Version=1.0.0.2, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\Dll\oledbDalHelper.dll</HintPath>
    </Reference>
    <Reference Include="SqlDal, Version=1.0.0.16, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\Dll\SqlDal.dll</HintPath>
    </Reference>
    <Reference Include="SqlLiteHelper, Version=1.0.0.2, Culture=neutral, processorArchitecture=x86">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\Dll\SqlLiteHelper.dll</HintPath>
    </Reference>
    <Reference Include="Stimulsoft.Base, Version=2011.2.1026.0, Culture=neutral, PublicKeyToken=ebe6666cba19647a, processorArchitecture=MSIL">
      <Private>True</Private>
    </Reference>
    <Reference Include="Stimulsoft.Controls, Version=2011.2.1026.0, Culture=neutral, PublicKeyToken=ebe6666cba19647a, processorArchitecture=MSIL">
      <Private>True</Private>
    </Reference>
    <Reference Include="Stimulsoft.Controls.Win, Version=2011.2.1026.0, Culture=neutral, PublicKeyToken=ebe6666cba19647a, processorArchitecture=MSIL">
      <Private>True</Private>
    </Reference>
    <Reference Include="Stimulsoft.Design, Version=2011.2.1026.0, Culture=neutral, PublicKeyToken=ebe6666cba19647a, processorArchitecture=MSIL">
      <Private>True</Private>
    </Reference>
    <Reference Include="Stimulsoft.Editor, Version=2011.2.1026.0, Culture=neutral, PublicKeyToken=ebe6666cba19647a, processorArchitecture=MSIL">
      <Private>True</Private>
    </Reference>
    <Reference Include="Stimulsoft.Report, Version=2011.2.1026.0, Culture=neutral, PublicKeyToken=ebe6666cba19647a, processorArchitecture=MSIL">
      <Private>True</Private>
    </Reference>
    <Reference Include="Stimulsoft.Report.Check, Version=2011.2.1026.0, Culture=neutral, PublicKeyToken=ebe6666cba19647a, processorArchitecture=MSIL">
      <Private>True</Private>
    </Reference>
    <Reference Include="Stimulsoft.Report.Design, Version=2011.2.1026.0, Culture=neutral, PublicKeyToken=ebe6666cba19647a, processorArchitecture=MSIL">
      <Private>True</Private>
    </Reference>
    <Reference Include="Stimulsoft.Report.Helper, Version=2011.2.1026.0, Culture=neutral, PublicKeyToken=ebe6666cba19647a, processorArchitecture=MSIL">
      <Private>True</Private>
    </Reference>
    <Reference Include="Stimulsoft.Report.Win, Version=2011.2.1026.0, Culture=neutral, PublicKeyToken=ebe6666cba19647a, processorArchitecture=MSIL">
      <Private>True</Private>
    </Reference>
    <Reference Include="System">
      <HintPath>C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.0\System.dll</HintPath>
    </Reference>
    <Reference Include="System.Configuration" />
    <Reference Include="System.Data" />
    <Reference Include="System.Data.OracleClient" />
    <Reference Include="System.Data.SQLite, Version=1.0.82.0, Culture=neutral, PublicKeyToken=db937bc2d44ff139, processorArchitecture=x86">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\Dll\System.Data.SQLite.dll</HintPath>
    </Reference>
    <Reference Include="System.Deployment" />
    <Reference Include="System.Design" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Drawing.Design" />
    <Reference Include="System.EnterpriseServices" />
    <Reference Include="System.Management" />
    <Reference Include="System.Security" />
    <Reference Include="System.Web" />
    <Reference Include="System.Web.Services" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="ZTHisInsuranceFunction, Version=*******, Culture=neutral, processorArchitecture=x86">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\output\ZTHisInsuranceFunction.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Import Include="Microsoft.VisualBasic" />
    <Import Include="System" />
    <Import Include="System.Collections" />
    <Import Include="System.Collections.Generic" />
    <Import Include="System.Data" />
    <Import Include="System.Drawing" />
    <Import Include="System.Diagnostics" />
    <Import Include="System.Windows.Forms" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="0.公用类\F_Model.vb" />
    <Compile Include="00.启动\F_Login.designer.vb">
      <DependentUpon>F_Login.vb</DependentUpon>
    </Compile>
    <Compile Include="00.启动\F_Login.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="00.启动\MainForm.designer.vb">
      <DependentUpon>MainForm.vb</DependentUpon>
    </Compile>
    <Compile Include="00.启动\MainForm.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="01.基础数据\1.04.发票设置\02.发票边距调整\Fp_Tz.Designer.vb">
      <DependentUpon>Fp_Tz.vb</DependentUpon>
    </Compile>
    <Compile Include="01.基础数据\1.04.发票设置\02.发票边距调整\Fp_Tz.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="01.基础数据\1.04.发票设置\07.门诊发票合并\Zd_MzFpHb1.Designer.vb">
      <DependentUpon>Zd_MzFpHb1.vb</DependentUpon>
    </Compile>
    <Compile Include="01.基础数据\1.04.发票设置\07.门诊发票合并\Zd_MzFpHb1.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="01.基础数据\1.04.发票设置\07.门诊发票合并\Zd_MzFpHb2.Designer.vb">
      <DependentUpon>Zd_MzFpHb2.vb</DependentUpon>
    </Compile>
    <Compile Include="01.基础数据\1.04.发票设置\07.门诊发票合并\Zd_MzFpHb2.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="01.基础数据\1.04.发票设置\07.门诊发票合并\Zd_MzFpHb3.Designer.vb">
      <DependentUpon>Zd_MzFpHb3.vb</DependentUpon>
    </Compile>
    <Compile Include="01.基础数据\1.04.发票设置\07.门诊发票合并\Zd_MzFpHb3.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="01.基础数据\1.04.发票设置\门诊发票分类\Zd_MzFp1.Designer.vb">
      <DependentUpon>Zd_MzFp1.vb</DependentUpon>
    </Compile>
    <Compile Include="01.基础数据\1.04.发票设置\门诊发票分类\Zd_MzFp1.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="01.基础数据\1.04.发票设置\门诊发票分类\Zd_MzFp2.designer.vb">
      <DependentUpon>Zd_MzFp2.vb</DependentUpon>
    </Compile>
    <Compile Include="01.基础数据\1.04.发票设置\门诊发票分类\Zd_MzFp2.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="01.基础数据\1.04.发票设置\门诊发票分类\Zd_MzFp3.designer.vb">
      <DependentUpon>Zd_MzFp3.vb</DependentUpon>
    </Compile>
    <Compile Include="01.基础数据\1.04.发票设置\门诊发票分类\Zd_MzFp3.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="01.基础数据\1.04.发票设置\门诊发票第三联分类\Zd_MzFp11.Designer.vb">
      <DependentUpon>Zd_MzFp11.vb</DependentUpon>
    </Compile>
    <Compile Include="01.基础数据\1.04.发票设置\门诊发票第三联分类\Zd_MzFp11.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="01.基础数据\1.04.发票设置\门诊发票第三联分类\Zd_MzFp12.designer.vb">
      <DependentUpon>Zd_MzFp12.vb</DependentUpon>
    </Compile>
    <Compile Include="01.基础数据\1.04.发票设置\门诊发票第三联分类\Zd_MzFp12.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="01.基础数据\1.04.发票设置\门诊发票第三联分类\Zd_MzFp13.designer.vb">
      <DependentUpon>Zd_MzFp13.vb</DependentUpon>
    </Compile>
    <Compile Include="01.基础数据\1.04.发票设置\门诊发票第三联分类\Zd_MzFp13.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="01.基础数据\1.04.发票设置\门诊发票第三联分类\Zd_MzFp14.Designer.vb">
      <DependentUpon>Zd_MzFp14.vb</DependentUpon>
    </Compile>
    <Compile Include="01.基础数据\1.04.发票设置\门诊发票第三联分类\Zd_MzFp14.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="01.基础数据\1.04.发票设置\门诊发票第三联分类\Zd_MzFp15.designer.vb">
      <DependentUpon>Zd_MzFp15.vb</DependentUpon>
    </Compile>
    <Compile Include="01.基础数据\1.04.发票设置\门诊发票第三联分类\Zd_MzFp15.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="02.药库管理\药库接收药房退库\Yk_Js1.Designer.vb">
      <DependentUpon>Yk_Js1.vb</DependentUpon>
    </Compile>
    <Compile Include="02.药库管理\药库接收药房退库\Yk_Js1.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="02.药库管理\药库接收药房退库\Yk_Js2.Designer.vb">
      <DependentUpon>Yk_Js2.vb</DependentUpon>
    </Compile>
    <Compile Include="02.药库管理\药库接收药房退库\Yk_Js2.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="02.药库管理\药库调价\Yk_Tj1.designer.vb">
      <DependentUpon>Yk_Tj1.vb</DependentUpon>
    </Compile>
    <Compile Include="02.药库管理\药库调价\Yk_Tj1.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="02.药库管理\药库调价\Yk_Tj2.designer.vb">
      <DependentUpon>Yk_Tj2.vb</DependentUpon>
    </Compile>
    <Compile Include="02.药库管理\药库调价\Yk_Tj2.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="02.药库管理\药库调价\Yk_Tj31.designer.vb">
      <DependentUpon>Yk_Tj31.vb</DependentUpon>
    </Compile>
    <Compile Include="02.药库管理\药库调价\Yk_Tj31.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="02.药库管理\调价生效\Yk_TjQp1.designer.vb">
      <DependentUpon>Yk_TjQp1.vb</DependentUpon>
    </Compile>
    <Compile Include="02.药库管理\调价生效\Yk_TjQp1.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="02.药库管理\调价生效\Yk_TjQp2.designer.vb">
      <DependentUpon>Yk_TjQp2.vb</DependentUpon>
    </Compile>
    <Compile Include="02.药库管理\调价生效\Yk_TjQp2.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="02.药库管理\调拨药房\Yk_Yf4.designer.vb">
      <DependentUpon>Yk_Yf4.vb</DependentUpon>
    </Compile>
    <Compile Include="02.药库管理\调拨药房\Yk_Yf4.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="03.药房管理\01.药房接收药库\Yf_Js1.Designer.vb">
      <DependentUpon>Yf_Js1.vb</DependentUpon>
    </Compile>
    <Compile Include="03.药房管理\01.药房接收药库\Yf_Js1.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="03.药房管理\06.住院药房退药\Zy_Ty.designer.vb">
      <DependentUpon>Zy_Ty.vb</DependentUpon>
    </Compile>
    <Compile Include="03.药房管理\06.住院药房退药\Zy_Ty.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="04.门诊管理\02.门诊录入\Xs_Mz1.designer.vb">
      <DependentUpon>Xs_Mz1.vb</DependentUpon>
    </Compile>
    <Compile Include="04.门诊管理\02.门诊录入\Xs_Mz1.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="03.药房管理\01.药房接收药库\Yf_Js2.Designer.vb">
      <DependentUpon>Yf_Js2.vb</DependentUpon>
    </Compile>
    <Compile Include="03.药房管理\01.药房接收药库\Yf_Js2.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="04.门诊管理\02.门诊录入\Xs_Mz2.designer.vb">
      <DependentUpon>Xs_Mz2.vb</DependentUpon>
    </Compile>
    <Compile Include="04.门诊管理\02.门诊录入\Xs_Mz2.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="04.门诊管理\02.门诊录入\Xs_Mz3.designer.vb">
      <DependentUpon>Xs_Mz3.vb</DependentUpon>
    </Compile>
    <Compile Include="04.门诊管理\02.门诊录入\Xs_Mz3.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="04.门诊管理\04.医生开处方\Ys_Cf2.designer.vb">
      <DependentUpon>Ys_Cf2.vb</DependentUpon>
    </Compile>
    <Compile Include="04.门诊管理\04.医生开处方\Ys_Cf2.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="05.住院管理\03.医嘱录入\Zy_Cf1.designer.vb">
      <DependentUpon>Zy_Cf1.vb</DependentUpon>
    </Compile>
    <Compile Include="05.住院管理\03.医嘱录入\Zy_Cf1.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="05.住院管理\04.出院\Xs_Zy_Cy1.designer.vb">
      <DependentUpon>Xs_Zy_Cy1.vb</DependentUpon>
    </Compile>
    <Compile Include="05.住院管理\04.出院\Xs_Zy_Cy1.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="05.住院管理\05.出院召回\Cy_Zh1.Designer.vb">
      <DependentUpon>Cy_Zh1.vb</DependentUpon>
    </Compile>
    <Compile Include="05.住院管理\05.出院召回\Cy_Zh1.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="05.住院管理\06.住院日结\Xs_Zy_Jz.Designer.vb">
      <DependentUpon>Xs_Zy_Jz.vb</DependentUpon>
    </Compile>
    <Compile Include="05.住院管理\06.住院日结\Xs_Zy_Jz.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="05.住院管理\06.住院日结\ZyRj_Print.Designer.vb">
      <DependentUpon>ZyRj_Print.vb</DependentUpon>
    </Compile>
    <Compile Include="05.住院管理\06.住院日结\ZyRj_Print.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="05.住院管理\06.住院日结\Zy_Jz_Zf.Designer.vb">
      <DependentUpon>Zy_Jz_Zf.vb</DependentUpon>
    </Compile>
    <Compile Include="05.住院管理\06.住院日结\Zy_Jz_Zf.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="05.住院管理\03.医嘱录入\Zy_Cf2.designer.vb">
      <DependentUpon>Zy_Cf2.vb</DependentUpon>
    </Compile>
    <Compile Include="05.住院管理\03.医嘱录入\Zy_Cf2.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="05.住院管理\07.长期医嘱\Cq_Cf1.designer.vb">
      <DependentUpon>Cq_Cf1.vb</DependentUpon>
    </Compile>
    <Compile Include="05.住院管理\07.长期医嘱\Cq_Cf1.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="05.住院管理\07.长期医嘱\Cq_Cf2.designer.vb">
      <DependentUpon>Cq_Cf2.vb</DependentUpon>
    </Compile>
    <Compile Include="05.住院管理\07.长期医嘱\Cq_Cf2.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="05.住院管理\07.长期医嘱\Cq_Cf3.designer.vb">
      <DependentUpon>Cq_Cf3.vb</DependentUpon>
    </Compile>
    <Compile Include="05.住院管理\07.长期医嘱\Cq_Cf3.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="06.数据查询\01.住院查询\住院日结查询\Zy_Rj_Cx.Designer.vb">
      <DependentUpon>Zy_Rj_Cx.vb</DependentUpon>
    </Compile>
    <Compile Include="06.数据查询\01.住院查询\住院日结查询\Zy_Rj_Cx.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="06.数据查询\01.住院查询\在院患者费用\Cx_Zyhz1.designer.vb">
      <DependentUpon>Cx_Zyhz1.vb</DependentUpon>
    </Compile>
    <Compile Include="06.数据查询\01.住院查询\在院患者费用\Cx_Zyhz1.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="06.数据查询\01.住院查询\在院患者费用\Cx_Zyhz2.designer.vb">
      <DependentUpon>Cx_Zyhz2.vb</DependentUpon>
    </Compile>
    <Compile Include="06.数据查询\01.住院查询\在院患者费用\Cx_Zyhz2.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="06.数据查询\01.住院查询\在院患者费用\Cx_ZyYjMx.designer.vb">
      <DependentUpon>Cx_ZyYjMx.vb</DependentUpon>
    </Compile>
    <Compile Include="06.数据查询\01.住院查询\在院患者费用\Cx_ZyYjMx.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="06.数据查询\01.住院查询\患者用药清单\Cx_Hzyy1.Designer.vb">
      <DependentUpon>Cx_Hzyy1.vb</DependentUpon>
    </Compile>
    <Compile Include="06.数据查询\01.住院查询\患者用药清单\Cx_Hzyy1.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="06.数据查询\03.药库查询\各药房信息查询\Yk_Cx_AllYf.Designer.vb">
      <DependentUpon>Yk_Cx_AllYf.vb</DependentUpon>
    </Compile>
    <Compile Include="06.数据查询\03.药库查询\各药房信息查询\Yk_Cx_AllYf.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="06.数据查询\03.药库查询\药库出入库台账\Yp_Cr_Tz.Designer.vb">
      <DependentUpon>Yp_Cr_Tz.vb</DependentUpon>
    </Compile>
    <Compile Include="06.数据查询\03.药库查询\药库出入库台账\Yp_Cr_Tz.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="88.公用窗体\药库药房出入库录入\YpNetCg_Dr.Designer.vb">
      <DependentUpon>YpNetCg_Dr.vb</DependentUpon>
    </Compile>
    <Compile Include="88.公用窗体\药库药房出入库录入\YpNetCg_Dr.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="06.数据查询\03.药库查询\药库调价查询\Yk_Tj_Cx1.Designer.vb">
      <DependentUpon>Yk_Tj_Cx1.vb</DependentUpon>
    </Compile>
    <Compile Include="06.数据查询\03.药库查询\药库调价查询\Yk_Tj_Cx1.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="06.数据查询\05.财务查询\全院工作量统计\Work_Tj.Designer.vb">
      <DependentUpon>Work_Tj.vb</DependentUpon>
    </Compile>
    <Compile Include="06.数据查询\05.财务查询\全院工作量统计\Work_Tj.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="06.数据查询\05.财务查询\床位统计\Cw_Tj.designer.vb">
      <DependentUpon>Cw_Tj.vb</DependentUpon>
    </Compile>
    <Compile Include="06.数据查询\05.财务查询\床位统计\Cw_Tj.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="06.数据查询\05.财务查询\科室收入统计\JcKs_Money_Tj.Designer.vb">
      <DependentUpon>JcKs_Money_Tj.vb</DependentUpon>
    </Compile>
    <Compile Include="06.数据查询\05.财务查询\科室收入统计\JcKs_Money_Tj.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="06.数据查询\05.财务查询\门诊住院日结查询\Cw_Cx_MzZy_Rj1.Designer.vb">
      <DependentUpon>Cw_Cx_MzZy_Rj1.vb</DependentUpon>
    </Compile>
    <Compile Include="06.数据查询\05.财务查询\门诊住院日结查询\Cw_Cx_MzZy_Rj1.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="06.数据查询\05.财务查询\门诊住院日结查询\Cw_Cx_MzZy_Rj2.Designer.vb">
      <DependentUpon>Cw_Cx_MzZy_Rj2.vb</DependentUpon>
    </Compile>
    <Compile Include="06.数据查询\05.财务查询\门诊住院日结查询\Cw_Cx_MzZy_Rj2.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="06.数据查询\05.财务查询\门诊住院日结查询\Cw_Cx_MzZy_Rj3.Designer.vb">
      <DependentUpon>Cw_Cx_MzZy_Rj3.vb</DependentUpon>
    </Compile>
    <Compile Include="06.数据查询\05.财务查询\门诊住院日结查询\Cw_Cx_MzZy_Rj3.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="88.公用窗体\药库药房报损报溢\YkYf_Bsby.designer.vb">
      <DependentUpon>YkYf_Bsby.vb</DependentUpon>
    </Compile>
    <Compile Include="88.公用窗体\药库药房报损报溢\YkYf_Bsby.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="88.公用窗体\药库药房数据盘点\YkYf_Pd1.designer.vb">
      <DependentUpon>YkYf_Pd1.vb</DependentUpon>
    </Compile>
    <Compile Include="88.公用窗体\药库药房数据盘点\YkYf_Pd1.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="88.公用窗体\药库药房数据盘点\YkYf_Pd2.designer.vb">
      <DependentUpon>YkYf_Pd2.vb</DependentUpon>
    </Compile>
    <Compile Include="88.公用窗体\药库药房数据盘点\YkYf_Pd2.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="88.公用窗体\药库药房警戒线查询\Cx_YkYf_Alar.Designer.vb">
      <DependentUpon>Cx_YkYf_Alar.vb</DependentUpon>
    </Compile>
    <Compile Include="88.公用窗体\药库药房警戒线查询\Cx_YkYf_Alar.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="99.系统设置\03.个人设置\Person_Config.Designer.vb">
      <DependentUpon>Person_Config.vb</DependentUpon>
    </Compile>
    <Compile Include="99.系统设置\03.个人设置\Person_Config.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="99.系统设置\05.数据库维护\DataBackUp.Designer.vb">
      <DependentUpon>DataBackUp.vb</DependentUpon>
    </Compile>
    <Compile Include="99.系统设置\05.数据库维护\DataBackUp.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="99.系统设置\Connect_Edit.Designer.vb">
      <DependentUpon>Connect_Edit.vb</DependentUpon>
    </Compile>
    <Compile Include="99.系统设置\Connect_Edit.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="99.系统设置\Zd_Cssz1.designer.vb">
      <DependentUpon>Zd_Cssz1.vb</DependentUpon>
    </Compile>
    <Compile Include="99.系统设置\Zd_Cssz1.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="My Project\AssemblyInfo.vb" />
    <Compile Include="My Project\Application.Designer.vb">
      <AutoGen>True</AutoGen>
      <DependentUpon>Application.myapp</DependentUpon>
    </Compile>
    <Compile Include="My Project\Resources.Designer.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Include="My Project\Settings.Designer.vb">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
    <Compile Include="88.公用窗体\基础数据\Zd_Dict1.Designer.vb">
      <DependentUpon>Zd_Dict1.vb</DependentUpon>
    </Compile>
    <Compile Include="88.公用窗体\基础数据\Zd_Dict1.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="88.公用窗体\基础数据\Zd_Dict2.Designer.vb">
      <DependentUpon>Zd_Dict2.vb</DependentUpon>
    </Compile>
    <Compile Include="88.公用窗体\基础数据\Zd_Dict2.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="88.公用窗体\精简选择\Zd_JjSave.Designer.vb">
      <DependentUpon>Zd_JjSave.vb</DependentUpon>
    </Compile>
    <Compile Include="88.公用窗体\精简选择\Zd_JjSave.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="88.公用窗体\药库药房出入库录入\YkYf_Ck3.designer.vb">
      <DependentUpon>YkYf_Ck3.vb</DependentUpon>
    </Compile>
    <Compile Include="88.公用窗体\药库药房出入库录入\YkYf_Ck3.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="88.公用窗体\药库药房出入库录入\YkYf_Crk1.designer.vb">
      <DependentUpon>YkYf_Crk1.vb</DependentUpon>
    </Compile>
    <Compile Include="88.公用窗体\药库药房出入库录入\YkYf_Crk1.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="88.公用窗体\药库药房出入库录入\YkYf_Crk2.designer.vb">
      <DependentUpon>YkYf_Crk2.vb</DependentUpon>
    </Compile>
    <Compile Include="88.公用窗体\药库药房出入库录入\YkYf_Crk2.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="88.公用窗体\药库药房出入库录入\YkYf_TkPf3.designer.vb">
      <DependentUpon>YkYf_TkPf3.vb</DependentUpon>
    </Compile>
    <Compile Include="88.公用窗体\药库药房出入库录入\YkYf_TkPf3.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Test.Designer.vb">
      <DependentUpon>Test.vb</DependentUpon>
    </Compile>
    <Compile Include="Test.vb">
      <SubType>Form</SubType>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="00.启动\F_Login.resx">
      <DependentUpon>F_Login.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="00.启动\MainForm.resx">
      <DependentUpon>MainForm.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="01.基础数据\1.04.发票设置\02.发票边距调整\Fp_Tz.resx">
      <DependentUpon>Fp_Tz.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="01.基础数据\1.04.发票设置\07.门诊发票合并\Zd_MzFpHb1.resx">
      <DependentUpon>Zd_MzFpHb1.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="01.基础数据\1.04.发票设置\07.门诊发票合并\Zd_MzFpHb2.resx">
      <DependentUpon>Zd_MzFpHb2.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="01.基础数据\1.04.发票设置\07.门诊发票合并\Zd_MzFpHb3.resx">
      <DependentUpon>Zd_MzFpHb3.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="01.基础数据\1.04.发票设置\门诊发票分类\Zd_MzFp1.resx">
      <DependentUpon>Zd_MzFp1.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="01.基础数据\1.04.发票设置\门诊发票分类\Zd_MzFp2.resx">
      <DependentUpon>Zd_MzFp2.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="01.基础数据\1.04.发票设置\门诊发票分类\Zd_MzFp3.resx">
      <DependentUpon>Zd_MzFp3.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="01.基础数据\1.04.发票设置\门诊发票第三联分类\Zd_MzFp11.resx">
      <DependentUpon>Zd_MzFp11.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="01.基础数据\1.04.发票设置\门诊发票第三联分类\Zd_MzFp12.resx">
      <DependentUpon>Zd_MzFp12.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="01.基础数据\1.04.发票设置\门诊发票第三联分类\Zd_MzFp13.resx">
      <DependentUpon>Zd_MzFp13.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="01.基础数据\1.04.发票设置\门诊发票第三联分类\Zd_MzFp14.resx">
      <DependentUpon>Zd_MzFp14.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="01.基础数据\1.04.发票设置\门诊发票第三联分类\Zd_MzFp15.resx">
      <DependentUpon>Zd_MzFp15.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="02.药库管理\药库接收药房退库\Yk_Js1.resx">
      <DependentUpon>Yk_Js1.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="02.药库管理\药库接收药房退库\Yk_Js2.resx">
      <DependentUpon>Yk_Js2.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="02.药库管理\药库调价\Yk_Tj1.resx">
      <DependentUpon>Yk_Tj1.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="02.药库管理\药库调价\Yk_Tj2.resx">
      <DependentUpon>Yk_Tj2.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="02.药库管理\药库调价\Yk_Tj31.resx">
      <DependentUpon>Yk_Tj31.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="02.药库管理\调价生效\Yk_TjQp1.resx">
      <DependentUpon>Yk_TjQp1.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="02.药库管理\调价生效\Yk_TjQp2.resx">
      <DependentUpon>Yk_TjQp2.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="02.药库管理\调拨药房\Yk_Yf4.resx">
      <DependentUpon>Yk_Yf4.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="03.药房管理\01.药房接收药库\Yf_Js1.resx">
      <DependentUpon>Yf_Js1.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="03.药房管理\06.住院药房退药\Zy_Ty.resx">
      <DependentUpon>Zy_Ty.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="04.门诊管理\02.门诊录入\Xs_Mz1.resx">
      <DependentUpon>Xs_Mz1.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="03.药房管理\01.药房接收药库\Yf_Js2.resx">
      <DependentUpon>Yf_Js2.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="04.门诊管理\02.门诊录入\Xs_Mz2.resx">
      <DependentUpon>Xs_Mz2.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="04.门诊管理\02.门诊录入\Xs_Mz3.resx">
      <DependentUpon>Xs_Mz3.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="04.门诊管理\04.医生开处方\Ys_Cf2.resx">
      <DependentUpon>Ys_Cf2.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="05.住院管理\03.医嘱录入\Zy_Cf1.resx">
      <DependentUpon>Zy_Cf1.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="05.住院管理\04.出院\Xs_Zy_Cy1.resx">
      <DependentUpon>Xs_Zy_Cy1.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="05.住院管理\05.出院召回\Cy_Zh1.resx">
      <DependentUpon>Cy_Zh1.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="05.住院管理\06.住院日结\Xs_Zy_Jz.resx">
      <DependentUpon>Xs_Zy_Jz.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="05.住院管理\06.住院日结\ZyRj_Print.resx">
      <DependentUpon>ZyRj_Print.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="05.住院管理\06.住院日结\Zy_Jz_Zf.resx">
      <DependentUpon>Zy_Jz_Zf.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="05.住院管理\03.医嘱录入\Zy_Cf2.resx">
      <DependentUpon>Zy_Cf2.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="05.住院管理\07.长期医嘱\Cq_Cf1.resx">
      <DependentUpon>Cq_Cf1.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="05.住院管理\07.长期医嘱\Cq_Cf2.resx">
      <DependentUpon>Cq_Cf2.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="05.住院管理\07.长期医嘱\Cq_Cf3.resx">
      <DependentUpon>Cq_Cf3.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="06.数据查询\01.住院查询\住院日结查询\Zy_Rj_Cx.resx">
      <DependentUpon>Zy_Rj_Cx.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="06.数据查询\01.住院查询\在院患者费用\Cx_Zyhz1.resx">
      <DependentUpon>Cx_Zyhz1.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="06.数据查询\01.住院查询\在院患者费用\Cx_Zyhz2.resx">
      <DependentUpon>Cx_Zyhz2.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="06.数据查询\01.住院查询\在院患者费用\Cx_ZyYjMx.resx">
      <DependentUpon>Cx_ZyYjMx.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="06.数据查询\01.住院查询\患者用药清单\Cx_Hzyy1.resx">
      <DependentUpon>Cx_Hzyy1.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="06.数据查询\03.药库查询\各药房信息查询\Yk_Cx_AllYf.resx">
      <DependentUpon>Yk_Cx_AllYf.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="06.数据查询\03.药库查询\药库出入库台账\Yp_Cr_Tz.resx">
      <DependentUpon>Yp_Cr_Tz.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="88.公用窗体\药库药房出入库录入\YpNetCg_Dr.resx">
      <DependentUpon>YpNetCg_Dr.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="06.数据查询\03.药库查询\药库调价查询\Yk_Tj_Cx1.resx">
      <DependentUpon>Yk_Tj_Cx1.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="06.数据查询\05.财务查询\全院工作量统计\Work_Tj.resx">
      <DependentUpon>Work_Tj.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="06.数据查询\05.财务查询\床位统计\Cw_Tj.resx">
      <DependentUpon>Cw_Tj.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="06.数据查询\05.财务查询\科室收入统计\JcKs_Money_Tj.resx">
      <DependentUpon>JcKs_Money_Tj.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="06.数据查询\05.财务查询\门诊住院日结查询\Cw_Cx_MzZy_Rj1.resx">
      <DependentUpon>Cw_Cx_MzZy_Rj1.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="06.数据查询\05.财务查询\门诊住院日结查询\Cw_Cx_MzZy_Rj2.resx">
      <DependentUpon>Cw_Cx_MzZy_Rj2.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="06.数据查询\05.财务查询\门诊住院日结查询\Cw_Cx_MzZy_Rj3.resx">
      <DependentUpon>Cw_Cx_MzZy_Rj3.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="88.公用窗体\药库药房报损报溢\YkYf_Bsby.resx">
      <DependentUpon>YkYf_Bsby.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="88.公用窗体\药库药房数据盘点\YkYf_Pd1.resx">
      <DependentUpon>YkYf_Pd1.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="88.公用窗体\药库药房数据盘点\YkYf_Pd2.resx">
      <DependentUpon>YkYf_Pd2.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="88.公用窗体\药库药房警戒线查询\Cx_YkYf_Alar.resx">
      <DependentUpon>Cx_YkYf_Alar.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="99.系统设置\03.个人设置\Person_Config.resx">
      <DependentUpon>Person_Config.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="99.系统设置\05.数据库维护\DataBackUp.resx">
      <DependentUpon>DataBackUp.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="99.系统设置\Connect_Edit.resx">
      <DependentUpon>Connect_Edit.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="99.系统设置\Zd_Cssz1.resx">
      <DependentUpon>Zd_Cssz1.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="My Project\licenses.licx" />
    <EmbeddedResource Include="My Project\Resources.resx">
      <Generator>VbMyResourcesResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.vb</LastGenOutput>
      <CustomToolNamespace>My.Resources</CustomToolNamespace>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="88.公用窗体\基础数据\Zd_Dict1.resx">
      <DependentUpon>Zd_Dict1.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="88.公用窗体\基础数据\Zd_Dict2.resx">
      <DependentUpon>Zd_Dict2.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="88.公用窗体\精简选择\Zd_JjSave.resx">
      <DependentUpon>Zd_JjSave.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="88.公用窗体\药库药房出入库录入\YkYf_Ck3.resx">
      <DependentUpon>YkYf_Ck3.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="88.公用窗体\药库药房出入库录入\YkYf_Crk1.resx">
      <DependentUpon>YkYf_Crk1.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="88.公用窗体\药库药房出入库录入\YkYf_Crk2.resx">
      <DependentUpon>YkYf_Crk2.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="88.公用窗体\药库药房出入库录入\YkYf_TkPf3.resx">
      <DependentUpon>YkYf_TkPf3.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Test.resx">
      <DependentUpon>Test.vb</DependentUpon>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <Content Include="IDReadDevDLL\HuaShi\sdtapi.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="IDReadDevDLL\HuaShi\Termb.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="IDReadDevDLL\HuaShi\WltRS.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="IDReadDevDLL\Synjones\ID_Fpr.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="IDReadDevDLL\Synjones\ID_FprCap.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="IDReadDevDLL\Synjones\sdtapi.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="IDReadDevDLL\Synjones\SynIDCardAPI.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="IDReadDevDLL\Synjones\USBRead.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="IDReadDevDLL\Synjones\WltRS.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="img\基本元素.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="img\打印.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="img\显示痕迹.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="img\选择续打.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="msvcr71.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="OSQL.EXE">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="YBCardDLL\GD\SSCard.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="YBCardDLL\JL\BmpToJpg.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="YBCardDLL\JL\CHSCardTool.exe">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="YBCardDLL\JL\conf.xml">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="YBCardDLL\JL\Fleck.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="YBCardDLL\JL\fwjc.ico">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="YBCardDLL\JL\HeaSecReadInfo.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="YBCardDLL\JL\HeaSecTesting.exe">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="YBCardDLL\JL\JpgDll.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="YBCardDLL\JL\NationECCode.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="YBCardDLL\JL\NationECCodeTest.exe">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="YBCardDLL\JL\NationEcCodeUpdater.exe">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="YBCardDLL\JL\NationEcCodeUpdater_Daemon.exe">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="YBCardDLL\JL\Neu_SFZ.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="YBCardDLL\JL\Neu_SFZFL.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="YBCardDLL\JL\Neu_SFZHD.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="YBCardDLL\JL\Neu_SFZHS.DLL">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="YBCardDLL\JL\Neu_SFZold.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="YBCardDLL\JL\ocx_call_demo.html">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="YBCardDLL\JL\sdtapi.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="YBCardDLL\JL\Services_Install.exe">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="YBCardDLL\JL\SiCoreClient.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="YBCardDLL\JL\SiExtend.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="YBCardDLL\JL\SSCardDriver.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="YBCardDLL\JL\SSSE32.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="YBCardDLL\JL\SSSE32.H">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="YBCardDLL\JL\SSSE32_DC1.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="YBCardDLL\JL\SSSE32_HD1.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="YBCardDLL\JL\SSSE32_TSD.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="YBCardDLL\JL\SSSE32_TSU.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="YBCardDLL\JL\TECSUN.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="YBCardDLL\JL\TSConfig.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="YBCardDLL\JL\TypeA.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="YBCardDLL\JL\unins000.exe">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="YBCardDLL\JL\UnPack.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="YBCardDLL\JL\WebClient_SFZ.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="YBCardDLL\JL\WebHeaSecClient.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="YBCardDLL\JL\WltRS.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="YBCardDLL\JL\wltrs_getbmp.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="YBCardDLL\LN\BmpToJpg.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="YBCardDLL\LN\GATEDZ.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="YBCardDLL\LN\NationECCode.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="YBCardDLL\LN\sdtapi.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="YBCardDLL\LN\SSCardDriver.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="YBCardDLL\LN\SSSE32.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="YBCardDLL\LN\SSSE32_DCV2.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="YBCardDLL\LN\SSSE32_GEM.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="YBCardDLL\LN\SSSE32_HD.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="YBCardDLL\LN\SSSE32_RD.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="YBCardDLL\LN\SSSE32_TS.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="YBCardDLL\LN\SSSE32_TSKB.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="YBCardDLL\LN\trf32.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="YBCardDLL\LN\UnPack.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="YBCardDLL\LN\WinSocket.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="YBCardDLL\LN\WltRS.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="YBCardDLL\LN\wltrs_getbmp.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="YBCardDLL\NMG\dc_nmg_sse.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="YBCardDLL\NMG\Ds_SSSE32.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="YBCardDLL\NMG\Ds_tsSICARD.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="YBCardDLL\NMG\Ds_WinSocket.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="YBCardDLL\NMG\Eapagent.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="YBCardDLL\NMG\GetInfo.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="YBCardDLL\NMG\gwiIcCard.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="YBCardDLL\NMG\HeaSecReadInfo.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="YBCardDLL\NMG\HeaSecTesting.exe">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="YBCardDLL\NMG\KeyPad.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="YBCardDLL\NMG\log4net.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="YBCardDLL\NMG\lzkc_dll.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="YBCardDLL\NMG\MT_NMSSSE32.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="YBCardDLL\NMG\mwIDdll\DLL_File.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="YBCardDLL\NMG\mwIDdll\sdtapi.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="YBCardDLL\NMG\mwIDdll\SysInfo.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="YBCardDLL\NMG\mwIDdll\termb.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="YBCardDLL\NMG\mwIDdll\UnPack.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="YBCardDLL\NMG\mwIDdll\WltRS.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="YBCardDLL\NMG\NationECCode.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="YBCardDLL\NMG\ScanerModule.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="YBCardDLL\NMG\SiCard.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="YBCardDLL\NMG\SSCardDriver.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="YBCardDLL\NMG\SSCardDriverDemo.exe">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="YBCardDLL\NMG\SSCardDriver_DC.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="YBCardDLL\NMG\SSCardDriver_DS.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="YBCardDLL\NMG\SSCardDriver_DX.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="YBCardDLL\NMG\SSCardDriver_JY.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="YBCardDLL\NMG\SSCardDriver_MT.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="YBCardDLL\NMG\SSCardDriver_MW.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="YBCardDLL\NMG\SSSE32.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="YBCardDLL\NMG\SSSE32_C.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="YBCardDLL\NMG\SSSE32_TSD.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="YBCardDLL\NMG\SSSE32_TSU.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="YBCardDLL\NMG\SSSEDemo.exe">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="YBCardDLL\NMG\TECSUN.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="YBCardDLL\NMG\TSConfig.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="YBCardDLL\NMG\TY_NMSE32.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="YBCardDLL\NMG\WltRS.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="YBCardDLL\NMG\YD570S_DLL.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\241111.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\210112.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\210422.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\210425.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\210426.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\210428.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\211111.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\211112.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\210429.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\210430.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\210501.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\210506.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\210507.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\210513.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\210514.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\210515.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\210516.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\210603.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\210623.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\211008.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\211009.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\211010.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\211026.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\211027.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\211028.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\211109.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\211115.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\211116.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\211117.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\211118.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\211120.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\211126.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\211203.sql" />
    <Content Include="更新脚本\211216.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\211217.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\220225.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\220305.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\220317.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\220320.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\220324.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\220325.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\220422.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\220425.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\220426.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\220428.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\220513.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\220514.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\220517.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\220520.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\220622.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\220627.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\220714.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\220727.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\220728.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\220801.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\220802.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\220807.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\220814.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\220822.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\220901.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\220909.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\221026.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\221029.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\221101.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\221103.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\221104.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\221222.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\230106.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\230227.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\230228.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\230323.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\230417.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\230429.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\230512.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\230513.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\230515.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\230519.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\230531.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\230615.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\230616.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\230706.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\230707.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\230717.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\230801.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\230818.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\230828.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\230901.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\230911.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\230914.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\230919.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\231008.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\231018.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\231019.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\231020.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\231021.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\231026.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\231031.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\231106.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\231107.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\231108.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\231109.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\231120.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\231201.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\231209.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\231210.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\231212.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\231219.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\231220.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\231221.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\231222.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\240105.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\240108.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\240117.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\240122.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\240121.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\240131.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\240204.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\240322.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\240205.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\240207.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\240208.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\240227.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\240226.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\240311.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\240312.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\240318.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\240319.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\240320.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\240323.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\240324.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\240326.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\240329.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\240407.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\240412.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\240416.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\240417.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\240422.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\240429.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\240506.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\240511.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\240515.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\240516.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\240529.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\240613.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\240619.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\240621.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\240626.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\240709.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\240716.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\240720.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\240721.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\240819.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\240821.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\240904.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\240927.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\241031.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\241118.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\241121.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\241125.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\241129.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\241216.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\241217.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\241218.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\250106.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\250109.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\250217.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\250225.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\250226.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\250301.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\250302.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\250304.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="通辽市民卡Dll\dcrf32idcardimagebuild.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="通辽市民卡Dll\dcsdtapi.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="通辽市民卡Dll\dc_pboc.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="通辽市民卡Dll\HealthyCarder.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="通辽市民卡Dll\HS_Reader.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="通辽市民卡Dll\JPG.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="通辽市民卡Dll\wltrs_getbmp.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="通辽市民卡Dll\ZTReadCard.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="..\Common\log4net.config">
      <Link>log4net.config</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <None Include="app.config">
      <SubType>Designer</SubType>
    </None>
    <Content Include="data\CodeConfig.xml">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="img\Icon_1469.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="img\insert.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="img\关闭审阅.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="img\关闭痕迹.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="img\开启审阅.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="img\开启痕迹.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="img\打印.bmp">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Resources\insert.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="背景.jpg">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Conf\CodeConfig.xml">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="DDTek.lic">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Conf\Conf.dat">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Conf\Conn.dat">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="EmrMb.ztemr">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="BasyExport.dbf">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="BasyExport_N41.dbf">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <None Include="My Project\Application.myapp">
      <Generator>MyApplicationCodeGenerator</Generator>
      <LastGenOutput>Application.Designer.vb</LastGenOutput>
    </None>
    <None Include="My Project\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <CustomToolNamespace>My</CustomToolNamespace>
      <SubType>Designer</SubType>
      <LastGenOutput>Settings1.Designer.vb</LastGenOutput>
    </None>
    <None Include="MyKey.pfx" />
    <Content Include="HuaDaJkkDLL\BankNo.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="HuaDaJkkDLL\HD300_V1.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="HuaDaJkkDLL\SSSE32.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="HuaDaJkkDLL\WSB_RWInterface.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="HuaDaJkkDLL\WSHealthyTCarder.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <None Include="packages.config" />
    <None Include="Resources\close_16px_1170413_easyicon.net.png" />
    <None Include="Resources\close_hide_16px_10915_easyicon.net.png" />
    <None Include="Resources\window_close_16px_509835_easyicon.net.png" />
    <None Include="Resources\帮助32.png" />
    <None Include="Resources\背景色32.png" />
    <None Include="Resources\个人设置32.png" />
    <None Include="Resources\说明32.png" />
    <None Include="Resources\退出32.png" />
    <None Include="Resources\Icon_1469.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <Content Include="Rpt\新农合门诊统筹补偿单（丰南）.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\住院押金缴费单%28丰南%29.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\卫生室采购计划汇总.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\卫生室采购计划详单.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\唐山门诊发票%28汇总%29.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\患者费用统计表.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\药品销售统计表.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\输液卡.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\输液卡（连打）.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="TemperatureChart.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\医技科室收入统计.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\催款单.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\药房台账汇总.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\药房台账统计.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\药库台账汇总.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\药库台账统计.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\门诊统筹月结 %28一般诊疗%29.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\新农合住院补偿单.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\新农合出院即报明细.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\新农合出院即报月结单.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\诊疗卡.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\门诊处方表%28二分之一A4%29.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\住院处方表%28二分之一A4%29.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\药库药房出入库查询清单简化.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\药库药房出入库查询药品汇总.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\药库药房出入库查询票据汇总.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\药库药房出入库查询客户汇总.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\药库药房退供应商查询客户汇总.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\药库药房退供应商查询清单简化.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\药库药房退供应商查询票据汇总.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\药库药房退供应商查询药品汇总.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\药库药房报损报益表.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\药库药房盘点表.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\药库药房盈亏表.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\辽宁省住院收费票据.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\辽宁省住院收费票据老版.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\辽宁省门诊收费票据.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\辽宁省门诊收费票据老版.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\患者用药清单热敏打印.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\新农合门诊统筹补偿单（葫芦岛）.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\打印农合本.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\新农合门诊统筹补偿单（玉田）.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\新农合门诊统筹补偿单（迁西）.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\健康卡结账.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\门诊统筹月结（丰南个人账户）.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\门诊统筹月结明细（卫生室丰南个人账户）.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\诊疗字典.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\健康卡交易单.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\健康卡交易明细表.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\健康卡充值打印.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\患者用药清单热敏打印75mm.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\药房库存表（带采购价）.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\输液卡（丰南连打）.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\新农合门诊统筹补偿单（灯塔）.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\新农合门诊统筹补偿单（灯塔民政）.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\住院日结横打.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\2013年河北省医保住院收费票据.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\2013年河北省医保门诊收费票据.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\药房门诊发药汇总表.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\物资盘点表.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\物资移库表.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\物资出库单.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\物资采购入库单.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\医保日结.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\物资退库表.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\物资支领表.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\物资其他入库单.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\2013年河北省医保住院收费票据居民.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\2013年河北省医保住院收费票据职工.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\2013年河北省医保门诊收费票据居民.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\2013年河北省医保门诊收费票据职工.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\门诊日结new.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\门诊处方表%28二分之一A4%29new.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\输液卡（长春连打）.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\套打处方%28长春%29.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\医保处方%28长春%29.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\检验报告单.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\门诊处方表%28二分之一A4%29new%28竖版%29.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\药房项目表%28带明细%29.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\药房项目表.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\内蒙古通辽市医院住院收费专用票.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\内蒙古通辽市门诊收费专用票据.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\通辽市医疗保险住院费用结算单.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\通辽市医疗保险门诊费用结算单.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\通辽市医疗机构住院专用收据.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\内蒙古医保对账.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\内蒙古通辽市霍林河门诊收费专用票据.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\子报表母表new.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\通辽市医疗保险住院费用结算单%28居民%29.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\通辽市医疗保险住院费用结算单%28职工%29.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="通辽市民卡Dll\ZTReadCard.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="通辽市民卡Dll\ZTreadCard\1001ZTReadCard.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="通辽市民卡Dll\ZTreadCard\2001ZTReadCard.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="通辽市民卡Dll\ZTreadCard\2002ZTReadCard.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="通辽市民卡Dll\ZTreadCard\2003ZTReadCard.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="通辽市民卡Dll\ZTreadCard\2004ZTReadCard.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="通辽市民卡Dll\ZTreadCard\2601ZTReadCard.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="通辽市民卡Dll\ZTreadCard\2121ZTReadCard.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="通辽市民卡Dll\ZTreadCard\2101ZTReadCard.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="通辽市民卡Dll\ZTreadCard\2110ZTReadCard.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="通辽市民卡Dll\ZTreadCard\2130ZTReadCard.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="通辽市民卡Dll\ZTreadCard\2104ZTReadCard.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="通辽市民卡Dll\ZTreadCard\2109ZTReadCard.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="通辽市民卡Dll\ZTreadCard\2115ZTReadCard.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="通辽市民卡Dll\ZTreadCard\2124ZTReadCard.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="通辽市民卡Dll\ZTreadCard\2126ZTReadCard.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="通辽市民卡Dll\ZTreadCard\2012ZTReadCard.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="通辽市民卡Dll\ZTreadCard\2024ZTReadCard.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="通辽市民卡Dll\ZTreadCard\2307ZTReadCard.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="通辽市民卡Dll\ZTreadCard\2310ZTReadCard.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="通辽市民卡Dll\ZTreadCard\2315ZTReadCard.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="通辽市民卡Dll\ZTreadCard\2316ZTReadCard.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="通辽市民卡Dll\ZTreadCard\2317ZTReadCard.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="通辽市民卡Dll\ZTreadCard\2318ZTReadCard.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="通辽市民卡Dll\ZTreadCard\2402ZTReadCard.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="通辽市民卡Dll\ZTreadCard\2404ZTReadCard.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="通辽市民卡Dll\ZTreadCard\2409ZTReadCard.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="通辽市民卡Dll\ZTreadCard\2415ZTReadCard.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="通辽市民卡Dll\ZTreadCard\2416ZTReadCard.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="通辽市民卡Dll\ZTreadCard\0503ZTReadCard.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="通辽市民卡Dll\ZTreadCard\2010ZTReadCard.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="通辽市民卡Dll\ZTreadCard\2018ZTReadCard.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="通辽市民卡Dll\ZTreadCard\2023ZTReadCard.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="通辽市民卡Dll\ZTreadCard\2030ZTReadCard.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="通辽市民卡Dll\ZTreadCard\2037ZTReadCard.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="通辽市民卡Dll\ZTreadCard\2013ZTReadCard.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="通辽市民卡Dll\ZTreadCard\2025ZTReadCard.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="通辽市民卡Dll\ZTreadCard\2026ZTReadCard.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="通辽市民卡Dll\ZTreadCard\2305ZTReadCard.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="通辽市民卡Dll\ZTreadCard\2304ZTReadCard.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="通辽市民卡Dll\ZTreadCard\2321ZTReadCard.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\门诊日结操作员现金统计new.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="通辽市民卡Dll\ZTreadCard\2628ZTReadCard.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="通辽市民卡Dll\ZTreadCard\2036ZTReadCard.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="通辽市民卡Dll\ZTreadCard\2602ZTReadCard.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\合力康医院发票.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\医保对账单.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\医保本地数据对账明细.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="通辽市民卡Dll\ZTreadCard\2403ZTReadCard.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\居民医保对账单.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\左中门诊发票样式.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="通辽市民卡Dll\ZTreadCard\2040ZTReadCard.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\内蒙古通辽市门诊收费专用票据%28不套打%29.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\内蒙古通辽市医院住院收费专用票2.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\通辽市医疗保险住院费用结算单%28居民普通%29.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\通辽市医疗保险住院费用结算单%28职工普通%29.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\通辽市医疗保险门诊费用结算单%28普通%29.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\中医病案首页.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\西医病案首页.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <None Include="Rpt\内蒙古通辽市开鲁县门诊收费专用票据.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <Content Include="Rpt\内蒙古通辽市开鲁县门诊收费专用票据new.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="YBCardDLL\NMG\MTConfig.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="YBCardDLL\NMG\SiCard.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="YBCardDLL\LN\host_socket.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\内蒙古通辽市科左中旗医院住院收费专用票据.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="通辽市民卡Dll\ZTreadCard\2123ZTReadCard.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="YBCardDLL\NMG\config.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="YBCardDLL\NMG\HeaSecReadInfo.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="YBCardDLL\NMG\JSONTool.lib">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="YBCardDLL\NMG\NetClient.lib">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="YBCardDLL\NMG\NetworkAgent.lib">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="YBCardDLL\NMG\ToolLib.lib">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="YBCardDLL\JL\FL_SFZREADER.INI">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="YBCardDLL\JL\Local.data">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="YBCardDLL\JL\NationEC.db">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="YBCardDLL\JL\NationEcCode.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="YBCardDLL\JL\NationECCodeTest.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="YBCardDLL\JL\SiConfig.cfg">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="YBCardDLL\JL\SSSE32.lib">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="YBCardDLL\JL\termb.lic">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="YBCardDLL\JL\unins000.dat">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="YBCardDLL\NMG\mwIDdll\license.dat">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Cloud.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
  </ItemGroup>
  <ItemGroup>
    <Content Include="44副本.ico" />
    <Content Include="img\09553.ico">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="img\09574.ico">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="IDReadDevDLL\PuTian\License.dat">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="IDReadDevDLL\PuTian\cardapi3.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="IDReadDevDLL\PuTian\sdtapi.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="IDReadDevDLL\PuTian\WltRS.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="sound\msg.wav">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="sound\notify.wav">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <None Include="My Project\app.manifest" />
    <Content Include="System.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\住院床位统计.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\药库调拨药房表.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\药库入库单.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\药品科室支领表.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\药库批发表.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\药库退供应商表.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\药房退药库表.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\药房日结.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\在院患者查询.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\药房批发表.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\药房入库单.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\住院押金缴费单.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\盘点手抄单.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\患者用药清单标准版.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\患者用药清单简化版.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\药库调价查询.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\药品警戒线查询.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\医院工作量统计.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\门诊日结.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\住院日结.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\住院日结汇总母表.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\患者用药清单标准版二.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\患者用药清单简化版二.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\住院日结患者押金.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\住院日结操作员现金统计.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\子报表母表.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\门诊日结操作员现金统计.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\邯郸曲周门诊票据.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\护士站汇总领药.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\唐山门诊发票.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\患者类别统计表.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\药房住院发药汇总.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\内蒙古通辽住院票据.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\内蒙古通辽门诊票据.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\吉林省医疗机构住院收费专用票据.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\吉林省医疗机构门诊收费专用票据.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\收入汇总表.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\医院费用统计.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\支出汇总表.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\出院明细表.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\物资出入库信息查询.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\物资库存信息查询.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\药品入库情况汇总表.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\门诊统筹日结.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\门诊统筹日结明细.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\门诊统筹月结.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <SubType>Designer</SubType>
    </Content>
    <Content Include="Rpt\门诊统筹补偿明细.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\门诊统筹月结明细（卫生室）.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\新农合门诊统筹补偿单.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\科室日报表.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\入院患者统计.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\门诊挂号票据.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\住院处方表.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\门诊处方表.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\2013年河北省住院收费票据.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\2013年河北省门诊收费票据.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\一般诊疗费用报表.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\新型合作医疗门诊统筹补偿单（丰润%29.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="XzxDkq116D\License.dat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="XzxDkq116D\sdtapi.dll">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="XzxDkq116D\SynIDCardAPI.dll">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="XzxDkq116D\WltRS.dll">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="zh-CHS.xml">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="唐山His2010帮助文档.chm">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="sound\Windows Error.wav">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Yy_Db.mdb">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="更新说明.txt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="电子病历模版.mdb">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
  </ItemGroup>
  <ItemGroup>
    <PublishFile Include="ActiveReports.Chart">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="ActiveReports.Document">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="ActiveReports.Viewer6">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="ActiveReports6">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="Conf\CodeConfig.xml">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>File</FileType>
    </PublishFile>
    <PublishFile Include="data\CodeConfig.xml">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>File</FileType>
    </PublishFile>
    <PublishFile Include="NPOI">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="Stimulsoft.Base">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="Stimulsoft.Controls">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="Stimulsoft.Controls.Win">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="Stimulsoft.Design">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="Stimulsoft.Editor">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="Stimulsoft.Report">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="Stimulsoft.Report.Check">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="Stimulsoft.Report.Design">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="Stimulsoft.Report.Helper">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="Stimulsoft.Report.Win">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="Yy_Db.mdb">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>File</FileType>
    </PublishFile>
    <PublishFile Include="zh-CHS.xml">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>File</FileType>
    </PublishFile>
    <PublishFile Include="电子病历模版.mdb">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>File</FileType>
    </PublishFile>
  </ItemGroup>
  <ItemGroup>
    <BootstrapperPackage Include=".NETFramework,Version=v4.0">
      <Visible>False</Visible>
      <ProductName>Microsoft .NET Framework 4 %28x86 和 x64%29</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Client.3.5">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1 Client Profile</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.2.0">
      <Visible>False</Visible>
      <ProductName>.NET Framework 2.0 %28x86%29</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.3.0">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.0 %28x86%29</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.3.5">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.3.5.SP1">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Windows.Installer.4.5">
      <Visible>False</Visible>
      <ProductName>Windows Installer 4.5</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\BaseClass\BaseClass.vbproj">
      <Project>{48964D0C-25C0-413C-A94A-C2246ADE46A1}</Project>
      <Name>BaseClass</Name>
    </ProjectReference>
    <ProjectReference Include="..\BaseFunc\BaseFunc.vbproj">
      <Project>{C7865854-F754-41AC-9912-829068BE5C2A}</Project>
      <Name>BaseFunc</Name>
    </ProjectReference>
    <ProjectReference Include="..\BLLOld\BLLOld.csproj">
      <Project>{42c23254-ba1f-4222-895b-276fc06bf59c}</Project>
      <Name>BLLOld</Name>
    </ProjectReference>
    <ProjectReference Include="..\BLL\BLL.csproj">
      <Project>{46b795c2-6efa-41e6-948e-66f92e591b6a}</Project>
      <Name>BLL</Name>
    </ProjectReference>
    <ProjectReference Include="..\Common.BaseForm\Common.BaseForm.csproj">
      <Project>{1dd7020c-8603-438a-8015-34702dabc229}</Project>
      <Name>Common.BaseForm</Name>
    </ProjectReference>
    <ProjectReference Include="..\Common.Delegate\Common.Delegate.csproj">
      <Project>{943ed6dc-c1fb-42fa-b543-9afaa67ba7c3}</Project>
      <Name>Common.Delegate</Name>
    </ProjectReference>
    <ProjectReference Include="..\Common.WinFormVar\Common.WinFormVar.csproj">
      <Project>{e267bdd2-634a-405b-bdbf-55354adbc027}</Project>
      <Name>Common.WinFormVar</Name>
    </ProjectReference>
    <ProjectReference Include="..\Common\Common.csproj">
      <Project>{92e350a0-3691-4b8d-a07e-ebb0f10e6997}</Project>
      <Name>Common</Name>
    </ProjectReference>
    <ProjectReference Include="..\CustomControl\CustomControl.csproj">
      <Project>{12BF4168-D60E-4A6C-85BF-926130EE6A6D}</Project>
      <Name>CustomControl</Name>
    </ProjectReference>
    <ProjectReference Include="..\CustomSunnyUI\CustomSunnyUI.csproj">
      <Project>{e12e3d1c-d546-447d-9cd7-1ac63e8d7f18}</Project>
      <Name>CustomSunnyUI</Name>
    </ProjectReference>
    <ProjectReference Include="..\DAL\DAL.csproj">
      <Project>{C0DAB999-F761-4901-BE5B-C542365756A6}</Project>
      <Name>DAL</Name>
    </ProjectReference>
    <ProjectReference Include="..\DbProviderFactory\DbProviderFactory.csproj">
      <Project>{fdf5d6d6-d281-4884-a81a-d0c49c2f3bc7}</Project>
      <Name>DbProviderFactory</Name>
    </ProjectReference>
    <ProjectReference Include="..\DTO\DTO.csproj">
      <Project>{cca47e9e-8991-4e5e-b341-1e313499e38e}</Project>
      <Name>DTO</Name>
    </ProjectReference>
    <ProjectReference Include="..\ERX\ERX.csproj">
      <Project>{472bfece-5fdb-4eb3-8973-f9734faf896b}</Project>
      <Name>ERX</Name>
    </ProjectReference>
    <ProjectReference Include="..\E_InvoiceAPI\E_InvoiceAPI.csproj">
      <Project>{19583ba6-3c10-4256-b889-fd5b80652f4e}</Project>
      <Name>E_InvoiceAPI</Name>
    </ProjectReference>
    <ProjectReference Include="..\E_Invoice\E_Invoice.csproj">
      <Project>{c05ddd2a-8706-41c7-9b11-77f87d964191}</Project>
      <Name>E_Invoice</Name>
    </ProjectReference>
    <ProjectReference Include="..\HisControl\HisControl.vbproj">
      <Project>{EF778791-6E30-4A07-83B9-2D73FD08810A}</Project>
      <Name>HisControl</Name>
    </ProjectReference>
    <ProjectReference Include="..\HisPara\HisPara.vbproj">
      <Project>{3E790840-B7EB-4875-A334-D0386A5633CB}</Project>
      <Name>HisPara</Name>
    </ProjectReference>
    <ProjectReference Include="..\HisVar\HisVar.vbproj">
      <Project>{4DAD5E14-6224-46B2-886D-6D22CE3886BC}</Project>
      <Name>HisVar</Name>
    </ProjectReference>
    <ProjectReference Include="..\IDBUtility\IDBUtility.csproj">
      <Project>{ccbb5cb6-0871-4d97-9eb3-a68b44cb7e86}</Project>
      <Name>IDBUtility</Name>
    </ProjectReference>
    <ProjectReference Include="..\ModelOld\ModelOld.csproj">
      <Project>{1FAF80FE-D42E-4FEB-853A-B9846C1862AE}</Project>
      <Name>ModelOld</Name>
    </ProjectReference>
    <ProjectReference Include="..\Model\Model.csproj">
      <Project>{3fb6ea13-2c32-4d08-a426-c22224f72121}</Project>
      <Name>Model</Name>
    </ProjectReference>
    <ProjectReference Include="..\PublicForm\PublicForm.vbproj">
      <Project>{C2D40F84-CBD2-4BB9-92CF-C995FC2F4A25}</Project>
      <Name>PublicForm</Name>
    </ProjectReference>
    <ProjectReference Include="..\Resources\MyResources.vbproj">
      <Project>{52FE1A23-CE20-42DA-8AFD-1B47B2BBCCC7}</Project>
      <Name>MyResources</Name>
    </ProjectReference>
    <ProjectReference Include="..\SQLServerDAL\SQLServerDAL.csproj">
      <Project>{ac6eb101-399f-43a9-93cd-e4cac537fc45}</Project>
      <Name>SQLServerDAL</Name>
    </ProjectReference>
    <ProjectReference Include="..\Utilities\Utilities.csproj">
      <Project>{ddef90d7-bcf0-4e30-9fce-3bb2d493565d}</Project>
      <Name>Utilities</Name>
    </ProjectReference>
    <ProjectReference Include="..\YBBLL\YBBLL.csproj">
      <Project>{41e5b5ba-abbd-445c-90b1-ce381afbc5ce}</Project>
      <Name>YBBLL</Name>
    </ProjectReference>
    <ProjectReference Include="..\YBModel\YBModel.csproj">
      <Project>{3cb4dc25-92a0-49f4-a946-d9988b409c7c}</Project>
      <Name>YBModel</Name>
    </ProjectReference>
    <ProjectReference Include="..\ZtHis.Emr\ZtHis.Emr.vbproj">
      <Project>{D05310B8-9C6A-4961-8664-8133AE171F15}</Project>
      <Name>ZtHis.Emr</Name>
    </ProjectReference>
    <ProjectReference Include="..\ZtHis.Materials\ZtHis.Materials.vbproj">
      <Project>{DB1F7C8F-F366-440A-BE79-905B41075418}</Project>
      <Name>ZtHis.Materials</Name>
    </ProjectReference>
    <ProjectReference Include="..\ZTHisBaseDict\ZTHisBaseDict.csproj">
      <Project>{1ae32808-6380-43c1-ac61-a89605f1229e}</Project>
      <Name>ZTHisBaseDict</Name>
    </ProjectReference>
    <ProjectReference Include="..\ZTHisControl\ZTHisControl.csproj">
      <Project>{3CC56B11-C172-4753-89C8-EEA972402626}</Project>
      <Name>ZTHisControl</Name>
    </ProjectReference>
    <ProjectReference Include="..\ZTHisDrugStore\ZTHisDrugStore.csproj">
      <Project>{772b7b3c-3fc6-4bcd-828d-ec38ac2e14dc}</Project>
      <Name>ZTHisDrugStore</Name>
    </ProjectReference>
    <ProjectReference Include="..\ZTHisEnum\ZTHisEnum.csproj">
      <Project>{940cdbcc-e9a4-4771-be47-343404a40123}</Project>
      <Name>ZTHisEnum</Name>
    </ProjectReference>
    <ProjectReference Include="..\ZTHisFinance\ZTHisFinance.csproj">
      <Project>{fa89a96b-3bde-4c46-b4e2-614a86b7aee2}</Project>
      <Name>ZTHisFinance</Name>
    </ProjectReference>
    <ProjectReference Include="..\ZTHisInpatient\ZTHisInpatient.csproj">
      <Project>{d79bb10e-64c3-4865-bee0-2d72454f47df}</Project>
      <Name>ZTHisInpatient</Name>
    </ProjectReference>
    <ProjectReference Include="..\ZTHisInsuranceAPI\ZTHisInsuranceAPI.csproj">
      <Project>{90f6db9b-772d-412e-8e79-c403cc58b9af}</Project>
      <Name>ZTHisInsuranceAPI</Name>
    </ProjectReference>
    <ProjectReference Include="..\ZTHisInsurance\ZTHisInsurance.csproj">
      <Project>{f6740b5c-0483-4def-9ede-849dae0923e0}</Project>
      <Name>ZTHisInsurance</Name>
    </ProjectReference>
    <ProjectReference Include="..\ZTHisLis\ZTHisLis.csproj">
      <Project>{e800477a-4ffe-4308-a238-44b853fb32fb}</Project>
      <Name>ZTHisLis</Name>
    </ProjectReference>
    <ProjectReference Include="..\ZTHisMaterials\ZTHisMaterials.csproj">
      <Project>{dfdcc0a6-19ca-4927-ad3d-21b371c413dd}</Project>
      <Name>ZTHisMaterials</Name>
    </ProjectReference>
    <ProjectReference Include="..\ZTHisNurse\ZTHisNurse.csproj">
      <Project>{37857e11-1d23-4c1e-9180-cf588a3a0885}</Project>
      <Name>ZTHisNurse</Name>
    </ProjectReference>
    <ProjectReference Include="..\ZTHisOutpatient\ZTHisOutpatient.csproj">
      <Project>{0b858e15-ed2e-45d4-96c2-8a2ff0364fc5}</Project>
      <Name>ZTHisOutpatient</Name>
    </ProjectReference>
    <ProjectReference Include="..\ZTHisPara\ZTHisPara.csproj">
      <Project>{9ca37597-119c-4f39-8063-effc91c2b20d}</Project>
      <Name>ZTHisPara</Name>
    </ProjectReference>
    <ProjectReference Include="..\ZTHisPharmacy\ZTHisPharmacy.csproj">
      <Project>{62d0ec07-80e2-42f8-ac5c-d64b2e37985a}</Project>
      <Name>ZTHisPharmacy</Name>
    </ProjectReference>
    <ProjectReference Include="..\ZTHisPublicForm\ZTHisPublicForm.csproj">
      <Project>{CDF3F32B-E89E-4761-AC07-991BCC2DD00B}</Project>
      <Name>ZTHisPublicForm</Name>
    </ProjectReference>
    <ProjectReference Include="..\ZTHisPublicFunction\ZTHisPublicFunction.csproj">
      <Project>{484f5b0c-f19f-448d-b819-e183bfc2fd96}</Project>
      <Name>ZTHisPublicFunction</Name>
    </ProjectReference>
    <ProjectReference Include="..\ZTHisResources\ZTHisResources.csproj">
      <Project>{e7e57f38-534a-4aea-841e-9a869e3738ff}</Project>
      <Name>ZTHisResources</Name>
    </ProjectReference>
    <ProjectReference Include="..\ZTHisSysManage\ZTHisSysManage.csproj">
      <Project>{54091883-6bed-4cb5-95d5-2c77d11ebf45}</Project>
      <Name>ZTHisSysManage</Name>
    </ProjectReference>
    <ProjectReference Include="..\ZTHisVar\ZTHisVar.csproj">
      <Project>{26bf0cc0-ae2a-459a-b41b-73f691f5299d}</Project>
      <Name>ZTHisVar</Name>
    </ProjectReference>
    <ProjectReference Include="..\护士站\护士站.vbproj">
      <Project>{392EA0BC-9A6E-4FE5-B87E-591EEE67BF2F}</Project>
      <Name>护士站</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <COMReference Include="AxNsoOfficeLib">
      <Guid>{B21A69CE-901E-42FF-8F96-39616D2F16D1}</Guid>
      <VersionMajor>1</VersionMajor>
      <VersionMinor>0</VersionMinor>
      <Lcid>0</Lcid>
      <WrapperTool>aximp</WrapperTool>
      <Isolated>False</Isolated>
    </COMReference>
    <COMReference Include="NsoOfficeLib">
      <Guid>{B21A69CE-901E-42FF-8F96-39616D2F16D1}</Guid>
      <VersionMajor>1</VersionMajor>
      <VersionMinor>0</VersionMinor>
      <Lcid>0</Lcid>
      <WrapperTool>tlbimp</WrapperTool>
      <Isolated>False</Isolated>
      <EmbedInteropTypes>True</EmbedInteropTypes>
    </COMReference>
  </ItemGroup>
  <ItemGroup />
  <Import Project="$(MSBuildToolsPath)\Microsoft.VisualBasic.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>