﻿/**  版本信息模板在安装目录下，可自行修改。
* M_Materials_Check2.cs
*
* 功 能： N/A
* 类 名： M_Materials_Check2
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2016-11-26 11:27:50   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
namespace ModelOld
{
	/// <summary>
	/// 物资盘点从表
	/// </summary>
	[Serializable]
	public partial class M_Materials_Check2
	{
		public M_Materials_Check2()
		{}
		#region Model
		private string _m_check_code;
		private string _materials_code;
		private string _materialsstock_code;
		private string _m_check_detail_code;
		private string _materialslot;
		private DateTime? _materialsexpirydate;
        private string _materials_spec;
		private decimal? _m_paper_num;
		private decimal? _m_real_num;
		private decimal? _m_check_num;
		private decimal? _m_check_price;
		private decimal? _m_check_money;
		private string _m_checkdetail_memo;
		/// <summary>
		/// yyMMdd+5位流水号
		/// </summary>
		public string M_Check_Code
		{
			set{ _m_check_code=value;}
			get{return _m_check_code;}
		}
		/// <summary>
		/// 物资编码
		/// </summary>
		public string Materials_Code
		{
			set{ _materials_code=value;}
			get{return _materials_code;}
		}
		/// <summary>
		/// 物资编码+6位流水号
		/// </summary>
		public string MaterialsStock_Code
		{
			set{ _materialsstock_code=value;}
			get{return _materialsstock_code;}
		}
		/// <summary>
		/// 盘点明细编码
		/// </summary>
		public string M_Check_Detail_Code
		{
			set{ _m_check_detail_code=value;}
			get{return _m_check_detail_code;}
		}
		/// <summary>
		/// 物资批号
		/// </summary>
		public string MaterialsLot
		{
			set{ _materialslot=value;}
			get{return _materialslot;}
		}
		/// <summary>
		/// 物资有效期
		/// </summary>
		public DateTime? MaterialsExpiryDate
		{
			set{ _materialsexpirydate=value;}
			get{return _materialsexpirydate;}
		}
        /// <summary>
        /// 物资规格
        /// </summary>
        public string Materials_Spec
        {
            set { _materials_spec  = value; }
            get { return _materials_spec; }
        }

		/// <summary>
		/// 账面库存
		/// </summary>
		public decimal? M_Paper_Num
		{
			set{ _m_paper_num=value;}
			get{return _m_paper_num;}
		}
		/// <summary>
		/// 实际库存
		/// </summary>
		public decimal? M_Real_Num
		{
			set{ _m_real_num=value;}
			get{return _m_real_num;}
		}
		/// <summary>
		/// 实际减去账面
		/// </summary>
		public decimal? M_Check_Num
		{
			set{ _m_check_num=value;}
			get{return _m_check_num;}
		}
		/// <summary>
		/// 单价
		/// </summary>
		public decimal? M_Check_Price
		{
			set{ _m_check_price=value;}
			get{return _m_check_price;}
		}
		/// <summary>
		/// 金额
		/// </summary>
		public decimal? M_Check_Money
		{
			set{ _m_check_money=value;}
			get{return _m_check_money;}
		}
		/// <summary>
		/// 备注
		/// </summary>
		public string M_CheckDetail_Memo
		{
			set{ _m_checkdetail_memo=value;}
			get{return _m_checkdetail_memo;}
		}
		#endregion Model

	}
}

