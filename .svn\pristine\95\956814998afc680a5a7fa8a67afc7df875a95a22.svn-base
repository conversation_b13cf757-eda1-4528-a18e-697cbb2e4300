﻿<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<StiSerializer version="1.02" type="Net" application="StiReport">
  <Dictionary Ref="1" type="Dictionary" isKey="true">
    <BusinessObjects isList="true" count="0" />
    <Databases isList="true" count="0" />
    <DataSources isList="true" count="0" />
    <Relations isList="true" count="0" />
    <Report isRef="0" />
    <Variables isList="true" count="42">
      <value>,姓名,姓名,System.String,,False,False</value>
      <value>,社会保障号,社会保障号,System.String,,False,False</value>
      <value>,病历号,病历号,System.String,,False,False</value>
      <value>,凭单号,凭单号,System.String,,False,False</value>
      <value>,医院名称,医院名称,System.String,,False,False</value>
      <value>,科别,科别,System.String,,False,False</value>
      <value>,人员类别,人员类别,System.String,,False,False</value>
      <value>,入院年,入院年,System.String,,False,False</value>
      <value>,入院月,入院月,System.String,,False,False</value>
      <value>,入院日,入院日,System.String,,False,False</value>
      <value>,基本统筹基金支付,基本统筹基金支付,System.String,,False,False</value>
      <value>,出院年,出院年,System.String,,False,False</value>
      <value>,出院月,出院月,System.String,,False,False</value>
      <value>,出院日,出院日,System.String,,False,False</value>
      <value>,住院天数,住院天数,System.String,,False,False</value>
      <value>,住院次数,住院次数,System.String,,False,False</value>
      <value>,起付线,起付线,System.String,,False,False</value>
      <value>,大额统筹基金支付,大额统筹基金支付,System.String,,False,False</value>
      <value>,床位费,床位费,System.String,,False,False</value>
      <value>,西药费,西药费,System.String,,False,False</value>
      <value>,中药费,中药费,System.String,,False,False</value>
      <value>,检查费,检查费,System.String,,False,False</value>
      <value>,治疗费,治疗费,System.String,,False,False</value>
      <value>,公务员补助支付,公务员补助支付,System.String,,False,False</value>
      <value>,化验费,化验费,System.String,,False,False</value>
      <value>,手术费,手术费,System.String,,False,False</value>
      <value>,材料费,材料费,System.String,,False,False</value>
      <value>,其他,其他,System.String,,False,False</value>
      <value>,变量1,变量1,System.String,,False,False</value>
      <value>,变量2,变量2,System.String,,False,False</value>
      <value>,统筹基金支付合计,统筹基金支付合计,System.String,,False,False</value>
      <value>,住院费用大写,住院费用大写,System.String,,False,False</value>
      <value>,住院费用,住院费用,System.String,,False,False</value>
      <value>,个人账户支付,个人账户支付,System.String,,False,False</value>
      <value>,个人账户余额,个人账户余额,System.String,,False,False</value>
      <value>,预交住院押金,预交住院押金,System.String,,False,False</value>
      <value>,自理费用合计,自理费用合计,System.String,,False,False</value>
      <value>,退补住院押金,退补住院押金,System.String,,False,False</value>
      <value>,收款,收款,System.String,,False,False</value>
      <value>,结算,结算,System.String,,False,False</value>
      <value>,领取,领取,System.String,,False,False</value>
      <value>,出单日期,出单日期,System.String,,False,False</value>
    </Variables>
  </Dictionary>
  <EngineVersion>EngineV2</EngineVersion>
  <GlobalizationStrings isList="true" count="0" />
  <MetaTags isList="true" count="0" />
  <Pages isList="true" count="1">
    <Page1 Ref="2" type="Page" isKey="true">
      <Border>None;Black;2;Solid;False;4;Black</Border>
      <Brush>Transparent</Brush>
      <Components isList="true" count="39">
        <PageHeaderBand1 Ref="3" type="PageHeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,0.4,19,0.81</ClientRectangle>
          <Components isList="true" count="4">
            <Text1 Ref="4" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0.8,0.6,2,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,8</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text1</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{姓名}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text1>
            <Text2 Ref="5" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>4.61,0.6,2.9,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,8</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text2</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{社会保障号}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text2>
            <Text3 Ref="6" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>7.48,0.6,1.62,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,8</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text3</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{病历号}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text3>
            <Text4 Ref="7" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>10.5,0.6,2,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,8</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text4</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{凭单号}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text4>
          </Components>
          <Conditions isList="true" count="0" />
          <Name>PageHeaderBand1</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
        </PageHeaderBand1>
        <Text5 Ref="8" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>1.6,1.5,4.8,0.5</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text5</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{医院名称}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text5>
        <Text6 Ref="9" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>8.2,1.5,1.7,0.5</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text6</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{科别}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text6>
        <Text7 Ref="10" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>11.5,1.5,4.5,0.5</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text7</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{人员类别}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text7>
        <Text8 Ref="11" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>1.6,2,1.2,0.5</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text8</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{入院年}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text8>
        <Text9 Ref="12" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>3,2,0.4,0.5</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text9</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{入院月}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text9>
        <Text10 Ref="13" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>3.6,2,0.4,0.5</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text10</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{入院日}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text10>
        <Text11 Ref="14" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>12.9,2,1.5,0.5</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text11</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{基本统筹基金支付}</Text>
          <TextBrush>Black</TextBrush>
        </Text11>
        <Text12 Ref="15" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>1.6,2.5,1.2,0.5</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text12</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{出院年}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text12>
        <Text13 Ref="16" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>3,2.5,0.4,0.5</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text13</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{出院月}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text13>
        <Text14 Ref="17" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>3.6,2.5,0.4,0.5</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text14</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{出院日}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text14>
        <Text15 Ref="18" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>4.65,2.5,2.2,0.5</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text15</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{住院天数}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text15>
        <Text16 Ref="19" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>6.35,2.5,1.7,0.5</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text16</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{住院次数}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text16>
        <Text17 Ref="20" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>8.17,2.5,1.65,0.5</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text17</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{起付线}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text17>
        <Text18 Ref="21" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>12.9,2.5,1.5,0.5</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text18</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{大额统筹基金支付}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text18>
        <Text19 Ref="22" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>12.92,3,1.52,0.5</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text19</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{公务员补助支付}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text19>
        <Text20 Ref="23" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0.98,3.5,1.7,0.5</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text20</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{床位费}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text20>
        <Text21 Ref="24" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>4.6,3.5,1.7,0.5</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text21</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{中药费}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text21>
        <Text22 Ref="25" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>2.9,3.5,1.7,0.5</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text22</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{西药费}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text22>
        <Text23 Ref="26" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>6.4,3.5,1.7,0.5</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text23</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{检查费}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text23>
        <Text24 Ref="27" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>8.1,3.5,1.7,0.5</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text24</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{治疗费}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text24>
        <Text25 Ref="28" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>1,4.7,1.7,0.5</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text25</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{化验费}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text25>
        <Text26 Ref="29" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>2.8,4.7,1.7,0.5</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text26</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{手术费}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text26>
        <Text27 Ref="30" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>4.63,4.7,1.52,0.5</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text27</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{材料费}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text27>
        <Text28 Ref="31" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>6.45,4.7,1.7,0.5</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text28</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{其他}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text28>
        <Text29 Ref="32" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>8.2,4.1,1.7,0.5</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text29</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{变量1}</Text>
          <TextBrush>Black</TextBrush>
        </Text29>
        <Text30 Ref="33" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>8.2,4.7,1.7,0.5</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text30</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{变量2}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text30>
        <Text31 Ref="34" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>12.9,4.7,1.6,0.5</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text31</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{统筹基金支付合计}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text31>
        <Text32 Ref="35" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>1.66,5.7,5.5,0.5</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text32</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{住院费用大写}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text32>
        <Text33 Ref="36" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>7.4,5.7,2.4,0.5</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text33</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{住院费用}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text33>
        <Text34 Ref="37" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>12.9,5.3,1.52,0.5</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text34</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{个人账户支付}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text34>
        <Text35 Ref="38" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>12.9,5.9,1.52,0.5</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text35</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{个人账户余额}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text35>
        <Text36 Ref="39" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>2.5,6.3,2.5,0.5</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text36</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{预交住院押金}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text36>
        <Text37 Ref="40" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>7.5,6.3,2.5,0.5</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text37</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{自理费用合计}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text37>
        <Text38 Ref="41" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>12.9,6.4,1.52,0.5</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text38</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{退补住院押金}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text38>
        <Text39 Ref="42" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0.8,7.1,2.2,0.5</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text39</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{收款}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text39>
        <Text40 Ref="43" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>3.9,7.1,2.2,0.5</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text40</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{结算}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text40>
        <Text41 Ref="44" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>7.5,7.1,2.2,0.5</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text41</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{领取}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text41>
        <Text42 Ref="45" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>11,7.1,3,0.5</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text42</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{出单日期}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text42>
      </Components>
      <Conditions isList="true" count="0" />
      <Guid>ca212eee9fe34e53a67e1f268b7b2e4c</Guid>
      <Margins>1,1,1,1</Margins>
      <Name>Page1</Name>
      <PageHeight>29.7</PageHeight>
      <PageWidth>21</PageWidth>
      <Report isRef="0" />
      <Watermark Ref="46" type="Stimulsoft.Report.Components.StiWatermark" isKey="true">
        <Font>Arial,100</Font>
        <TextBrush>[50:0:0:0]</TextBrush>
      </Watermark>
    </Page1>
  </Pages>
  <PrinterSettings Ref="47" type="Stimulsoft.Report.Print.StiPrinterSettings" isKey="true" />
  <ReferencedAssemblies isList="true" count="8">
    <value>System.Dll</value>
    <value>System.Drawing.Dll</value>
    <value>System.Windows.Forms.Dll</value>
    <value>System.Data.Dll</value>
    <value>System.Xml.Dll</value>
    <value>Stimulsoft.Controls.Dll</value>
    <value>Stimulsoft.Base.Dll</value>
    <value>Stimulsoft.Report.Dll</value>
  </ReferencedAssemblies>
  <ReportAlias>Report</ReportAlias>
  <ReportChanged>6/11/2019 4:53:06 PM</ReportChanged>
  <ReportCreated>6/10/2019 3:42:34 PM</ReportCreated>
  <ReportFile>C:\Users\<USER>\Desktop\开发\内蒙古医保\his2010v3(长春)\his2010\Rpt\通辽市医疗机构住院专用收据.mrt</ReportFile>
  <ReportGuid>5527e645826f4a67853c87aac710528d</ReportGuid>
  <ReportName>Report</ReportName>
  <ReportUnit>Centimeters</ReportUnit>
  <ReportVersion>2011.2.1026</ReportVersion>
  <Script>using System;
using System.Drawing;
using System.Windows.Forms;
using System.Data;
using Stimulsoft.Controls;
using Stimulsoft.Base.Drawing;
using Stimulsoft.Report;
using Stimulsoft.Report.Dialogs;
using Stimulsoft.Report.Components;

namespace Reports
{
    public class Report : Stimulsoft.Report.StiReport
    {
        public Report()        {
            this.InitializeComponent();
        }

        #region StiReport Designer generated code - do not modify
        #endregion StiReport Designer generated code - do not modify
    }
}
</Script>
  <ScriptLanguage>CSharp</ScriptLanguage>
  <Styles isList="true" count="0" />
</StiSerializer>