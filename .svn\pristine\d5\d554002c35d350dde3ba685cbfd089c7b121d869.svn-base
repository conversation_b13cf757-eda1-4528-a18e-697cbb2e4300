﻿Imports System.Drawing
Imports System.Windows.Forms
Imports C1.Win.C1ChartBase
Imports Stimulsoft.Report

Public Class MaterialsUseOut

#Region "定义__变量"
    Dim My_Table As New DataTable                       '从表 普通录入表
    Dim Cb_Cm As CurrencyManager                     '同步指针
    Dim <PERSON> As Boolean
    Dim V_TotalMoney As Double
    Dim flag_Error As Boolean
    Dim M_Materials_Use_Out1 As New ModelOld.M_Materials_Use_Out1
    Dim B_Materials_Use_Out1 As New BLLOld.B_Materials_Use_Out1
    Dim B_Materials_Use_Out2 As New BLLOld.B_Materials_Use_Out2
    Dim B_Materials_Stock As New BLLOld.B_Materials_Stock
#End Region

    Public Sub New(ByVal model As ModelOld.M_Materials_Use_Out1)

        ' 此调用是设计器所必需的。
        InitializeComponent()
        ' 在 InitializeComponent() 调用之后添加任何初始化。
        M_Materials_Use_Out1 = model
    End Sub

    Private Sub MaterialsUseOut_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        Call Form_Init()
        If M_Materials_Use_Out1 Is Nothing Then
            Call Zb_Clear()
        Else
            Call Zb_Show()
        End If
        Call StatisticsDataShow()
    End Sub

#Region "窗体__事件"
    Private Sub Form_Init()
        Panel2.Height = 35
        Comm_Del.Location = New Point(100, 1)
        Comm_DelAll.Location = New Point(Comm_Del.Left + Comm_Del.Width + 2, 1)
        Comm_New.Location = New Point(Comm_DelAll.Left + Comm_DelAll.Width + 2, 1)
        Comm_Save.Location = New Point(Comm_New.Left + Comm_New.Width + 2, 1)
        Comm_Complete.Location = New Point(Comm_Save.Left + Comm_Save.Width + 2, 1)
        Comm_Print.Location = New Point(Comm_Complete.Left + Comm_Complete.Width + 2, 1)
        Comm_Search.Location = New Point(Comm_Print.Left + Comm_Print.Width + 2, 1)
        Comm_WriteOffAll.Location = New Point(Comm_Search.Left + Comm_Search.Width + 2, 1)
        Comm_WriteOffPart.Location = New Point(Comm_WriteOffAll.Left + Comm_WriteOffAll.Width + 2, 1)
        Comm_Close.Location = New Point(Comm_WriteOffPart.Left + Comm_WriteOffPart.Width + 2, 1)

        Jsr_Text.Enabled = False
        Code_Text.Enabled = False
        Jsr_Text.Text = HisVar.HisVar.JsrName
        '支领科室
        Dim B_Zd_YyKs As New BLLOld.B_Zd_YyKs
        With Ks_DtCom
            .DataView = B_Zd_YyKs.GetAllList.Tables(0).DefaultView
            .Init_Colum("Ks_Code", "科室编码", 0, "左")
            .Init_Colum("Ks_Name", "科室名称", 100, "左")
            .Init_Colum("Ks_Jc", "科室简称", 60, "左")
            .DisplayMember = "Ks_Name"
            .ValueMember = "Ks_Code"
            .DroupDownWidth = .Width
            .MaxDropDownItems = 15
            .SelectedIndex = -1
            .RowFilterTextNull = ""
            .RowFilterNotTextNull = "Ks_Jc"
        End With

        '支领人
        Dim B_Zd_YyJsr As New BLLOld.B_Zd_YyJsr
        With UseStaff_DtCom
            .DataView = B_Zd_YyJsr.GetAllList.Tables(0).DefaultView
            .Init_Colum("Jsr_Code", "支领人编码", 0, "左")
            .Init_Colum("Jsr_Name", "支领人名称", 100, "左")
            .Init_Colum("Jsr_Jc", "支领人简称", 60, "左")
            .DisplayMember = "Jsr_Name"
            .ValueMember = "Jsr_Code"
            .DroupDownWidth = .Width
            .MaxDropDownItems = 15
            .SelectedIndex = -1
            .RowFilterTextNull = ""
            .RowFilterNotTextNull = "Jsr_Jc"
        End With

        '库房
        Dim B_Materials_Warehouse_Dict As New BLLOld.B_Materials_Warehouse_Dict
        With WareHouse_DtCom
            .DataView = B_Materials_Warehouse_Dict.GetList("IsUse='1' ").Tables(0).DefaultView
            .Init_Colum("MaterialsWh_Py", "简称", 70, "左")
            .Init_Colum("MaterialsWh_Name", "名称", 80, "左")
            .Init_Colum("MaterialsWh_Code", "编码", 0, "左")
            .Init_Colum("MaterialsWh_Wb", "--", 0, "左")
            .Init_Colum("MaterialsWh_Memo", "--", 0, "左")
            .Init_Colum("Serial_No", "--", 0, "左")
            .Init_Colum("IsUse", "--", 0, "左")
            .DisplayMember = "MaterialsWh_Name"
            .ValueMember = "MaterialsWh_Code"
            .DroupDownWidth = 250
            .MaxDropDownItems = 15
            .SelectedIndex = -1
            .RowFilterTextNull = ""
            .RowFilterNotTextNull = "MaterialsWh_Py"
        End With

        '入库日期
        With Form_Date
            .CustomFormat = "yyyy-MM-dd HH:mm"
            .EditFormat = "yyyy-MM-dd HH:mm"
            .DisplayFormat = "yyyy-MM-dd HH:mm"
            .Value = Format(Now, "yyyy-MM-dd HH:mm")
        End With

    End Sub

    Private Sub BtnState()
        MyGrid_Init()
        If M_Materials_Use_Out1.OrdersStatus Is Nothing OrElse M_Materials_Use_Out1.OrdersStatus = "录入" Then
            Comm_Del.Enabled = True
            Comm_DelAll.Enabled = True
            Comm_New.Enabled = True
            Comm_Save.Enabled = True
            Comm_Complete.Enabled = True
            Comm_Print.Enabled = False
            Comm_Search.Enabled = True
            Comm_Close.Enabled = True
            Comm_WriteOffAll.Enabled = False
            Comm_WriteOffPart.Enabled = False

            MyGrid1.Tag = True
            If M_Materials_Use_Out1.WriteOffStatus = "冲销" Then
                Ks_DtCom.Enabled = False
                UseStaff_DtCom.Enabled = False
                WareHouse_DtCom.Enabled = False
            Else
                Ks_DtCom.Enabled = True
                UseStaff_DtCom.Enabled = True
                WareHouse_DtCom.Enabled = True
            End If
            Form_Date.Enabled = True
            Memo_Text.Enabled = True
            Exit Sub
        End If

        '非本人的单据不可以修改
        If M_Materials_Use_Out1.Jsr_Code <> HisVar.HisVar.JsrCode Then
            Comm_Del.Enabled = False
            Comm_DelAll.Enabled = False
            Comm_New.Enabled = True
            Comm_Save.Enabled = False
            Comm_Complete.Enabled = False
            Comm_Print.Enabled = False
            Comm_Search.Enabled = True
            Comm_Close.Enabled = True
            Comm_WriteOffAll.Enabled = False
            Comm_WriteOffPart.Enabled = False

            MyGrid1.Tag = False
            Ks_DtCom.Enabled = False
            UseStaff_DtCom.Enabled = False
            WareHouse_DtCom.Enabled = False
            Form_Date.Enabled = False
            Memo_Text.Enabled = False
            Exit Sub
        End If

        '完成的只有冲销可以使用，被冲销的一键冲销不能使用
        If M_Materials_Use_Out1.OrdersStatus = "完成" Then
            Comm_Del.Enabled = False
            Comm_DelAll.Enabled = False
            Comm_New.Enabled = True
            Comm_Save.Enabled = False
            Comm_Complete.Enabled = False
            Comm_Print.Enabled = True
            Comm_Search.Enabled = True
            Comm_Close.Enabled = True

            MyGrid1.Tag = False
            Ks_DtCom.Enabled = False
            UseStaff_DtCom.Enabled = False
            WareHouse_DtCom.Enabled = False
            Form_Date.Enabled = False
            Memo_Text.Enabled = False

            Select Case M_Materials_Use_Out1.WriteOffStatus & ""
                Case "冲销"      '冲销单不能被冲销
                    Comm_WriteOffAll.Enabled = False
                    Comm_WriteOffPart.Enabled = False
                Case "被冲销"    '被冲销单不能一键冲销
                    Comm_WriteOffAll.Enabled = False
                    Comm_WriteOffPart.Enabled = True
                Case "全部被冲销" '全部被冲销单不能被冲销
                    Comm_WriteOffAll.Enabled = False
                    Comm_WriteOffPart.Enabled = False
                Case ""
                    Comm_WriteOffAll.Enabled = True
                    Comm_WriteOffPart.Enabled = True
            End Select
            Exit Sub
        End If

    End Sub

    Private Sub MyGrid_Init()
        Dim ColEdit As Boolean
        If M_Materials_Use_Out1.OrdersStatus = "完成" Then
            ColEdit = False
        Else
            ColEdit = True
        End If
        '初始化TDBGrid
        With MyGrid1
            .Init_Grid()
            .Init_Column("支领编码", "M_Use_Code", "0", "左", "", False)
            .Init_Column("支领明细编码", "M_Use_Detail_Code", "0", "左", "", False)
            .Init_Column("物资库存编码", "MaterialsStock_Code", "0", "左", "", False)
            .Init_Column("物资编码", "Materials_Code", "0", "左", "", False)

            If M_Materials_Use_Out1.WriteOffStatus = "冲销" Then
                .Init_Column("物资名称", "Materials_Name", "150", "左", "", False)
            Else
                .Init_Column("物资名称", "Materials_Name", "150", "左", "", ColEdit)
            End If

            .Init_Column("物资批号", "MaterialsLot", "100", "左", "", False)
            .Init_Column("物资规格", "Materials_Spec", "80", "左", "", False)
            .Init_Column("物资有效期", "MaterialsExpiryDate", "110", "中", "yyyy-MM-dd", False)

            If M_Materials_Use_Out1.WriteOffStatus = "冲销" Then
                .Init_Column("可冲数量", "Can_RealWriteOffNo", "100", "右", "####0.####", False)
                .Init_Column("数量", "M_Use_Num", "80", "右", "####0.####", ColEdit)
                .Init_Column("库存数量", "MaterialsStore_Num", "100", "右", "####0.####", False)
                .Init_Column("单价", "M_Use_Price", "70", "右", "####0.00##", False)
                .Init_Column("冲销金额", "M_Use_Money", "100", "右", "##,##0.00##", False)
            Else
                If M_Materials_Use_Out1.OrdersStatus = "完成" Then
                    .Init_Column("数量", "M_Use_Num", "80", "右", "####0.####", ColEdit)
                    .Init_Column("冲销数量", "M_Use_WriteoffNo", "80", "右", "####0.####", False)
                    .Init_Column("实际领用数量", "M_Use_RealNo", "100", "右", "####0.####", False)
                    .Init_Column("单价", "M_Use_Price", "70", "右", "####0.00##", False)
                    .Init_Column("领用金额", "M_Use_Money", "100", "右", "##,##0.00##", False)
                    .Init_Column("实际领用金额", "M_Use_RealMoney", "120", "右", "##,##0.00##", False)
                Else
                    .Init_Column("当前库存", "MaterialsStore_Num", "70", "右", "####0.####", False)
                    .Init_Column("数量", "M_Use_Num", "80", "右", "####0.####", ColEdit)
                    .Init_Column("单价", "M_Use_Price", "70", "右", "####0.00##", False)
                    .Init_Column("领用金额", "M_Use_Money", "100", "右", "##,##0.00##", False)
                End If
            End If
            .Init_Column("备注", "M_UseDetail_Memo", "200", "左", "", ColEdit)

            .MarqueeStyle = C1.Win.C1TrueDBGrid.MarqueeEnum.HighlightCell
            If M_Materials_Use_Out1.WriteOffStatus = "冲销" Then
                .AllowAddNew = False '冲销单不允许增加新行
            Else
                .AllowAddNew = ColEdit
            End If
            .ColumnFooters = True
            MyGrid1.Splits(0).DisplayColumns("Materials_Name").Locked = True
        End With
    End Sub

    Private Sub MaterialsUseOut_FormClosing(ByVal sender As Object, ByVal e As System.Windows.Forms.FormClosingEventArgs) Handles Me.FormClosing
        If e.CloseReason = CloseReason.UserClosing Then
            Try
                MyGrid1.UpdateData()
                If My_Table.DataSet.HasChanges = True Then '只有录入状态的单据才能保存
                    If B_Materials_Use_Out1.GetRecordCount("M_Use_Code='" & Code_Text.Text.Trim & "' and OrdersStatus='完成'") = 0 Then
                        If MessageBox.Show("数据尚未保存,是否保存数据?", "提示", MessageBoxButtons.OKCancel, MessageBoxIcon.Question, MessageBoxDefaultButton.Button1) = Windows.Forms.DialogResult.OK Then
                            Call Data_Save("保存")
                        End If
                    End If
                End If
            Catch ex As Exception
                MsgBox("当前编辑行数据因未填写完整，将不能保存！", MsgBoxStyle.Critical + MsgBoxStyle.OkOnly, "提示：")
                MyGrid1.Delete()
            End Try
        End If
    End Sub
#End Region

#Region "清空__显示"
    Private Sub Zb_Clear()
        Rinsert = True
        M_Materials_Use_Out1 = New ModelOld.M_Materials_Use_Out1
        M_Materials_Use_Out1.M_Use_Code = B_Materials_Use_Out1.MaxCode

        Code_Text.Text = M_Materials_Use_Out1.M_Use_Code
        Jsr_Text.Text = HisVar.HisVar.JsrName

        Ks_DtCom.SelectedIndex = -1
        UseStaff_DtCom.SelectedIndex = -1
        WareHouse_DtCom.SelectedIndex = -1
        Form_Date.Value = Format(Now, "yyyy-MM-dd HH:mm")
        Memo_Text.Text = ""

        V_TotalMoney = 0

        Call BtnState()
        Call P_Data_Show()

        Ks_DtCom.Select()
    End Sub

    Private Sub Zb_Show()   '显示记录
        Rinsert = False
        With M_Materials_Use_Out1
            Code_Text.Text = .M_Use_Code
            Jsr_Text.Text = .Jsr_Name
            Ks_DtCom.SelectedValue = .Ks_Code
            UseStaff_DtCom.Text = .Use_Staff
            WareHouse_DtCom.SelectedValue = .MaterialsWh_Code
            Form_Date.Value = .M_Use_Date
            Memo_Text.Text = .M_Use_Memo
        End With
        Call BtnState()
        Call P_Data_Show()
    End Sub

    Private Sub P_Data_Show()   '从表数据
        If M_Materials_Use_Out1.WriteOffStatus = "冲销" Then
            My_Table = B_Materials_Use_Out2.GetWriteOffList("Materials_Use_Out2.M_Use_Code='" & M_Materials_Use_Out1.M_Use_Code & "'").Tables(0)
        Else
            My_Table = B_Materials_Use_Out2.GetList("Materials_Use_Out2.M_Use_Code='" & M_Materials_Use_Out1.M_Use_Code & "'").Tables(0)
        End If
        My_Table.Columns("M_Use_Code").AllowDBNull = True
        My_Table.Columns("M_Use_Detail_Code").AllowDBNull = True
        My_Table.Columns("M_Use_Num").AllowDBNull = True
        My_Table.Columns("M_Use_WriteoffNo").DefaultValue = 0
        My_Table.Constraints.Clear()
        MyGrid1.DataTable = My_Table
        Cb_Cm = CType(BindingContext(My_Table, ""), CurrencyManager)
        Call F_Sum()
        MyGrid1.Focus()
    End Sub

    Private Sub StatisticsDataShow()
        Dim str As String = "Finish_Date BETWEEN '" & Format(Now, "yyyy-MM-dd 00:00:00") & "' AND '" & Format(Now, "yyyy-MM-dd 23:59:59") & "'"
        TodayFormsNo_Lbl.Text = "今日支领单数量：" & B_Materials_Use_Out1.GetRecordCount(str)
        TodayMoney_Lbl.Text = "今日支领总金额：" & B_Materials_Use_Out1.GetSumMoney(str)
        str += "AND Jsr_Code='" & HisVar.HisVar.JsrCode & "'"
        BrTodayFormsNo_Lbl.Text = "本人支领单数量：" & B_Materials_Use_Out1.GetRecordCount(str)
        BrTodayMoney_Lbl.Text = "本人支领总金额：" & B_Materials_Use_Out1.GetSumMoney(str)
    End Sub
#End Region

#Region "控件__动作"

#Region "按钮"

    Private Sub Comm_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Comm_Del.Click, Comm_DelAll.Click, Comm_Save.Click,
        Comm_Complete.Click, Comm_New.Click, Comm_Print.Click, Comm_Close.Click, Comm_Search.Click, Comm_WriteOffPart.Click, Comm_WriteOffAll.Click
        Select Case sender.tag
            Case "删除行"
                If B_Materials_Use_Out1.GetRecordCount("M_Use_Code='" & Code_Text.Text.Trim & "' and OrdersStatus ='完成'") > 0 Then
                    MsgBox("已完成单据不能删除！", MsgBoxStyle.Exclamation, "提示")
                    Exit Sub
                End If
                Data_Delete()
            Case "删除单"
                If M_Materials_Use_Out1.M_Use_Date Is Nothing Then
                    MsgBox("数据尚未保存,无法删除!", MsgBoxStyle.Exclamation, "提示")
                    Exit Sub
                End If
                If B_Materials_Use_Out1.GetRecordCount("M_Use_Code='" & Code_Text.Text.Trim & "' and OrdersStatus ='录入'") = 0 Then
                    MsgBox("已完成单据不能删除！", MsgBoxStyle.Exclamation, "提示")
                    Exit Sub
                End If
                Call Data_DeleteAll()
            Case "新单"
                Data_New()
            Case "保存", "完成"
                Call Data_Save(sender.tag)
            Case "打印"
                If M_Materials_Use_Out1.M_Use_Date Is Nothing Then
                    MsgBox("数据尚未保存,无法打印!", MsgBoxStyle.Exclamation, "提示")
                    Exit Sub
                End If
                Call Data_Print()
            Case "速查"
                Dim t_Rc As New BaseClass.C_RowChange
                AddHandler t_Rc.ModelChangeEvent, AddressOf ChangeModel
                Dim f As New MaterialsUseOutQuery(t_Rc)
                f.Owner = Me
                f.ShowDialog()
            Case "退出"
                Me.Close()
            Case "冲销", "一键冲销"
                If B_Materials_Use_Out1.GetRecordCount("M_Use_Code='" & Code_Text.Text.Trim & "' and OrdersStatus='完成'") = 0 Then
                    MsgBox("未完成单据不能冲销", MsgBoxStyle.Exclamation, "提示")
                    Exit Sub
                End If
                If B_Materials_Use_Out2.GetRecordCount("M_Use_Code='" & Code_Text.Text.Trim & "' and M_Use_RealNo>0") = 0 Then
                    MsgBox("该单据已全部被冲销，不能冲销", MsgBoxStyle.Exclamation, "提示")
                    Exit Sub
                End If

                If MsgBox("是否" + sender.tag + " 领用科室：" & Ks_DtCom.Text & "，领用金额：" & M_Materials_Use_Out1.TotalMoney & "，单号：" & Code_Text.Text & "的科室领用单？", MsgBoxStyle.Question + MsgBoxStyle.YesNo + MsgBoxStyle.DefaultButton1, "提示") = DialogResult.Yes Then
                    Dim model As New ModelOld.M_Materials_Use_Out1
                    With model
                        .M_Use_Code = B_Materials_Use_Out1.MaxCode
                        .Ks_Code = M_Materials_Use_Out1.Ks_Code
                        .Use_Staff = M_Materials_Use_Out1.Use_Staff
                        .MaterialsWh_Code = M_Materials_Use_Out1.MaterialsWh_Code
                        .M_Use_Date = M_Materials_Use_Out1.M_Use_Date
                        .Input_Date = Format(Now, "yyyy-MM-dd HH:mm")
                        .Finish_Date = Nothing
                        .Jsr_Code = HisVar.HisVar.JsrCode
                        .Jsr_Name = HisVar.HisVar.JsrName
                        .TotalMoney = 0
                        .M_Use_Memo = ""
                        .OrdersStatus = "录入"
                        .WriteOff_Code = M_Materials_Use_Out1.M_Use_Code
                        .WriteOffStatus = "冲销"
                    End With
                    If B_Materials_Use_Out1.AddWriteOff(model) = True Then
                        If sender.Text = "一键冲销" Then
                            model.OrdersStatus = "完成"
                            model.Finish_Date = Format(Now, "yyyy-MM-dd HH:mm")
                            If Not B_Materials_Use_Out1.Complete(model) Then
                                model.OrdersStatus = "录入"
                                model.Finish_Date = Nothing
                                MsgBox("完成操作失败!", MsgBoxStyle.Exclamation, "提示")
                                Exit Sub
                            End If
                        End If
                        Dim V_Form As New Materials.MaterialsUseOut(model)
                        V_Form.Name = V_Form.Name & model.M_Use_Code
                        BaseFunc.BaseFunc.addTabControl(V_Form, "物资领用冲销-" & Code_Text.Text)
                    End If
                End If
        End Select

    End Sub

    Private Sub Memo_Text_Validated(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Memo_Text.Validated
        MyGrid1.Focus()
    End Sub

#End Region

#Region "DBGrid动作"

    Private Sub MyGrid1_AfterColUpdate(ByVal sender As System.Object, ByVal e As C1.Win.C1TrueDBGrid.ColEventArgs) Handles MyGrid1.AfterColUpdate
        Dim up_Row As DataRow                    '当 前 行
        up_Row = Cb_Cm.List(MyGrid1.Row).Row
        Select Case MyGrid1.Splits(0).DisplayColumns(e.ColIndex).Name
            Case "数量"
                If up_Row("M_Use_Num") IsNot DBNull.Value Then
                    up_Row("M_Use_Money") = up_Row("M_Use_Num") * up_Row("M_Use_Price")
                End If
        End Select
    End Sub

    Private Sub MyGrid1_AfterUpdate(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyGrid1.AfterUpdate
        Call F_Sum()
    End Sub

    Private Sub MyGrid1_KeyDown(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles MyGrid1.KeyDown
        Select Case e.KeyCode
            Case Keys.Escape, Keys.F2, Keys.F3, Keys.F4, Keys.F5, Keys.F6, Keys.Up, Keys.Down, Keys.Left, Keys.Right
            Case Keys.Delete
                Data_Delete()
            Case Else
                If M_Materials_Use_Out1.WriteOffStatus <> "冲销" And B_Materials_Use_Out1.GetRecordCount("M_Use_Code='" & Code_Text.Text.Trim & "' and OrdersStatus='完成'") = 0 Then
                    If Zb_Check() = False Then Exit Sub
                    If MyGrid1.Columns(MyGrid1.Col).DataField = "Materials_Name" Then '物资输入
                        Call Input("物资")
                        Exit Sub
                    End If
                End If

                If e.KeyCode = Keys.Enter Then
                    If MyGrid1.Columns(MyGrid1.Col).DataField = "M_Use_Num" Then
                        Me.MyGrid1.DirectionAfterEnter = C1.Win.C1TrueDBGrid.DirectionAfterEnterEnum.MoveNone
                        Me.MyGrid1.Col = MyGrid1.Splits(0).DisplayColumns.IndexOf(MyGrid1.Columns("M_UseDetail_Memo"))
                        Exit Sub
                    End If

                    If MyGrid1.Columns(MyGrid1.Col).DataField = "M_UseDetail_Memo" Then
                        Me.MyGrid1.DirectionAfterEnter = C1.Win.C1TrueDBGrid.DirectionAfterEnterEnum.MoveNone
                        Me.MyGrid1.Row = Me.MyGrid1.Row + 1
                        If flag_Error = True Then
                            flag_Error = False
                            Exit Sub
                        End If
                        If M_Materials_Use_Out1.WriteOffStatus = "冲销" Then
                            Me.MyGrid1.Col = MyGrid1.Splits(0).DisplayColumns.IndexOf(MyGrid1.Columns("M_Use_Num"))
                        Else
                            Me.MyGrid1.Col = MyGrid1.Splits(0).DisplayColumns.IndexOf(MyGrid1.Columns("Materials_Name"))
                        End If
                    Else
                        Me.MyGrid1.DirectionAfterEnter = C1.Win.C1TrueDBGrid.DirectionAfterEnterEnum.MoveRight
                    End If
                End If
        End Select
    End Sub

    Private Sub MyGrid1_MouseDoubleClick(ByVal sender As System.Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles MyGrid1.MouseDoubleClick
        If M_Materials_Use_Out1.WriteOffStatus <> "冲销" AndAlso B_Materials_Use_Out1.GetRecordCount("M_Use_Code='" & Code_Text.Text.Trim & "' and OrdersStatus='完成'") = 0 Then
            If Zb_Check() = False Then Exit Sub
            If MyGrid1.Columns(MyGrid1.Col).DataField = "Materials_Name" Then '物资输入
                Call Input("物资")
                Exit Sub
            End If
        End If
    End Sub

    Private Sub MyGrid1_GotFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles MyGrid1.GotFocus
        Me.CancelButton = Nothing
    End Sub

    Private Sub MyGrid1_Validated(ByVal sender As Object, ByVal e As System.EventArgs) Handles MyGrid1.Validated
        Me.CancelButton = Comm_Close
    End Sub

    Private Sub MyGrid1_BeforeColUpdate(ByVal sender As System.Object, ByVal e As C1.Win.C1TrueDBGrid.BeforeColUpdateEventArgs) Handles MyGrid1.BeforeColUpdate
        If Zb_Check() = False Then Exit Sub
        If e.Column.Name = "物资名称" Then
            If VerifyMaterialsName(MyGrid1.Columns("Materials_Name").Text) = False Then
                e.Cancel = True
            End If
        End If

        If e.Column.Name = "数量" Then
            If VerifyNum(MyGrid1.Columns("M_Use_Num").Value, MyGrid1.Row) = False Then
                e.Cancel = True
            End If
        End If
    End Sub

    Private Sub MyGrid1_Error(ByVal sender As Object, ByVal e As C1.Win.C1TrueDBGrid.ErrorEventArgs) Handles MyGrid1.Error
        e.Handled = True
        flag_Error = True
        If StrConv(e.Exception.Message, VbStrConv.Narrow) = "列""Materials_Name""不允许 nulls?" Then
            MessageBox.Show("物资不能为空!", "提示", MessageBoxButtons.OK, MessageBoxIcon.Exclamation)
            MyGrid1.SetActiveCell(MyGrid1.Row, MyGrid1.Splits(0).DisplayColumns.IndexOf(MyGrid1.Columns("Materials_Name")))
        End If

        If StrConv(e.Exception.Message, VbStrConv.Narrow) = "列""M_Use_Num""不允许 nulls?" Then
            MessageBox.Show("数量不能为空!", "提示", MessageBoxButtons.OK, MessageBoxIcon.Exclamation)
            MyGrid1.SetActiveCell(MyGrid1.Row, MyGrid1.Splits(0).DisplayColumns.IndexOf(MyGrid1.Columns("M_Use_Num")))
        End If

        Me.MyGrid1.Row = Me.MyGrid1.Row - 1
    End Sub

#End Region

#Region "快捷键"
    Private Sub ShortCutKey(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles MyBase.KeyDown
        If e.Control = True And e.KeyCode = Keys.S And Comm_Save.Enabled = True Then
            Call Data_Save("保存")
        End If

        If e.KeyData = Keys.F2 And Comm_New.Enabled = True Then
            MyGrid1.Focus()
            sender.tag = "新单"
            Call Comm_Click(sender, Nothing)
        End If

        If e.KeyData = Keys.F3 And Comm_Save.Enabled = True Then
            MyGrid1.Focus()
            sender.tag = "保存"
            Call Comm_Click(sender, Nothing)
        End If

        If e.KeyData = Keys.F4 And Comm_Complete.Enabled = True Then
            MyGrid1.Focus()
            sender.tag = "完成"
            Call Comm_Click(sender, Nothing)
        End If

        If e.KeyData = Keys.F5 And Comm_Print.Enabled = True Then
            MyGrid1.Focus()
            sender.tag = "打印"
            Call Comm_Click(sender, Nothing)
        End If
        If e.KeyData = Keys.F6 And Comm_Search.Enabled = True Then
            MyGrid1.Focus()
            sender.tag = "速查"
            Call Comm_Click(sender, Nothing)
        End If
    End Sub
#End Region

#End Region

#Region "自定义函数"

#Region "验证函数"

    Private Function Zb_Check() As Boolean
        If Ks_DtCom.SelectedValue Is Nothing Then
            MsgBox("请添加支领科室!", MsgBoxStyle.Exclamation, "提示")
            Ks_DtCom.Select()
            Return False
        End If

        If String.IsNullOrEmpty(UseStaff_DtCom.Text) Then
            MsgBox("请添加支领人!", MsgBoxStyle.Exclamation, "提示")
            UseStaff_DtCom.Select()
            Return False
        End If

        If WareHouse_DtCom.SelectedValue Is Nothing Then
            MsgBox("请添加库房!", MsgBoxStyle.Exclamation, "提示")
            WareHouse_DtCom.Select()
            Return False
        End If
        Return True
    End Function

    Private Function Zb_CheckWc() As Boolean
        If B_Materials_Use_Out1.GetRecordCount("M_Use_Code='" & Code_Text.Text.Trim & "' And OrdersStatus='完成'") > 0 Then
            MsgBox("此次支领已经完成!", MsgBoxStyle.Exclamation, "提示")
            Return False
        End If
        Return True
    End Function

    Private Function Cb_CheckRowCounts() As Boolean
        If MyGrid1.RowCount = 0 Then
            MsgBox("尚未录入数据", MsgBoxStyle.Exclamation, "提示")
            Return False
        End If
        Return True
    End Function

    Private Function Cb_Check() As Boolean
        MyGrid1.UpdateData()
        Dim i As Integer = 0
        For Each _Row In My_Table.Rows
            If _Row.RowState = DataRowState.Deleted Then
                Continue For
            End If
            '支领数量验证
            If VerifyNum(_Row("M_Use_Num"), i) = False Then
                MyGrid1.Focus()
                MyGrid1.SetActiveCell(i, MyGrid1.Splits(0).DisplayColumns.IndexOf(MyGrid1.Columns("M_Use_Num")))
                Return False
            End If

            If M_Materials_Use_Out1.WriteOffStatus = "冲销" Then
                _Row("M_Use_RealNo") = 0
                _Row("M_Use_WriteoffNo") = 0
                _Row("M_Use_Money") = _Row("M_Use_Num") * _Row("M_Use_Price")
                _Row("M_Use_RealMoney") = 0
            Else
                _Row("M_Use_RealNo") = _Row("M_Use_Num") + _Row("M_Use_WriteoffNo")
                _Row("M_Use_Money") = CDec(_Row("M_Use_Num")) * CDec(_Row("M_Use_Price"))
                _Row("M_Use_RealMoney") = CDec(_Row("M_Use_RealNo")) * CDec(_Row("M_Use_Price"))
            End If
            i = i + 1
        Next
        Return True
    End Function

    '验证支领数量是否正确
    Private Function VerifyNum(ByVal Num As Object, ByVal Index As Integer) As Boolean

        If IsDBNull(Num) Then
            HisControl.msg.Show("请输入正确数字!", "提示")
            Return False
        End If

        If Common.Tools.IsNumber(Num) = False Then
            HisControl.msg.Show("请输入正确数字!", "提示")
            Return False
        End If
        If M_Materials_Use_Out1.WriteOffStatus & "" <> "冲销" Then
            If CDbl(Num) <= 0 Then
                HisControl.msg.Show("支领数量必须大于0!", "提示")
                Return False
            End If
        Else
            If CDbl(Num) >= 0 Then
                HisControl.msg.Show("冲销数量必须小于0!", "提示")
                Return False
            End If
        End If

        Dim row As DataRow = Cb_Cm.List(Index).row
        If M_Materials_Use_Out1.WriteOffStatus & "" <> "冲销" Then '正常单据减少库存，要和当前库存比较
            If CDbl(Num) > B_Materials_Stock.GetModel(row("MaterialsStock_Code")).MaterialsStore_Num Then
                HisControl.msg.Show("支领数量不能大于库存数量!", "    提示")
                Return False
            End If
        Else '冲销单据不能超过可冲数量
            If -CDbl(Num) > B_Materials_Use_Out2.GetModelByCondition("M_Use_Code='" & M_Materials_Use_Out1.WriteOff_Code & "' and MaterialsStock_Code='" & row("MaterialsStock_Code") & "'").M_Use_RealNo Then
                HisControl.msg.Show("冲销数量不能大于可冲数量!", "提示")
                Return False
            End If
        End If
        Return True
    End Function
    '验证支领物资
    Private Function VerifyMaterialsName(ByVal Materials_Name As String) As Boolean
        If Materials_Name = "" Then
            HisControl.msg.Show("物资不能为空!", "提示")
            Return False
        End If
        Return True
    End Function

#End Region

#Region "按钮函数"
    Private Sub Data_Delete()
        If MessageBox.Show("确认是否删除当前记录,如果删除该记录将不可恢复!", "提示", MessageBoxButtons.OKCancel, MessageBoxIcon.Error, MessageBoxDefaultButton.Button1) = Windows.Forms.DialogResult.OK Then
            Try
                MyGrid1.Delete()
                Call F_Sum()
            Catch ex As Exception
                If ex.Message.ToString = "索引 -1 不是为负数，就是大于行数。" Then
                    MsgBox("未选中任何行！", MsgBoxStyle.Critical + MsgBoxStyle.OkOnly, "提示:")
                End If
            End Try
        End If
    End Sub

    Private Sub Data_DeleteAll()
        If MessageBox.Show("确认是否删除当前单据,如果删除该单据将不可恢复!", "提示", MessageBoxButtons.OKCancel, MessageBoxIcon.Error, MessageBoxDefaultButton.Button1) = Windows.Forms.DialogResult.OK Then
            B_Materials_Use_Out1.DeleteAll(M_Materials_Use_Out1.M_Use_Code)
            Zb_Clear()
        End If
    End Sub

    Private Sub Data_New()
        MyGrid1.UpdateData()
        If My_Table.DataSet.HasChanges = True Then
            If B_Materials_Use_Out1.GetRecordCount("  M_Use_Code='" & Code_Text.Text.Trim & "' and OrdersStatus='完成'") = 0 Then
                If MessageBox.Show("数据尚未保存,是否保存数据?", "提示", MessageBoxButtons.OKCancel, MessageBoxIcon.Question, MessageBoxDefaultButton.Button1) = Windows.Forms.DialogResult.OK Then
                    Call Data_Save("保存")
                End If
            End If
        End If
        Call Zb_Clear()
    End Sub

    Private Sub Data_Save(ByVal arg As String)
        If Not Zb_CheckWc() Then Exit Sub
        If Not Zb_Check() Then Exit Sub
        If Not Cb_CheckRowCounts() Then Exit Sub
        If Not Cb_Check() Then Exit Sub

        If arg = "保存" Then
            Call Zb_Save()
        ElseIf arg = "完成" Then
            Call Zb_Save()
            Call Data_Complete()
        End If
    End Sub

    Private Sub Data_Complete()
        If MsgBox("是否完成此次支领？", MsgBoxStyle.Information + MsgBoxStyle.OkCancel + MsgBoxStyle.DefaultButton1, "提示:") = MsgBoxResult.Cancel Then Exit Sub
        If Not Zb_CheckWc() Then Exit Sub

        Try
            With M_Materials_Use_Out1
                .Finish_Date = Format(Now, "yyyy-MM-dd HH:mm")
                .OrdersStatus = "完成"
            End With
            If Not B_Materials_Use_Out1.Complete(M_Materials_Use_Out1) Then
                With M_Materials_Use_Out1
                    .Finish_Date = Nothing
                    .OrdersStatus = "录入"
                End With
            End If
            StatisticsDataShow()
            HisControl.msg.Show("支领完成!", "提示")
            Call BtnState()
            Call P_Data_Show()
            Focus()
        Catch ex As Exception
            Beep()
            MsgBox(ex.Message, MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
            WareHouse_DtCom.Select()
            Exit Sub
        End Try
    End Sub


    Private Sub Data_Print()
        Dim StiRpt As New StiReport
        StiRpt.Load(".\Rpt\物资支领表.mrt")

        StiRpt.ReportName = "物资支领单"
        StiRpt.RegData(My_Table)
        StiRpt.Pages(0).PaperSize = Printing.PaperKind.A4
        StiRpt.Pages(0).Margins.Top = 1
        StiRpt.Pages(0).Margins.Bottom = 1
        StiRpt.Pages(0).Margins.Left = 1
        StiRpt.Pages(0).Margins.Right = 1
        StiRpt.Compile()

        StiRpt("支领编码") = Code_Text.Text.Trim
        StiRpt("打印时间") = Now
        StiRpt("操作人") = Jsr_Text.Text
        StiRpt("支领科室") = Ks_DtCom.Text
        StiRpt("支领人") = UseStaff_DtCom.Text
        StiRpt("物资仓库") = WareHouse_DtCom.Text
        StiRpt("支领时间") = CDate(Form_Date.Value.ToString)

        StiRpt.Render()
        'StiRpt.Design()
        StiRpt.Show()
    End Sub


#End Region

#Region "数据库更改"

#Region "主表__编辑"
    Private Sub Zb_Save()                       '主表保存
        If Not Zb_CheckWc() Then Exit Sub
        If Rinsert = True Then     '增加记录
            Call Zb_Add()
        Else                       '编辑记录
            Call Zb_Edit()
        End If
        Call Cb_Update()
        HisControl.msg.Show("数据保存成功!", "提示")
        MyGrid1.Focus()
    End Sub

    Private Sub Zb_Add()    '增加记录
        Try
            With M_Materials_Use_Out1
                .M_Use_Code = B_Materials_Use_Out1.MaxCode
                Code_Text.Text = .M_Use_Code

                .Ks_Code = Ks_DtCom.SelectedValue
                .Use_Staff = UseStaff_DtCom.Text.Trim
                .MaterialsWh_Code = WareHouse_DtCom.SelectedValue
                .M_Use_Date = Format(Form_Date.Value, "yyyy-MM-dd HH:mm")
                .Input_Date = Format(Now, "yyyy-MM-dd HH:mm")
                .Jsr_Code = HisVar.HisVar.JsrCode
                .TotalMoney = V_TotalMoney
                .M_Use_Memo = Memo_Text.Text.Trim
                .OrdersStatus = "录入"

            End With
            B_Materials_Use_Out1.Add(M_Materials_Use_Out1)
            Rinsert = False
        Catch ex As Exception
            Beep()
            MsgBox(ex.Message, MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
            Exit Sub
        End Try
    End Sub

    Private Sub Zb_Edit()   '编辑记录
        Try
            With M_Materials_Use_Out1
                .Ks_Code = Ks_DtCom.SelectedValue
                .Use_Staff = UseStaff_DtCom.Text.Trim
                .MaterialsWh_Code = WareHouse_DtCom.SelectedValue
                .M_Use_Date = Format(Form_Date.Value, "yyyy-MM-dd HH:mm")
                .Jsr_Code = HisVar.HisVar.JsrCode
                .M_Use_Memo = Memo_Text.Text.Trim
                .TotalMoney = V_TotalMoney
            End With
            B_Materials_Use_Out1.Update(M_Materials_Use_Out1)
        Catch ex As Exception
            Beep()
            MsgBox(ex.Message, MsgBoxStyle.Critical + MsgBoxStyle.OkOnly, "提示:")
            Exit Sub
        End Try
    End Sub

#End Region

#Region "从表__编辑"

    Private Sub Cb_Update()   '从表更新
        For Each _row As DataRow In My_Table.Rows
            If _row.RowState = DataRowState.Added Then
                CbData_Insert(_row)
            End If
            If _row.RowState = DataRowState.Modified Then
                CbData_Update(_row)
            End If
            If _row.RowState = DataRowState.Deleted Then
                CbData_Delete(_row)
            End If
        Next
        My_Table.AcceptChanges()
    End Sub

    Private Sub CbData_Update(ByVal Cb_Row As DataRow)   '从表更新
        Dim model As ModelOld.M_Materials_Use_Out2 = B_Materials_Use_Out2.GetModel(Cb_Row("M_Use_Detail_Code"))
        With model
            .Materials_Code = Cb_Row("Materials_Code")
            .MaterialsStock_Code = Cb_Row("MaterialsStock_Code")
            .MaterialsLot = Cb_Row("MaterialsLot")
            .MaterialsExpiryDate = Cb_Row("MaterialsExpiryDate")
            .M_Use_Num = Cb_Row("M_Use_Num")
            .M_Use_WriteoffNo = Cb_Row("M_Use_WriteoffNo")
            .M_Use_RealNo = Cb_Row("M_Use_RealNo")
            .M_Use_Price = Cb_Row("M_Use_Price")
            .M_Use_Money = Cb_Row("M_Use_Money")
            .M_Use_RealMoney = Cb_Row("M_Use_RealMoney")
            .M_UseDetail_Memo = Cb_Row("M_UseDetail_Memo") & ""
        End With
        B_Materials_Use_Out2.Update(model)
    End Sub

    Private Sub CbData_Insert(ByVal Cb_Row As DataRow)   '从表增加 
        With Cb_Row
            .Item("M_Use_Code") = M_Materials_Use_Out1.M_Use_Code
            .Item("M_Use_Detail_Code") = B_Materials_Use_Out2.MaxCode(M_Materials_Use_Out1.M_Use_Code)
        End With

        Dim model As New ModelOld.M_Materials_Use_Out2
        With model
            .M_Use_Code = Cb_Row("M_Use_Code")
            .Materials_Code = Cb_Row("Materials_Code")
            .MaterialsStock_Code = Cb_Row("MaterialsStock_Code")
            .M_Use_Detail_Code = Cb_Row("M_Use_Detail_Code")
            .MaterialsLot = Cb_Row("MaterialsLot")
            .MaterialsExpiryDate = Cb_Row("MaterialsExpiryDate")
            .M_Use_Num = Cb_Row("M_Use_Num")
            .M_Use_WriteoffNo = Cb_Row("M_Use_WriteoffNo")
            .M_Use_RealNo = Cb_Row("M_Use_RealNo")
            .M_Use_Price = Cb_Row("M_Use_Price")
            .M_Use_Money = Cb_Row("M_Use_Money")
            .M_Use_RealMoney = Cb_Row("M_Use_RealMoney")
            .M_UseDetail_Memo = Cb_Row("M_UseDetail_Memo") & ""
        End With
        B_Materials_Use_Out2.Add(model)
    End Sub

    Private Sub CbData_Delete(ByVal Cb_Row As DataRow)
        B_Materials_Use_Out2.Delete(Cb_Row("M_Use_Detail_Code", DataRowVersion.Original))
    End Sub

#End Region

#End Region

    Private Sub ChangeModel(ByVal model As ModelOld.M_Materials_Use_Out1)
        M_Materials_Use_Out1 = model
        If M_Materials_Use_Out1 Is Nothing Then Exit Sub
        Call Zb_Show()
    End Sub

    Public Overrides Sub F_Sum()
        If MyGrid1.RowCount = 0 Then
            WareHouse_DtCom.Enabled = True
            Ks_DtCom.Enabled = True
            UseStaff_DtCom.Enabled = True
        Else
            WareHouse_DtCom.Enabled = False
            Ks_DtCom.Enabled = False
            UseStaff_DtCom.Enabled = False
        End If

        Dim Total_Use_Num As Double = IIf(My_Table.Compute("Sum(M_Use_Num)", "") Is DBNull.Value, 0, My_Table.Compute("Sum(M_Use_Num)", ""))
        MyGrid1.Columns("M_Use_Num").FooterText = Total_Use_Num.ToString("0.00##")

        Dim Total_Use_Money As Double = IIf(My_Table.Compute("Sum(M_Use_Money)", "") Is DBNull.Value, 0, My_Table.Compute("Sum(M_Use_Money)", ""))
        MyGrid1.Columns("M_Use_Money").FooterText = Total_Use_Money.ToString("0.00##")

        If M_Materials_Use_Out1.OrdersStatus = "完成" And M_Materials_Use_Out1.WriteOffStatus <> "冲销" Then
            Dim Total_Use_WriteoffNo As Double = IIf(My_Table.Compute("Sum(M_Use_WriteoffNo)", "") Is DBNull.Value, 0, My_Table.Compute("Sum(M_Use_WriteoffNo)", ""))
            MyGrid1.Columns("M_Use_WriteoffNo").FooterText = Total_Use_WriteoffNo.ToString("0.00##")

            Dim Total_Use_RealNo As Double = IIf(My_Table.Compute("Sum(M_Use_RealNo)", "") Is DBNull.Value, 0, My_Table.Compute("Sum(M_Use_RealNo)", ""))
            MyGrid1.Columns("M_Use_RealNo").FooterText = Total_Use_RealNo.ToString("0.00##")

            Dim Total_Use_RealMoney As Double = IIf(My_Table.Compute("Sum(M_Use_RealMoney)", "") Is DBNull.Value, 0, My_Table.Compute("Sum(M_Use_RealMoney)", ""))
            MyGrid1.Columns("M_Use_RealMoney").FooterText = Total_Use_Money.ToString("0.00##")
        End If

        V_TotalMoney = Total_Use_Money
    End Sub

    Private Sub Input(ByVal V_Lb As String)
        Dim strwhere As String = ""
        For Each _row In My_Table.Rows
            If _row.RowState = DataRowState.Deleted Then
                Continue For
            End If
            strwhere = strwhere & "'" & _row("MaterialsStock_Code") & "',"
        Next
        Dim f As New MaterialsStockSelect(WareHouse_DtCom.SelectedValue, strwhere)
        f.Owner = Me
        If f.ShowDialog = Windows.Forms.DialogResult.OK Then
            If My_Table.Select("MaterialsStock_Code='" & f.ModelMaterials_Stock.MaterialsStock_Code & "'").Length > 0 Then
                MsgBox("已经存在此条支领记录!", MsgBoxStyle.Exclamation, "提示")
                MyGrid1.SetActiveCell(My_Table.Rows.IndexOf(My_Table.Select("MaterialsStock_Code='" & f.ModelMaterials_Stock.MaterialsStock_Code & "'")(0)), MyGrid1.Splits(0).DisplayColumns.IndexOf(MyGrid1.Columns("M_Use_Num")))
                Exit Sub
            End If

            MyGrid1.Columns("M_Use_Code").Value = Code_Text.Text.Trim
            Dim row As DataRow
            row = Cb_Cm.List(MyGrid1.Row).Row

            Select Case V_Lb
                Case "物资"
                    With f.ModelMaterials_Stock
                        row("Materials_Code") = .Materials_Code
                        row("Materials_Name") = .Materials_Name
                        row("MaterialsStock_Code") = .MaterialsStock_Code
                        row("MaterialsLot") = .MaterialsLot
                        row("MaterialsExpiryDate") = .MaterialsExpiryDate
                        row("M_Use_Price") = .MaterialsStore_Price
                        row("MaterialsStore_Num") = .MaterialsStore_Num
                        row("Materials_Spec") = .Materials_Spec
                    End With
                    MyGrid1.SetActiveCell(MyGrid1.Row, MyGrid1.Splits(0).DisplayColumns.IndexOf(MyGrid1.Columns("M_Use_Num")))
            End Select
        End If
    End Sub



#End Region

#Region "输入法设置"
    '中文
    Private Sub CodeTextBox1_Enter(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Memo_Text.GotFocus
        InputLanguage.CurrentInputLanguage = HisVar.HisVar.InputCode
    End Sub
    '英文
    Private Sub yw_Enter(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Ks_DtCom.GotFocus, UseStaff_DtCom.GotFocus, WareHouse_DtCom.GotFocus
        InputLanguage.CurrentInputLanguage = InputLanguage.InstalledInputLanguages(0)
    End Sub
#End Region

End Class
