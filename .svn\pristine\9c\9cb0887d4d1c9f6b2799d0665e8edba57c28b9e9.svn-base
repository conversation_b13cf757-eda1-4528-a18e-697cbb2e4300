﻿Imports C1.Win.C1Input
Imports C1.Win.C1TrueDBGrid
Imports System.Data.SqlClient
Imports BaseClass

Public Class Zd_Yp13

#Region "变量定义"
    Dim My_Button As C_Button                                       '按扭初始化 
    Dim My_Cc As New BaseClass.C_Cc()
    Dim My_Table As New DataTable
#End Region

#Region "传参"
    Dim R<PERSON>ert As Boolean
    Dim Rrow As DataRow
    Dim RZbtb As DataTable
    Dim Rtdbgrid As C1.Win.C1TrueDBGrid.C1TrueDBGrid
    Dim Rlb As C1.Win.C1Input.C1Label
    Dim Rrc As C_RowChange
    Dim Rtree As TreeView
    Dim R_Count As Integer
    Dim RDl_Code As Integer
#End Region

    Public Sub New(ByVal tinsert As Boolean, ByVal trow As DataRow, ByVal tZbtb As DataTable, ByVal ttdbgrid As C1.Win.C1TrueDBGrid.C1TrueDBGrid, ByVal tlb As C1.Win.C1Input.C1Label, ByRef trc As C_RowChange, ByRef ttree As TreeView, ByRef t_Count As Integer,
                   ByVal tDl_Code As String)
        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()
        Rinsert = tinsert
        'Rdate = tdate
        Rrow = trow
        RZbtb = tZbtb
        Rtdbgrid = ttdbgrid
        Rlb = tlb
        Rrc = trc
        Rtree = ttree
        R_Count = t_Count
        RDl_Code = tDl_Code
        AddHandler Rrc.RowChanged, New BaseClass.RowChangedHandler(AddressOf Data_Show)
        ' 在 InitializeComponent() 调用之后添加任何初始化。
    End Sub

    Private Sub Zd_Yp13_FormClosed(ByVal sender As Object, ByVal e As System.Windows.Forms.FormClosedEventArgs) Handles Me.FormClosed
        RemoveHandler Rrc.RowChanged, New BaseClass.RowChangedHandler(AddressOf Data_Show)
        Me.Dispose()
    End Sub

    Protected Overrides Sub OnKeyDown(ByVal e As System.Windows.Forms.KeyEventArgs)
        MyBase.OnKeyDown(e)
        If e.Control = True And e.KeyCode = Keys.S Then
            Comm1.Select()
            Call Comm_Click(Comm1, Nothing)
        End If
    End Sub

    Private Sub Zd_Yp13_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        Call Form_Init()
        If Rinsert = True Then Call Data_Clear() Else Call Data_Show(Rrow)
    End Sub

    Private Sub Form_Init()
        Panel1.Height = 29

        'Escape 不接收修改
        C1TextBox1.AcceptsEscape = False
        C1TextBox2.AcceptsEscape = False
        C1TextBox3.AcceptsEscape = False

        Dim My_Dataset As New DataSet


        HisVar.HisVar.Sqldal.QueryDt(My_Dataset, "Select Gx_Jc,Gx_Name,Gx_Code From Zd_Ml_Ypgx Order By Gx_Code", "功效", True)
        Dim My_Combo As New BaseClass.C_Combo2(C1Combo1, My_Dataset.Tables("功效").DefaultView, "Gx_Name", "Gx_Code", 386)
        With My_Combo
            .Init_TDBCombo()
            .Init_Colum("Gx_Jc", "简称", 92, "左")
            .Init_Colum("Gx_Name", "功效名称", 200, "左")
            .Init_Colum("Gx_Code", "编码", 0, "左")
            .MaxDropDownItems(12)
            .SelectedIndex(-1)
        End With
        With C1Combo1
            .AutoCompletion = False
            .AutoSelect = False


        End With

        If Rinsert = True Then
            HisVar.HisVar.Sqldal.QueryDt(My_Dataset, "select Xl_Code,Xl_Name,Xl_Jc FROM Zd_Ml_Yp1Xl where Dl_Code='" & Rtree.SelectedNode.Tag & "'", "类型", True)
        Else
            If Rtree.SelectedNode.Tag = "00"Then
                HisVar.HisVar.Sqldal.QueryDt(My_Dataset, "select Xl_Code,Xl_Name,Xl_Jc FROM Zd_Ml_Yp1Xl ", "类型", True)
            Else
                HisVar.HisVar.Sqldal.QueryDt(My_Dataset, "select Xl_Code,Xl_Name,Xl_Jc FROM Zd_Ml_Yp1Xl where Dl_Code='" & Rtree.SelectedNode.Tag & "'", "类型", True)
            End If
        End If
        Dim My_Combo3 As New BaseClass.C_Combo2(C1Combo3, My_Dataset.Tables("类型").DefaultView, "Xl_Name", "Xl_Code", 386)
        With My_Combo3
            .Init_TDBCombo()
            .Init_Colum("Xl_Jc", "简称", 30, "左")
            .Init_Colum("Xl_Name", "类型名称", 100, "左")
            .Init_Colum("Xl_Code", "编码", 0, "左")
            .MaxDropDownItems(12)
            .SelectedIndex(-1)
        End With
        With C1Combo3
            .AutoCompletion = False
            .AutoSelect = False
        End With


        Dim My_Combo1 As New BaseClass.C_Combo1(Me.C1Combo2)
        My_Combo1.Init_TDBCombo()
        With C1Combo2
            .AddItem("国家基本药物")
            .AddItem("省增补基本药物")
            .AddItem("非基本药物")
            .AddItem("其他")
            .SelectedIndex = 0
            .Width = 121
            .DropDownWidth = 121
        End With


        Dim My_Combo4 As New BaseClass.C_Combo1(Me.C1Combo4)
        My_Combo4.Init_TDBCombo()
        With C1Combo4
            .AddItem("非特殊药品")
            .AddItem("麻醉药品")
            .AddItem("一类精神药品")
            .AddItem("二类精神药品")
            .AddItem("医疗用毒性药品")
            .AddItem("疫苗")

            .SelectedIndex = 0
            .Width = 169
            .DropDownWidth = 169
        End With

        '按扭初始化
        My_Button = New C_Button(Comm1, Comm2)
        My_Button.Init_Button(Panel1.Left + Panel1.Width / 2, 4)


    End Sub

#Region "数据__操作"

    Private Sub Data_Clear()
        Rinsert = True
        My_Cc.Get_MaxCode("Zd_Ml_Yp2", "Yp_Code", 8, "Dl_Code", RDl_Code)
        L_Dl_Code.Text = My_Cc.编码
        C1TextBox1.Text = ""
        C1TextBox2.Text = ""
        C1TextBox3.Text = ""
        C1Combo1.SelectedIndex = -1
        C1Combo2.Text = "国家基本药品"
        C1Combo3.SelectedIndex = -1

    End Sub

    Private Sub Data_Show(ByVal tmp_Row As DataRow)
        If HisPara.PublicConfig.IsCenterDb = "是" Then
            C1TextBox1.BackColor = SystemColors.Info
            C1TextBox1.Enabled = False
        End If
        Rinsert = False
        Rrow = tmp_Row
        With Rrow
            L_Dl_Code.Text = .Item("Yp_Code") & ""
            C1TextBox1.Text = .Item("Yp_Name") & ""
            C1TextBox2.Text = .Item("Yp_Jc") & ""
            C1TextBox3.Text = .Item("Yp_Memo") & ""
            C1Combo1.SelectedValue = .Item("Gx_Code")
            C1Combo2.Text = .Item("IsJb") & ""
            C1Combo4.Text = .Item("Special") & ""
            C1Combo3.SelectedValue = .Item("Xl_Code") & ""
        End With
    End Sub



#End Region

#Region "控件__动作"

    Private Sub Comm_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Comm1.Click, Comm2.Click
        Select Case sender.tag

            Case "保存"
                If Trim(C1TextBox1.Text & "") = "" Then
                    MsgBox("药品名称不能为空,按任意键返回！", MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
                    C1TextBox1.Focus()
                    Exit Sub
                End If

                If Rinsert = True Then Call Data_Add() Else Call Data_Edit()

            Case "取消"
                Me.Close()

        End Select
    End Sub

    Private Sub Move_Click(ByVal sender As Object, ByVal e As C1.Win.C1Command.ClickEventArgs)
        If sender.text = "新增" Then                            '增加状态
            Call Data_Clear()
        Else
            With Rtdbgrid
                If .RowCount = 0 Then Exit Sub
                Select Case sender.text
                    Case "最前"
                        .MoveFirst()
                    Case "上移"
                        .MovePrevious()
                    Case "下移"
                        .MoveNext()
                    Case "最后"
                        .MoveLast()
                End Select
                'Call Data_Show()
            End With
        End If
    End Sub

    Private Sub TextBox_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles C1TextBox1.KeyPress, C1TextBox2.KeyPress, C1TextBox3.KeyPress, C1Combo2.KeyPress
        If e.KeyChar <> Chr(Keys.Return) Then Exit Sub
        e.Handled = True
        System.Windows.Forms.SendKeys.Send("{Tab}")
    End Sub

    Private Sub C1TextBox1_Validated(ByVal sender As Object, ByVal e As System.EventArgs) Handles C1TextBox1.Validated
        My_Cc.Get_Py(Me.C1TextBox1.Text & "")
        C1TextBox2.Text = My_Cc.简拚.ToString
    End Sub
#End Region

#Region "数据__编辑"

    Private Sub Data_Add()

        Dim My_NewRow As DataRow = RZbtb.NewRow
        With My_NewRow
            .Item("Dl_Code") = Rtree.SelectedNode.Tag
            My_Cc.Get_MaxCode("Zd_Ml_Yp2", "Yp_Code", 8, "Dl_Code", Rtree.SelectedNode.Tag)
            .Item("Yp_Code") = My_Cc.编码
            .Item("Yp_Name") = Trim(C1TextBox1.Text & "")
            .Item("Yp_Jc") = Trim(C1TextBox2.Text & "")
            If C1Combo1.SelectedValue <> "" Then
                .Item("Gx_Code") = Trim(C1Combo1.SelectedValue & "")
                .Item("Gx_Name") = Trim(C1Combo1.Text & "")
            Else
                .Item("Gx_Code") = DBNull.Value
                .Item("Gx_Name") = ""
            End If

            If C1Combo3.SelectedValue <> "" Then
                .Item("Xl_Code") = Trim(C1Combo3.SelectedValue & "")
                .Item("Xl_Name") = Trim(C1Combo3.Text & "")
            Else
                .Item("Xl_Code") = DBNull.Value
                .Item("Xl_Name") = ""
            End If

            .Item("Yp_Memo") = Trim(C1TextBox3.Text & "")
            .Item("IsJb") = Trim(C1Combo2.Text & "")
            .Item("Special") = Trim(C1Combo4.Text & "")
        End With
        Call Data_SaveAdd(My_NewRow)
        Call Data_Clear()
        Call Tree_Add()
    End Sub

    Private Sub Tree_Add()
        R_Count = R_Count + 1
        Rtree.TopNode.Text = "药品类别(" + R_Count.ToString + ")"

        Dim V_NodeText As String = Rtree.SelectedNode.Text         '当前选中的节点
        V_NodeText = Mid(V_NodeText, 1, InStr(V_NodeText, "(") - 1)
        Rtree.SelectedNode.Text = V_NodeText + "(" + Rtdbgrid.Splits(0).Rows.Count.ToString + ")"
    End Sub

    Private Sub Data_Edit()
        Dim My_Row As DataRow = Rrow
        With My_Row
            .BeginEdit()
            .Item("Yp_Name") = Trim(C1TextBox1.Text & "")
            .Item("Yp_Jc") = Trim(C1TextBox2.Text & "")
            .Item("Yp_Memo") = Trim(C1TextBox3.Text & "")
            If C1Combo1.SelectedValue <> "" Then
                .Item("Gx_Code") = Trim(C1Combo1.SelectedValue & "")
                .Item("Gx_Name") = Trim(C1Combo1.Text & "")
            Else
                .Item("Gx_Code") = DBNull.Value
                .Item("Gx_Name") = ""
            End If

            If C1Combo3.SelectedValue <> "" Then
                .Item("Xl_Code") = Trim(C1Combo3.SelectedValue & "")
                .Item("Xl_Name") = Trim(C1Combo3.Text & "")
            Else
                .Item("Xl_Code") = DBNull.Value
                .Item("Xl_Name") = ""
            End If

            .Item("IsJb") = Trim(C1Combo2.Text & "")
            .Item("Special") = Trim(C1Combo4.Text & "")
            .EndEdit()
        End With
        Call Data_SaveEdit(My_Row)
    End Sub

    Private Sub Data_SaveAdd(ByVal My_Row As DataRow)        '数据保存
        Try
            RZbtb.Rows.Add(My_Row)
            Rtdbgrid.MoveLast()
            Rlb.Text = "∑=" + Rtdbgrid.Splits(0).Rows.Count.ToString
        Catch ex As Exception
            Beep()
            MsgBox(ex.Message, MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
            C1TextBox1.Select()
            Exit Sub
        End Try

        Dim Para(8) As SqlClient.SqlParameter
        Para(0) = New SqlParameter("@Dl_Code", SqlDbType.Char)
        Para(1) = New SqlParameter("@Yp_Code", SqlDbType.VarChar)
        Para(2) = New SqlParameter("@Yp_Name", SqlDbType.VarChar)
        Para(3) = New SqlParameter("@Yp_Jc", SqlDbType.VarChar)
        Para(4) = New SqlParameter("@Gx_Code", SqlDbType.Char)
        Para(5) = New SqlParameter("@Yp_Memo", SqlDbType.VarChar)
        Para(6) = New SqlParameter("@IsJb", SqlDbType.VarChar)
        Para(7) = New SqlParameter("@Special", SqlDbType.VarChar)
        Para(8) = New SqlParameter("@Xl_Code", SqlDbType.VarChar)

        Para(0).Value = My_Row.Item("Dl_Code")
        Para(1).Value = My_Row.Item("Yp_Code")
        Para(2).Value = My_Row.Item("Yp_Name")
        Para(3).Value = My_Row.Item("Yp_Jc")
        Para(4).Value = My_Row.Item("Gx_Code")
        Para(5).Value = My_Row.Item("Yp_Memo")
        Para(6).Value = My_Row.Item("IsJb")
        Para(7).Value = My_Row.Item("Special")
        Para(8).Value = My_Row.Item("Xl_Code")
        Try

            HisVar.HisVar.Sqldal.ExecuteSql("Insert Into Zd_Ml_Yp2(Dl_Code,Yp_Code,Yp_Name,Yp_Jc,Gx_Code,Yp_Memo,IsJb,Special,Xl_Code)Values(@Dl_Code,@Yp_Code,@Yp_Name,@Yp_Jc,@Gx_Code,@Yp_Memo,@IsJb,@Special,@Xl_Code)", Para)

            My_Row.AcceptChanges()
        Catch ex As Exception
            Beep()
            MsgBox(ex.Message, MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
            C1TextBox1.Select()
        Finally
            Call P_Conn(False)
        End Try
    End Sub

    Private Sub Data_SaveEdit(ByVal My_Row As DataRow)        '数据更新
        Try
            Dim Para(8) As SqlClient.SqlParameter
            Para(0) = New SqlParameter("@Dl_Code", SqlDbType.Char)
            Para(1) = New SqlParameter("@Yp_Name", SqlDbType.VarChar)
            Para(2) = New SqlParameter("@Yp_Jc", SqlDbType.VarChar)
            Para(3) = New SqlParameter("@Gx_Code", SqlDbType.Char)
            Para(4) = New SqlParameter("@Yp_Memo", SqlDbType.VarChar)
            Para(5) = New SqlParameter("@IsJb", SqlDbType.VarChar)
            Para(6) = New SqlParameter("@Special", SqlDbType.VarChar)
            Para(7) = New SqlParameter("@Old_Yp_Code", SqlDbType.VarChar)
            Para(8) = New SqlParameter("@Xl_Code", SqlDbType.VarChar)

            Para(0).Value = My_Row.Item("Dl_Code")
            Para(1).Value = My_Row.Item("Yp_Name")
            Para(2).Value = My_Row.Item("Yp_Jc")
            Para(3).Value = My_Row.Item("Gx_Code")
            Para(4).Value = My_Row.Item("Yp_Memo")
            Para(5).Value = My_Row.Item("IsJb")
            Para(6).Value = My_Row.Item("Special")
            Para(7).Value = My_Row.Item("Yp_Code", DataRowVersion.Original)
            Para(8).Value = My_Row.Item("Xl_Code")

            HisVar.HisVar.Sqldal.ExecuteSql("Update  Zd_Ml_Yp2 Set Yp_Name=@Yp_Name,Yp_Jc=@Yp_Jc,Gx_Code=@Gx_Code,Yp_Memo=@Yp_Memo,IsJb=@IsJb,Special=@Special,Xl_Code=@Xl_Code Where Yp_Code=@Old_Yp_Code", Para)

            My_Row.AcceptChanges()
        Catch ex As Exception
            Beep()
            MsgBox(ex.Message, MsgBoxStyle.Critical + MsgBoxStyle.OkOnly, "提示:")
        Finally
            Call P_Conn(False)
            C1TextBox1.Select()
        End Try

    End Sub

#End Region

#Region "自定义按扭"

    Private Sub Comm_KeyDown(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles Comm1.KeyDown, Comm2.KeyDown
        If e.KeyCode = Keys.Enter Or e.KeyCode = Keys.Space Then My_Button.KeyDown(sender.tag)
    End Sub

    Private Sub Comm_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles Comm1.KeyUp, Comm2.KeyUp
        If e.KeyCode = Keys.Enter Or e.KeyCode = Keys.Space Then My_Button.KeyUp(sender.tag)
    End Sub

    Private Sub Comm_MouseEnter(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Comm1.MouseEnter, Comm2.MouseEnter
        My_Button.MouseEnter(sender.tag)
    End Sub

    Private Sub Comm_MouseLeave(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Comm1.MouseLeave, Comm2.MouseLeave
        My_Button.MouseLeave(sender.tag)
    End Sub

    Private Sub Comm_MouseDown(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles Comm1.MouseDown, Comm2.MouseDown
        My_Button.MouseDown(sender.tag)
    End Sub

    Private Sub Comm_MouseUp(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles Comm1.MouseUp, Comm2.MouseUp
        My_Button.MouseUp(sender.tag)
    End Sub

#End Region

#Region "Combo1动作"

    Private Sub C1Combo1_GotFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles C1Combo1.GotFocus
        Me.CancelButton = Nothing
    End Sub

    Private Sub C1Combo1_Validated(ByVal sender As Object, ByVal e As System.EventArgs) Handles C1Combo1.Validated
        Me.CancelButton = Comm2
    End Sub

    Private Sub C1Combo1_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles C1Combo1.KeyUp

        If (e.KeyValue > 47 And e.KeyValue < 58) Or (e.KeyValue > 96 And e.KeyValue < 123) Or (e.KeyValue > 64 And e.KeyValue < 91) Or (e.KeyValue = 8) Or (e.KeyValue = 189) Or (e.KeyValue = 190) Then
            Dim s As String = C1Combo1.Text
            If C1Combo1.Text = "" Then
                C1Combo1.DataSource.RowFilter = ""
            Else
                C1Combo1.DataSource.RowFilter = "Gx_Jc like '*" & C1Combo1.Text.Replace("*", "[*]") & "*'"
            End If
            If (e.KeyValue = 8) Then
                C1Combo1.DroppedDown = False
                C1Combo1.DroppedDown = True
            End If

            C1Combo1.Text = s
            C1Combo1.SelectionStart = C1Combo1.Text.Length
            C1Combo1.SelectionLength = 0
        End If

    End Sub

    Private Sub C1Combo1_KeyDown(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles C1Combo1.KeyDown
        If e.KeyValue = 13 Then
            If C1Combo1.WillChangeToIndex < 1 Then
                If (CType(C1Combo1.DataSource, DataView).Count) = 0 Then
                    MsgBox("功效: '" + Me.C1Combo1.Text + "' 不存在！", MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
                    System.Windows.Forms.SendKeys.Send("^{A}")
                    Exit Sub
                End If
                C1Combo1.SelectedIndex = -1
                C1Combo1.SelectedIndex = 0
            Else
                Dim index As Integer
                index = C1Combo1.WillChangeToIndex
                C1Combo1.SelectedIndex = -1
                C1Combo1.SelectedIndex = index
            End If
            e.Handled = True
            System.Windows.Forms.SendKeys.Send("{Tab}")
        End If
    End Sub

#End Region

#Region "输入法设置"
    '中文
    Private Sub C1TextBox1_Enter(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles C1TextBox1.GotFocus, C1TextBox3.GotFocus
        InputLanguage.CurrentInputLanguage = HisVar.HisVar.InputCode
    End Sub
    '英文
    Private Sub C1Combo1_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles C1Combo1.GotFocus
        InputLanguage.CurrentInputLanguage = InputLanguage.InstalledInputLanguages(0)
    End Sub


#End Region


End Class