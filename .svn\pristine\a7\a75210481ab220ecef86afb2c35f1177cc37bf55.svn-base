﻿Imports BaseClass
Imports System.Windows.Forms
Imports System.Drawing
Public Class MBISCondition
    Public outputstr As String
    Dim str_Select As String
    Dim My_Dataset As New DataSet            '数据库
    Dim BllMaterDict As New BLLOld.B_Materials_Dict
    Dim bllMateWarehDict As New BLLOld.B_Materials_Warehouse_Dict
    Dim bllMaterialsSupDict As New BLLOld.B_Materials_Sup_Dict

    Dim bllYYJsr As New BLLOld.B_Zd_YyJsr

    Private Sub MSSCondition_Load(sender As System.Object, e As System.EventArgs) Handles MyBase.Load

        FormInt()

    End Sub
   

#Region "自定义函数"
    Private Function cx()
        str_Select = ""
        If dhCheckBox1.Checked = True Then
            If DHDoubleDateEdit.StartValue Is DBNull.Value And DHDoubleDateEdit.EndValue Is DBNull.Value Then
            ElseIf DHDoubleDateEdit.StartValue Is DBNull.Value Then
                str_Select += " and Order_Date <= '" & DHDoubleDateEdit.EndValue & "' "
            ElseIf DHDoubleDateEdit.EndValue Is DBNull.Value Then
                str_Select += " and Order_Date >= '" & DHDoubleDateEdit.StartValue & "' "
            Else
                str_Select += " and  Order_Date BETWEEN '" & DHDoubleDateEdit.StartValue & "' AND '" & DHDoubleDateEdit.EndValue & "' "
            End If


        End If
        If dahCheckBox2.Checked = True Then
            If DAHDoubleDateEdit.StartValue Is DBNull.Value And DAHDoubleDateEdit.EndValue Is DBNull.Value Then
            ElseIf DAHDoubleDateEdit.StartValue Is DBNull.Value Then
                str_Select += " and Arrival_Date <= '" & DAHDoubleDateEdit.EndValue & "' "
            ElseIf DAHDoubleDateEdit.EndValue Is DBNull.Value Then
                str_Select += " and Arrival_Date >= '" & DAHDoubleDateEdit.StartValue & "' "
            Else
                str_Select += " and  Arrival_Date BETWEEN '" & DAHDoubleDateEdit.StartValue & "' AND '" & DAHDoubleDateEdit.EndValue & "' "
            End If


        End If
        If lrCheckBox3.Checked = True Then
            If LrDoubleDateEdit.StartValue Is DBNull.Value And LrDoubleDateEdit.EndValue Is DBNull.Value Then
            ElseIf LrDoubleDateEdit.StartValue Is DBNull.Value Then
                str_Select += " and Input_Date <= '" & LrDoubleDateEdit.EndValue & "' "
            ElseIf LrDoubleDateEdit.EndValue Is DBNull.Value Then
                str_Select += " and Input_Date >= '" & LrDoubleDateEdit.StartValue & "' "
            Else
                str_Select += " and  Input_Date BETWEEN '" & LrDoubleDateEdit.StartValue & "' AND '" & LrDoubleDateEdit.EndValue & "' "
            End If


        End If
        If wcCheckBox4.Checked = True Then
            If WCDoubleDateEdit.StartValue Is DBNull.Value And WCDoubleDateEdit.EndValue Is DBNull.Value Then
            ElseIf WCDoubleDateEdit.StartValue Is DBNull.Value Then
                str_Select += " and Finish_Date <= '" & WCDoubleDateEdit.EndValue & "' "
            ElseIf WCDoubleDateEdit.EndValue Is DBNull.Value Then
                str_Select += " and Finish_Date >= '" & WCDoubleDateEdit.StartValue & "' "
            Else
                str_Select += " and  Finish_Date BETWEEN '" & WCDoubleDateEdit.StartValue & "' AND '" & WCDoubleDateEdit.EndValue & "' "
            End If


        End If

        If DJZTSingleComobo.SelectedText <> "全部" Then
            str_Select += " and OrdersStatus='" & DJZTSingleComobo.Text & "' "
        End If
        If CXZTSingleComobo.SelectedText <> "全部" Then
            str_Select += " and WriteOffStatus='" & CXZTSingleComobo.SelectedText & "' "
        End If
        If KFDtComobo.Text <> "" Then
            str_Select += " and Materials_Buy_In1.MaterialsWh_Code='" & KFDtComobo.SelectedValue & "' "
        End If
        If WZMCDtComobo.Text <> "" Then
            str_Select += " and Materials_Name='" & WZMCDtComobo.Text & "' "
        End If
        If GYSDtComobo.Text <> "" Then
            str_Select += " and Materials_Buy_In1.MaterialsSup_Code='" & GYSDtComobo.SelectedValue & "' "
        End If
        If SCCJTextBox1.Text <> "" Then
            str_Select += " and MateManu_Name = '" & SCCJTextBox1.Text & "' "
        End If
        If JSRDtComobo.Text <> "" Then
            str_Select += " and Materials_Buy_In1.Jsr_Code='" & JSRDtComobo.SelectedValue & "' "
        End If
        If WZPHTextBox1.Text <> "" Then
            str_Select += " and MaterialsLot='" & WZPHTextBox1.Text & "' "
        End If
        If GGTextBox.Text <> "" Then
            str_Select += " and Materials_Spec='" & GGTextBox.Text & "' "
        End If
        If WZLBDtComobo1.Text <> "" Then
            str_Select += " and Class_Code='" & WZLBDtComobo1.SelectedValue & "' "
        End If
        Return str_Select
    End Function
    Private Sub ccconfig()

        iniOperate.iniopreate.WriteINI("采购查询参数", "订货日期1", DHDoubleDateEdit.StartValue & "", HisVar.HisVar.Parapath & "\Config.ini")
        iniOperate.iniopreate.WriteINI("采购查询参数", "订货日期2", DHDoubleDateEdit.EndValue & "", HisVar.HisVar.Parapath & "\Config.ini")
        iniOperate.iniopreate.WriteINI("采购查询参数", "到货日期1", DAHDoubleDateEdit.StartValue & "", HisVar.HisVar.Parapath & "\Config.ini")
        iniOperate.iniopreate.WriteINI("采购查询参数", "到货日期2", DAHDoubleDateEdit.EndValue & "", HisVar.HisVar.Parapath & "\Config.ini")
        iniOperate.iniopreate.WriteINI("采购查询参数", "录入日期1", LrDoubleDateEdit.StartValue & "", HisVar.HisVar.Parapath & "\Config.ini")
        iniOperate.iniopreate.WriteINI("采购查询参数", "录入日期2", LrDoubleDateEdit.EndValue & "", HisVar.HisVar.Parapath & "\Config.ini")
        iniOperate.iniopreate.WriteINI("采购查询参数", "完成日期1", WCDoubleDateEdit.StartValue & "", HisVar.HisVar.Parapath & "\Config.ini")
        iniOperate.iniopreate.WriteINI("采购查询参数", "完成日期2", WCDoubleDateEdit.EndValue & "", HisVar.HisVar.Parapath & "\Config.ini")
        iniOperate.iniopreate.WriteINI("采购查询参数", "单据状态", DJZTSingleComobo.SelectedIndex, HisVar.HisVar.Parapath & "\Config.ini")
        iniOperate.iniopreate.WriteINI("采购查询参数", "库房名称", KFDtComobo.SelectedValue, HisVar.HisVar.Parapath & "\Config.ini")
        iniOperate.iniopreate.WriteINI("采购查询参数", "物资名称", WZMCDtComobo.Text, HisVar.HisVar.Parapath & "\Config.ini")
        iniOperate.iniopreate.WriteINI("采购查询参数", "供应商", GYSDtComobo.SelectedValue, HisVar.HisVar.Parapath & "\Config.ini")
        iniOperate.iniopreate.WriteINI("采购查询参数", "经手人", JSRDtComobo.SelectedValue, HisVar.HisVar.Parapath & "\Config.ini")
        iniOperate.iniopreate.WriteINI("采购查询参数", "冲销状态", CXZTSingleComobo.SelectedIndex, HisVar.HisVar.Parapath & "\Config.ini")
        iniOperate.iniopreate.WriteINI("采购查询参数", "物资批号", WZPHTextBox1.Text, HisVar.HisVar.Parapath & "\Config.ini")
        iniOperate.iniopreate.WriteINI("采购查询参数", "规格", GGTextBox.Text, HisVar.HisVar.Parapath & "\Config.ini")
        iniOperate.iniopreate.WriteINI("采购查询参数", "生产厂家", SCCJTextBox1.Text, HisVar.HisVar.Parapath & "\Config.ini")
        iniOperate.iniopreate.WriteINI("采购查询参数", "订货日期", dhCheckBox1.Checked, HisVar.HisVar.Parapath & "\Config.ini")
        iniOperate.iniopreate.WriteINI("采购查询参数", "到货日期", dahCheckBox2.Checked, HisVar.HisVar.Parapath & "\Config.ini")
        iniOperate.iniopreate.WriteINI("采购查询参数", "录入日期", lrCheckBox3.Checked, HisVar.HisVar.Parapath & "\Config.ini")
        iniOperate.iniopreate.WriteINI("采购查询参数", "完成日期", wcCheckBox4.Checked, HisVar.HisVar.Parapath & "\Config.ini")
        iniOperate.iniopreate.WriteINI("采购查询参数", "物资类别", WZLBDtComobo1.SelectedValue, HisVar.HisVar.Parapath & "\Config.ini")
    End Sub
#End Region
#Region "控件动作"

    Private Sub MyButton1_Click(sender As System.Object, e As System.EventArgs) Handles MyButton1.Click
        SCCJTextBox1.Text = ""
        WZPHTextBox1.Text = ""
        DHDoubleDateEdit.StartValue = Now
        DHDoubleDateEdit.EndValue = Now
        DJZTSingleComobo.SelectedIndex = 0
        JSRDtComobo.SelectedValue = -1
        DAHDoubleDateEdit.StartValue = Now
        DAHDoubleDateEdit.EndValue = Now
        KFDtComobo.SelectedValue = -1
        WZMCDtComobo.Text = ""
        GYSDtComobo.SelectedValue = -1
        WCDoubleDateEdit.EndValue = Now
        WCDoubleDateEdit.StartValue = Now
        LrDoubleDateEdit.EndValue = Now
        LrDoubleDateEdit.StartValue = Now
        CXZTSingleComobo.SelectedIndex = 0
        WZLBDtComobo1.SelectedValue = -1
        GGTextBox.Text = ""
        dahCheckBox2.Checked = False

        wcCheckBox4.Checked = False
        lrCheckBox3.Checked = False

        dhCheckBox1.Checked = False
    End Sub
    Private Sub QDButton_Click(sender As System.Object, e As System.EventArgs) Handles QDButton.Click
        ccconfig()
        outputstr = cx()

    End Sub
    Private Sub dhCheckBox1_CheckedChanged(sender As Object, e As System.EventArgs) Handles dhCheckBox1.CheckedChanged
        If dhCheckBox1.Checked = False Then
            DHDoubleDateEdit.Enabled = False
        Else
            DHDoubleDateEdit.Enabled = True
        End If
    End Sub

    Private Sub dahCheckBox2_CheckedChanged(sender As Object, e As System.EventArgs) Handles dahCheckBox2.CheckedChanged
        If dahCheckBox2.Checked = False Then
            DAHDoubleDateEdit.Enabled = False
        Else
            DAHDoubleDateEdit.Enabled = True
        End If

    End Sub

    Private Sub lrCheckBox3_CheckedChanged(sender As Object, e As System.EventArgs) Handles lrCheckBox3.CheckedChanged
        If lrCheckBox3.Checked = False Then
            LrDoubleDateEdit.Enabled = False
        Else
            LrDoubleDateEdit.Enabled = True
        End If
    End Sub

    Private Sub wcCheckBox4_CheckedChanged(sender As Object, e As System.EventArgs) Handles wcCheckBox4.CheckedChanged
        If wcCheckBox4.Checked = False Then
            WCDoubleDateEdit.Enabled = False
        Else
            WCDoubleDateEdit.Enabled = True
        End If
    End Sub



    Private Sub QXButton_Click(sender As System.Object, e As System.EventArgs) Handles QXButton.Click
        Me.Close()
    End Sub
#End Region
#Region "窗体初始化"
    Private Sub FormInt()
        DHDoubleDateEdit.Enabled = False
        DAHDoubleDateEdit.Enabled = False
        LrDoubleDateEdit.Enabled = False
        WCDoubleDateEdit.Enabled = False

        If iniOperate.iniopreate.GetINI("采购查询参数", "订货日期", "", HisVar.HisVar.Parapath & "\Config.ini") & "" <> "" Then
            dhCheckBox1.Checked = iniOperate.iniopreate.GetINI("采购查询参数", "订货日期", "", HisVar.HisVar.Parapath & "\Config.ini") & ""
        Else
            dhCheckBox1.Checked = False
        End If
        If iniOperate.iniopreate.GetINI("采购查询参数", "到货日期", "", HisVar.HisVar.Parapath & "\Config.ini") & "" <> "" Then
            dahCheckBox2.Checked = iniOperate.iniopreate.GetINI("采购查询参数", "到货日期", "", HisVar.HisVar.Parapath & "\Config.ini") & ""
        Else
            dahCheckBox2.Checked = False
        End If
        If iniOperate.iniopreate.GetINI("采购查询参数", "录入日期", "", HisVar.HisVar.Parapath & "\Config.ini") & "" <> "" Then
            lrCheckBox3.Checked = iniOperate.iniopreate.GetINI("采购查询参数", "录入日期", "", HisVar.HisVar.Parapath & "\Config.ini") & ""
        Else
            lrCheckBox3.Checked = False
        End If
        If iniOperate.iniopreate.GetINI("采购查询参数", "完成日期", "", HisVar.HisVar.Parapath & "\Config.ini") & "" <> "" Then
            wcCheckBox4.Checked = iniOperate.iniopreate.GetINI("采购查询参数", "完成日期", "", HisVar.HisVar.Parapath & "\Config.ini") & ""
        Else
            wcCheckBox4.Checked = False
        End If




        DHDoubleDateEdit.StartValue = iniOperate.iniopreate.GetINI("采购查询参数", "订货日期1", "", HisVar.HisVar.Parapath & "\Config.ini") & ""
        DHDoubleDateEdit.EndValue = iniOperate.iniopreate.GetINI("采购查询参数", "订货日期2", "", HisVar.HisVar.Parapath & "\Config.ini") & ""

        DAHDoubleDateEdit.StartValue = iniOperate.iniopreate.GetINI("采购查询参数", "到货日期1", "", HisVar.HisVar.Parapath & "\Config.ini") & ""
        DAHDoubleDateEdit.EndValue = iniOperate.iniopreate.GetINI("采购查询参数", "到货日期2", "", HisVar.HisVar.Parapath & "\Config.ini") & ""


        LrDoubleDateEdit.StartValue = iniOperate.iniopreate.GetINI("采购查询参数", "录入日期1", "", HisVar.HisVar.Parapath & "\Config.ini") & ""
        LrDoubleDateEdit.EndValue = iniOperate.iniopreate.GetINI("采购查询参数", "录入日期2", "", HisVar.HisVar.Parapath & "\Config.ini") & ""


        WCDoubleDateEdit.StartValue = iniOperate.iniopreate.GetINI("采购查询参数", "完成日期1", "", HisVar.HisVar.Parapath & "\Config.ini") & ""
        WCDoubleDateEdit.EndValue = iniOperate.iniopreate.GetINI("采购查询参数", "完成日期2", "", HisVar.HisVar.Parapath & "\Config.ini") & ""




        With WZMCDtComobo
            .DataView = BllMaterDict.GetAList().Tables(0).DefaultView
            .Init_Colum("Materials_Name", "物资名称", 80, "左")
            .Init_Colum("Materials_Py", "物资拼音", 60, "左")
            .DisplayMember = "Materials_Name"
            .RowFilterNotTextNull = "Materials_Py"
            .RowFilterTextNull = ""
            .DroupDownWidth = .Width - 40
            .MaxDropDownItems = 15
            .SelectedValue = -1
            If iniOperate.iniopreate.GetINI("采购查询参数", "物资名称", "", HisVar.HisVar.Parapath & "\Config.ini") & "" <> "" Then
                .Text = iniOperate.iniopreate.GetINI("采购查询参数", "物资名称", "", HisVar.HisVar.Parapath & "\Config.ini")
            Else
                .SelectedValue = -1
            End If
        End With

        Dim bllMaterialClass_dict As New BLLOld.B_Materials_Class_Dict
        With WZLBDtComobo1
            .DataView = bllMaterialClass_dict.GetList(" HaveChild=0 ").Tables(0).DefaultView
            .Init_Colum("Class_Code", "物资类别编码", 0, "左")
            .Init_Colum("Class_Name", "物资类别名称", 80, "左")
            .Init_Colum("Class_Py", "物资类别拼音", 80, "左")
            .DisplayMember = "Class_Name"
            .ValueMember = "Class_Code"
            .RowFilterNotTextNull = "Class_Py"
            .RowFilterTextNull = ""
            .DroupDownWidth = .Width - 40
            .MaxDropDownItems = 15
            If iniOperate.iniopreate.GetINI("采购查询参数", "物资类别", "", HisVar.HisVar.Parapath & "\Config.ini") & "" <> "" Then
                .SelectedValue = iniOperate.iniopreate.GetINI("采购查询参数", "物资类别", "", HisVar.HisVar.Parapath & "\Config.ini")

            Else
                .SelectedValue = -1
            End If
        End With
        With KFDtComobo
            .DataView = bllMateWarehDict.GetAllList().Tables(0).DefaultView
            .Init_Colum("MaterialsWh_Code", "库房编码", 0, "左")
            .Init_Colum("MaterialsWh_Name", "库房名称", 80, "左")
            .Init_Colum("MaterialsWh_Py", "库房拼音", 60, "左")
            .DisplayMember = "MaterialsWh_Name"
            .ValueMember = "MaterialsWh_Code"
            .RowFilterNotTextNull = "MaterialsWh_Py"
            .RowFilterTextNull = ""
            .DroupDownWidth = .Width - 40
            .MaxDropDownItems = 15
            If iniOperate.iniopreate.GetINI("采购查询参数", "库房名称", "", HisVar.HisVar.Parapath & "\Config.ini") & "" <> "" Then
                .SelectedValue = iniOperate.iniopreate.GetINI("采购查询参数", "库房名称", "", HisVar.HisVar.Parapath & "\Config.ini")
            Else
                .SelectedValue = -1
            End If

        End With

        With GYSDtComobo
            .DataView = bllMaterialsSupDict.GetAllList().Tables(0).DefaultView
            .Init_Colum("MaterialsSup_Code", "供应商编码", 0, "左")
            .Init_Colum("MaterialsSup_Name", "供应商名称", 80, "左")
            .Init_Colum("MaterialsSup_Py", "供应商拼音", 60, "左")
            .DisplayMember = "MaterialsSup_Name"
            .ValueMember = "MaterialsSup_Code"
            .RowFilterNotTextNull = "MaterialsSup_Py"
            .RowFilterTextNull = ""
            .DroupDownWidth = .Width - 40
            .MaxDropDownItems = 15
            If iniOperate.iniopreate.GetINI("采购查询参数", "供应商", "", HisVar.HisVar.Parapath & "\Config.ini") & "" <> "" Then
                .SelectedValue = iniOperate.iniopreate.GetINI("采购查询参数", "供应商", "", HisVar.HisVar.Parapath & "\Config.ini")
            Else
                .SelectedValue = -1
            End If

        End With
        With JSRDtComobo
            .DataView = bllYYJsr.GetAllList().Tables(0).DefaultView
            .Init_Colum("Jsr_Code", "经手人编码", 0, "左")
            .Init_Colum("Jsr_Name", "经手人名称", 80, "左")
            .Init_Colum("Jsr_Jc", "经手人拼音", 60, "左")
            .DisplayMember = "Jsr_Name"
            .ValueMember = "Jsr_Code"
            .RowFilterNotTextNull = "Jsr_Jc"
            .RowFilterTextNull = ""
            .DroupDownWidth = .Width - 40
            .MaxDropDownItems = 15
            If iniOperate.iniopreate.GetINI("采购查询参数", "经手人", "", HisVar.HisVar.Parapath & "\Config.ini") & "" <> "" Then
                .SelectedValue = iniOperate.iniopreate.GetINI("采购查询参数", "经手人", "", HisVar.HisVar.Parapath & "\Config.ini")
            Else
                .SelectedValue = -1
            End If

        End With
        With DJZTSingleComobo
            .Additem = "全部"
            .Additem = "录入"
            .Additem = "完成"
            .DisplayColumns(1).Visible = False
            If iniOperate.iniopreate.GetINI("采购查询参数", "单据状态", "", HisVar.HisVar.Parapath & "\Config.ini") & "" <> "" Then
                .SelectedIndex = iniOperate.iniopreate.GetINI("采购查询参数", "单据状态", "", HisVar.HisVar.Parapath & "\Config.ini")
            Else
                .SelectedIndex = 0
            End If
        End With
        With CXZTSingleComobo
            .Additem = "全部"
            .Additem = "被冲销"
            .Additem = "冲销"
            .Additem = "未冲销"
            .DisplayColumns(1).Visible = False
            If iniOperate.iniopreate.GetINI("采购查询参数", "冲销状态", "", HisVar.HisVar.Parapath & "\Config.ini") & "" <> "" Then
                .SelectedIndex = iniOperate.iniopreate.GetINI("采购查询参数", "冲销状态", "", HisVar.HisVar.Parapath & "\Config.ini")
            Else
                .SelectedIndex = 0
            End If
        End With

        If iniOperate.iniopreate.GetINI("采购查询参数", "物资批号", "", HisVar.HisVar.Parapath & "\Config.ini") & "" <> "" Then
            WZPHTextBox1.Text = iniOperate.iniopreate.GetINI("采购查询参数", "物资批号", "", HisVar.HisVar.Parapath & "\Config.ini")
        Else
            WZPHTextBox1.Text = ""
        End If
        If iniOperate.iniopreate.GetINI("采购查询参数", "规格", "", HisVar.HisVar.Parapath & "\Config.ini") & "" <> "" Then
            GGTextBox.Text = iniOperate.iniopreate.GetINI("采购查询参数", "规格", "", HisVar.HisVar.Parapath & "\Config.ini")
        Else
            GGTextBox.Text = ""
        End If

        If iniOperate.iniopreate.GetINI("采购查询参数", "生产厂家", "", HisVar.HisVar.Parapath & "\Config.ini") & "" <> "" Then
            SCCJTextBox1.Text = iniOperate.iniopreate.GetINI("采购查询参数", "生产厂家", "", HisVar.HisVar.Parapath & "\Config.ini")
        Else
            SCCJTextBox1.Text = ""
        End If
    End Sub
#End Region



#Region "输入法设置"
    '中文
    Private Sub C1TextBox1_Enter(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles WZPHTextBox1.GotFocus, GGTextBox.GotFocus, SCCJTextBox1.GotFocus
        InputLanguage.CurrentInputLanguage = HisVar.HisVar.InputCode
    End Sub
    '英文
    Private Sub yw_Enter(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles KFDtComobo.GotFocus, GYSDtComobo.GotFocus, WZMCDtComobo.GotFocus, JSRDtComobo.GotFocus, WZLBDtComobo1.GotFocus
        InputLanguage.CurrentInputLanguage = InputLanguage.InstalledInputLanguages(0)
    End Sub

#End Region


End Class