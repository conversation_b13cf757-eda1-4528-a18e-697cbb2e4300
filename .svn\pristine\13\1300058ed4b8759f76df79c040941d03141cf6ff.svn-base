﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using Common.BaseForm;

namespace ERX
{
    public partial class SelectYs : BaseChild
    {
        public string YbYs_Code;
        public string Ys_Name;
        private string prac_psn_code;
        public SelectYs(string prac_psn_code)
        {
            InitializeComponent();
            this.prac_psn_code = prac_psn_code;
        }

        private void SelectYs_Load(object sender, EventArgs e)
        {
            combo_doctor_name1.Init($"prac_psn_code<>'{prac_psn_code}'");
        }

        private void BtnSave_Click(object sender, EventArgs e)
        {
            if (CustomControl.Func.NotAllowEmpty(combo_doctor_name1)) return;
            if (combo_doctor_name1.Columns["prac_psn_type"].Value + "" == "2")
            {
                if (MessageBox.Show($"选择的医护人员【{combo_doctor_name1.Columns["prac_psn_name"].Value }】是护士，是否继续", "警告", MessageBoxButtons.YesNo, MessageBoxIcon.Exclamation) == DialogResult.No)
                {
                    combo_doctor_name1.Select();
                    return;
                }
            }
            Ys_Name = combo_doctor_name1.Columns["prac_psn_name"].Value + "";
            YbYs_Code = combo_doctor_name1.Columns["prac_psn_code"].Value + "";
            this.DialogResult = DialogResult.OK;
        }
    }
}
