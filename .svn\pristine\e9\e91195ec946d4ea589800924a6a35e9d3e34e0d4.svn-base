﻿Imports System.Data.SqlClient
Imports Stimulsoft.Report
Imports Stimulsoft.Report.CrossTab

Public Class Cw_Cx_MzZy_Rj1

    Public My_Data As New DataSet
    Public My_Table As New DataTable
    Dim My_View As New DataView                 '数据视图
    Public My_Cm As CurrencyManager

    Public V_Str1 As String

#Region "控件__动作"
    '数据查询按钮的单击事件
    Private Sub Button1_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Button1.Click

        If C1Combo2.Text = "按日结单据查询" Then
            If TDBCombo1.Text = "住院" Then
                V_Str1 = "select '住院' as V_Lb,Jz_Date,Jz_Code,Jz_Time,Jsr_Name, Jz_Cy_Rs, Jz_Sj_M, Jz_Sz_M from Bl_Jz,Zd_Yyjsr where Bl_Jz.Yy_Code='" & HisVar.HisVar.WsyCode & "' and Bl_Jz.Jsr_Code=Zd_Yyjsr.Jsr_Code And Jz_Date+Jz_Time between'" & DateTimePicker1.Text & "' and '" & DateTimePicker2.Text & "'"
            Else
                V_Str1 = "select '门诊' as V_Lb,Jz_Date,Jz_Code,Jz_Time,Jsr_Name,Jz_Mz_Money,Jz_Th_Money,Jz_Sq_Money  from Mz_Jz,Zd_Yyjsr where Mz_Jz.Yy_Code='" & HisVar.HisVar.WsyCode & "' and Mz_Jz.Jsr_Code=Zd_Yyjsr.Jsr_Code And Jz_Date+Jz_Time between'" & DateTimePicker1.Text & "' and '" & DateTimePicker2.Text & "'"

            End If
            HisVar.HisVar.Sqldal.QueryDt(My_Data, V_Str1, "门诊住院日结查询", True)
        ElseIf C1Combo2.Text = "按收费明细查询" Then

            If TDBCombo1.Text = "住院" Then

                V_Str1 = "select Ks_Name,Ys_Name,Ry_Name,Bxlb_Name,Yp_Name,Cf_Lb as Mz_Lb,Cf_Date as Mz_Date,Cf_Time As Mz_Time,Cf_Sl as Mz_Sl,Bl_CfYp.Cf_Money as  M from Bl,Bl_Cf,Bl_CfYp,V_YpKc,Zd_Bxlb,Zd_Yyys,Zd_YyKs,Bl_Jz  where Bl_Jz.Jz_Code=Bl_Cf.Jz_Code and BL_Cf.Ks_Code=Zd_YyKs.Ks_Code and BL_Cf.Ys_Code=Zd_Yyys.Ys_Code and  Bl.Bl_Code=Bl_Cf.Bl_Code and Bl.Bxlb_Code=Zd_Bxlb.Bxlb_Code and Bl_Cf.Yy_Code='" & HisVar.HisVar.WsyCode & "'  and Bl_Cf.Cf_Code=Bl_CfYP.Cf_Code and Bl_CfYP.Xx_code=V_YpKc.Xx_code And Jz_Date+Jz_Time>='" & Format(DateTimePicker1.Value, "yyyy-MM-dd HH:mm:ss") & "' And Jz_Date+Jz_Time<='" & Format(DateTimePicker2.Value, "yyyy-MM-dd HH:mm:ss") & "'" & _
          " union all select Ks_Name,Ys_Name,Ry_Name,Bxlb_Name,Xm_Name,Cf_Lb as Mz_Lb,Cf_Date as Mz_Date,Cf_Time as Mz_Time,Cf_Sl as Mz_Sl,Bl_CfXm.Cf_Money as M from Bl,Bl_Cf,Bl_CfXm,Zd_Ml_Xm3,Zd_Bxlb,Zd_Yyys ,Zd_YyKs,Bl_Jz  where Bl_Jz.Jz_Code=Bl_Cf.Jz_Code and  BL_Cf.Ks_Code=Zd_YyKs.Ks_Code and BL_Cf.Ys_Code=Zd_Yyys.Ys_Code and Bl.Bl_Code=Bl_Cf.Bl_Code and Bl.Bxlb_Code=Zd_Bxlb.Bxlb_Code  and Bl_Cf.Yy_Code='" & HisVar.HisVar.WsyCode & "'  and Bl_Cf.Cf_Code=Bl_CfXm.Cf_Code and Bl_CfXm.Xm_Code=Zd_Ml_Xm3.Xm_Code And Jz_Date+Jz_Time>='" & Format(DateTimePicker1.Value, "yyyy-MM-dd HH:mm:ss") & "' And Jz_Date+Jz_Time<='" & Format(DateTimePicker2.Value, "yyyy-MM-dd HH:mm:ss") & "' order by Ys_Name,Bxlb_Name,Ry_Name"

            Else

                V_Str1 = "select Ks_Name,Ys_Name,Ry_Name,Bxlb_Name,Yp_Name, Mz_Lb,Mz_Date,Mz_Time,Mz_Sl,Mz_Yp_Sum.Mz_Money as M from Mz_Sum,Mz_Yp_Sum,V_YpKc,Zd_Bxlb,Zd_Yyys  ,Zd_YyKs,Mz_Jz  where Mz_Jz.Jz_Code=Mz_Sum.Jz_Code and Mz_Sum.Ks_Code=Zd_YyKs.Ks_Code and Mz_Sum.Ys_Code=Zd_Yyys.Ys_Code and Mz_Sum.Bxlb_Code=Zd_Bxlb.Bxlb_Code and Mz_Print=1 and Mz_Sum.Yy_Code='" & HisVar.HisVar.WsyCode & "'  and Mz_Sum.Mz_Code=Mz_Yp_Sum.Mz_Code and Mz_Yp_Sum.Xx_code=V_YpKc.Xx_code And Mz_Ph Not In (Select Mz_Ph From Mz_Ty_Sum ) And Jz_Date+Jz_Time>='" & Format(DateTimePicker1.Value, "yyyy-MM-dd HH:mm:ss") & "' And Jz_Date+Jz_Time<='" & Format(DateTimePicker2.Value, "yyyy-MM-dd HH:mm:ss") & "'" & _
                          " union all select Ks_Name,Ys_Name,Ry_Name,Bxlb_Name,Xm_Name, Mz_Lb,Mz_Date,Mz_Time,Mz_Sl,Mz_Xm_Sum.Mz_Money as M  from Mz_Sum,Mz_Xm_Sum,Zd_Ml_Xm3,Zd_Bxlb,Zd_Yyys  ,Zd_YyKs ,Mz_Jz  where Mz_Jz.Jz_Code=Mz_Sum.Jz_Code and Mz_Sum.Ks_Code=Zd_YyKs.Ks_Code and Mz_Sum.Ys_Code=Zd_Yyys.Ys_Code and Mz_Sum.Bxlb_Code=Zd_Bxlb.Bxlb_Code and Mz_Print=1  and Mz_Sum.Yy_Code='" & HisVar.HisVar.WsyCode & "' and Mz_Sum.Mz_Code=Mz_Xm_Sum.Mz_Code and Mz_Xm_Sum.Xm_Code=Zd_Ml_Xm3.Xm_Code And Mz_Ph Not In (Select Mz_Ph From Mz_Ty_Sum ) And Jz_Date+Jz_Time>='" & Format(DateTimePicker1.Value, "yyyy-MM-dd HH:mm:ss") & "' And Jz_Date+Jz_Time<='" & Format(DateTimePicker2.Value, "yyyy-MM-dd HH:mm:ss") & "' order by Ys_Name,Bxlb_Name,Ry_Name"

            End If
            HisVar.HisVar.Sqldal.QueryDt(My_Data, V_Str1, "日结明细", True)
        Else

            HisVar.HisVar.Sqldal.QueryDt(My_Data, " select Ks_Name,'X'+Dl_Code as Dl_Code,Dl_Name,Ry_Name,Yp_Name,Bl_CfYp.Cf_Money ,Bxlb_Name from Bl,Bl_Cf,Bl_CfYp,V_Ypkc,Zd_Yyks,Zd_Bxlb,Bl_Jz where  Bl.Jz_Code=Bl_Jz.Jz_Code and Bl.Bxlb_Code=Zd_Bxlb.Bxlb_Code and Bl.Bl_Code=Bl_Cf.Bl_Code And Bl.Yy_Code='" & HisVar.HisVar.WsyCode & "' And Bl_Cf.Cf_Code=Bl_CfYp.Cf_Code and Bl.Ks_Code=Zd_Yyks.Ks_Code and Bl_CfYp.Xx_Code=V_YpKc.Xx_Code   And Jz_Date+Jz_Time>='" & Format(DateTimePicker1.Value, "yyyy-MM-dd HH:mm:ss") & "' And Jz_Date+Jz_Time<='" & Format(DateTimePicker2.Value, "yyyy-MM-dd HH:mm:ss") & "'  and Cf_Qr='是'  " & _
                                          "  union all select Ks_Name,'Y'+Zd_Ml_Xm3.Xmlb_Code as Dl_Code,Xmlb_Name as Dl_Name,Ry_Name,Xm_Name as Yp_Name,Bl_CfXm.Cf_Money ,Bxlb_Name from Bl,Bl_Cf,Bl_CfXm,Zd_Ml_Xm3,Zd_Yyks,Zd_Ml_Xm1,Zd_Bxlb,Bl_Jz where Bl.Jz_Code=Bl_Jz.Jz_Code and Bl.Bxlb_Code=Zd_Bxlb.Bxlb_Code and Bl.Bl_Code=Bl_Cf.Bl_Code And Bl.Yy_Code='" & HisVar.HisVar.WsyCode & "' And Bl_Cf.Cf_Code=Bl_CfXm.Cf_Code and Bl.Ks_Code=Zd_Yyks.Ks_Code and Bl_CfXm.Xm_Code=Zd_Ml_Xm3.Xm_Code and Zd_Ml_Xm1.Xmlb_Code=Zd_ML_Xm3.Xmlb_Code  And Jz_Date+Jz_Time>='" & Format(DateTimePicker1.Value, "yyyy-MM-dd HH:mm:ss") & "' And Jz_Date+Jz_Time<='" & Format(DateTimePicker2.Value, "yyyy-MM-dd HH:mm:ss") & "'  and Cf_Qr='是'  " & _
                                        " union all select Ks_Name,'Z1' as Dl_Code,'费用合计' as Dl_Name,Ry_Name,'' as Yp_Name,Bl_M_Yj-Bl_M_Th as Cf_Money ,Bxlb_Name from Bl,Zd_Yyks,Zd_Bxlb,Bl_Jz where Bl.Jz_Code=Bl_Jz.Jz_Code and Bl.Bxlb_Code=Zd_Bxlb.Bxlb_Code And Bl.Yy_Code='" & HisVar.HisVar.WsyCode & "'  and Bl.Ks_Code=Zd_Yyks.Ks_Code   And Jz_Date+Jz_Time>='" & Format(DateTimePicker1.Value, "yyyy-MM-dd HH:mm:ss") & "' And Jz_Date+Jz_Time<='" & Format(DateTimePicker2.Value, "yyyy-MM-dd HH:mm:ss") & "'  " & _
                                        "  union all  select Ks_Name,'Z2' as Dl_Code,'预交押金' as Dl_Name,Ry_Name,'' as Yp_Name,Bl_M_Yj as Cf_Money ,Bxlb_Name from Bl,Zd_Yyks,Zd_Bxlb,Bl_Jz where Bl.Jz_Code=Bl_Jz.Jz_Code and Bl.Bxlb_Code=Zd_Bxlb.Bxlb_Code And Bl.Yy_Code='" & HisVar.HisVar.WsyCode & "'  and Bl.Ks_Code=Zd_Yyks.Ks_Code   And Jz_Date+Jz_Time>='" & Format(DateTimePicker1.Value, "yyyy-MM-dd HH:mm:ss") & "' And Jz_Date+Jz_Time<='" & Format(DateTimePicker2.Value, "yyyy-MM-dd HH:mm:ss") & "'   " & _
                                          "  union all select Ks_Name,'Z3' as Dl_Code,'退补押金' as Dl_Name,Ry_Name,'' as Yp_Name,Bl_M_Th as Cf_Money ,Bxlb_Name from Bl,Zd_Yyks,Zd_Bxlb,Bl_Jz where Bl.Jz_Code=Bl_Jz.Jz_Code and Bl.Bxlb_Code=Zd_Bxlb.Bxlb_Code And Bl.Yy_Code='" & HisVar.HisVar.WsyCode & "'  and Bl.Ks_Code=Zd_Yyks.Ks_Code   And Jz_Date+Jz_Time>='" & Format(DateTimePicker1.Value, "yyyy-MM-dd HH:mm:ss") & "' And Jz_Date+Jz_Time<='" & Format(DateTimePicker2.Value, "yyyy-MM-dd HH:mm:ss") & "'  Order by Dl_Code", "患者费用统计表", True)


            Dim StiRpt As New StiReport
            StiRpt.Load(".\Rpt\患者费用统计表.mrt")
            StiRpt.ReportName = "患者费用统计表"
            StiRpt.RegData(My_Data.Tables("患者费用统计表"))

            TryCast(StiRpt.Pages(0).GetComponents("CrossTab1_Column1"), StiCrossColumn).ShowTotal = False

            StiRpt.Compile()
            StiRpt("查询时间") = "查询时间:" & Format(DateTimePicker1.Value, "yyyy年MM月dd日") & "至" & Format(DateTimePicker2.Value, "yyyy年MM月dd日")
            StiRpt("打印时间") = "打印时间:" & Format(Now, "yyyy-MM-dd HH:mm:ss")
            StiRpt("标题") = "出院患者费用统计"


            'StiRpt.Design()
            StiRpt.Show()


        End If

        If C1Combo2.Text = "按日结单据查询" Then
            If My_Data.Tables("门诊住院日结查询").Rows.Count = 0 Then
                Beep()
                MsgBox("没有符合条件的记录", vbInformation + vbOKOnly + vbDefaultButton1, "提示:")
            Else

                Dim vform As New Cw_Cx_MzZy_Rj2(Me, True, TDBCombo1.Text, My_Data)
                vform.Tag = "Cw_Cx_MzZy_Rj1"
                BaseFunc.BaseFunc.addTabControl(vform, vform.Text)
            End If
        ElseIf C1Combo2.Text = "按收费明细查询" Then
            If My_Data.Tables("日结明细").Rows.Count = 0 Then
                Beep()
                MsgBox("没有符合条件的记录", vbInformation + vbOKOnly + vbDefaultButton1, "提示:")
            Else



                Dim vform As New Cw_Cx_MzZy_Rj3(Me, False, Nothing, My_Data)
                vform.Tag = "Cw_Cx_MzZy_Rj1"
                BaseFunc.BaseFunc.addTabControl(vform, vform.Text)

            End If
            'Else

        End If


    End Sub

    '清空按钮时间
    Private Sub Button2_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Button2.Click
        DateTimePicker1.Value = Format(Now, "yyyy-MM-dd 00:00:00")
        DateTimePicker2.Value = Format(Now, "yyyy-MM-dd 23:59:59")

    End Sub

#End Region

#Region "自定义方法"

    Private Sub Data_Cls()
        DateTimePicker1.Value = Format(Now, "yyyy-MM-dd 00:00:00")
        DateTimePicker2.Value = Format(Now, "yyyy-MM-dd 23:59:59")
    End Sub
#End Region


    Private Sub Cw_Cx_MzZy_Rj1_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        BaseFunc.BaseFunc.Init_Datetimepicker(DateTimePicker1)
        BaseFunc.BaseFunc.Init_Datetimepicker(DateTimePicker2)
        DateTimePicker1.Value = Format(Now, "yyyy-MM-dd 00:00:00")
        DateTimePicker2.Value = Format(Now, "yyyy-MM-dd 23:59:59")
        'RadioButton1.Checked = True
        Dim My_Combo1 As New BaseClass.C_Combo1(Me.TDBCombo1)
        My_Combo1.Init_TDBCombo()
        With TDBCombo1
            .AddItem("门诊")
            .AddItem("住院")
            .SelectedIndex = 0
            .Width = 142
            .DropDownWidth = 142
        End With
        Dim My_Combo2 As New BaseClass.C_Combo1(Me.C1Combo2)
        My_Combo2.Init_TDBCombo()
        With C1Combo2
            .AddItem("按日结单据查询")
            .AddItem("按收费明细查询")
            .AddItem("出院患者费用统计")
            .SelectedIndex = 0
            .Width = 142
            .DropDownWidth = 142
        End With
        '
    End Sub

    Private Sub C1Combo2_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles C1Combo2.TextChanged
        If C1Combo2.Text = "出院患者费用统计" Then
            TDBCombo1.Text = "住院"
            TDBCombo1.Enabled = False
        Else
            TDBCombo1.Text = "门诊"
            TDBCombo1.Enabled = True
        End If

    End Sub
End Class
