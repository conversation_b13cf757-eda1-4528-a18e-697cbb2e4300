﻿'------------------------------------------------------------------------------
' <auto-generated>
'     此代码由工具生成。
'     运行时版本:4.0.30319.42000
'
'     对此文件的更改可能会导致不正确的行为，并且如果
'     重新生成代码，这些更改将会丢失。
' </auto-generated>
'------------------------------------------------------------------------------

Option Strict Off
Option Explicit On

Imports System
Imports System.ComponentModel
Imports System.Data
Imports System.Diagnostics
Imports System.Web.Services
Imports System.Web.Services.Protocols
Imports System.Xml.Serialization

'
'此源代码是由 Microsoft.VSDesigner 4.0.30319.42000 版自动生成。
'
Namespace MztcXyDb
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0"),  _
     System.Diagnostics.DebuggerStepThroughAttribute(),  _
     System.ComponentModel.DesignerCategoryAttribute("code"),  _
     System.Web.Services.WebServiceBindingAttribute(Name:="OracleServiceSoap", [Namespace]:="http://tempuri.org/")>  _
    Partial Public Class OracleService
        Inherits System.Web.Services.Protocols.SoapHttpClientProtocol
        
        Private HelloWorldOperationCompleted As System.Threading.SendOrPostCallback
        
        Private GetNhZsOperationCompleted As System.Threading.SendOrPostCallback
        
        Private Yp_DownloadOperationCompleted As System.Threading.SendOrPostCallback
        
        Private Xm_DownloadOperationCompleted As System.Threading.SendOrPostCallback
        
        Private Mztc_XyDbOperationCompleted As System.Threading.SendOrPostCallback
        
        Private useDefaultCredentialsSetExplicitly As Boolean
        
        '''<remarks/>
        Public Sub New()
            MyBase.New
            Me.Url = Global.HisVar.My.MySettings.Default.HisVar_MztcXyDb_OracleService
            If (Me.IsLocalFileSystemWebService(Me.Url) = true) Then
                Me.UseDefaultCredentials = true
                Me.useDefaultCredentialsSetExplicitly = false
            Else
                Me.useDefaultCredentialsSetExplicitly = true
            End If
        End Sub
        
        Public Shadows Property Url() As String
            Get
                Return MyBase.Url
            End Get
            Set
                If (((Me.IsLocalFileSystemWebService(MyBase.Url) = true)  _
                            AndAlso (Me.useDefaultCredentialsSetExplicitly = false))  _
                            AndAlso (Me.IsLocalFileSystemWebService(value) = false)) Then
                    MyBase.UseDefaultCredentials = false
                End If
                MyBase.Url = value
            End Set
        End Property
        
        Public Shadows Property UseDefaultCredentials() As Boolean
            Get
                Return MyBase.UseDefaultCredentials
            End Get
            Set
                MyBase.UseDefaultCredentials = value
                Me.useDefaultCredentialsSetExplicitly = true
            End Set
        End Property
        
        '''<remarks/>
        Public Event HelloWorldCompleted As HelloWorldCompletedEventHandler
        
        '''<remarks/>
        Public Event GetNhZsCompleted As GetNhZsCompletedEventHandler
        
        '''<remarks/>
        Public Event Yp_DownloadCompleted As Yp_DownloadCompletedEventHandler
        
        '''<remarks/>
        Public Event Xm_DownloadCompleted As Xm_DownloadCompletedEventHandler
        
        '''<remarks/>
        Public Event Mztc_XyDbCompleted As Mztc_XyDbCompletedEventHandler
        
        '''<remarks/>
        <System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/HelloWorld", RequestNamespace:="http://tempuri.org/", ResponseNamespace:="http://tempuri.org/", Use:=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle:=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)>  _
        Public Function HelloWorld() As String
            Dim results() As Object = Me.Invoke("HelloWorld", New Object(-1) {})
            Return CType(results(0),String)
        End Function
        
        '''<remarks/>
        Public Overloads Sub HelloWorldAsync()
            Me.HelloWorldAsync(Nothing)
        End Sub
        
        '''<remarks/>
        Public Overloads Sub HelloWorldAsync(ByVal userState As Object)
            If (Me.HelloWorldOperationCompleted Is Nothing) Then
                Me.HelloWorldOperationCompleted = AddressOf Me.OnHelloWorldOperationCompleted
            End If
            Me.InvokeAsync("HelloWorld", New Object(-1) {}, Me.HelloWorldOperationCompleted, userState)
        End Sub
        
        Private Sub OnHelloWorldOperationCompleted(ByVal arg As Object)
            If (Not (Me.HelloWorldCompletedEvent) Is Nothing) Then
                Dim invokeArgs As System.Web.Services.Protocols.InvokeCompletedEventArgs = CType(arg,System.Web.Services.Protocols.InvokeCompletedEventArgs)
                RaiseEvent HelloWorldCompleted(Me, New HelloWorldCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState))
            End If
        End Sub
        
        '''<remarks/>
        <System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/GetNhZs", RequestNamespace:="http://tempuri.org/", ResponseNamespace:="http://tempuri.org/", Use:=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle:=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)>  _
        Public Function GetNhZs(ByVal NhYyCode As String) As System.Data.DataTable
            Dim results() As Object = Me.Invoke("GetNhZs", New Object() {NhYyCode})
            Return CType(results(0),System.Data.DataTable)
        End Function
        
        '''<remarks/>
        Public Overloads Sub GetNhZsAsync(ByVal NhYyCode As String)
            Me.GetNhZsAsync(NhYyCode, Nothing)
        End Sub
        
        '''<remarks/>
        Public Overloads Sub GetNhZsAsync(ByVal NhYyCode As String, ByVal userState As Object)
            If (Me.GetNhZsOperationCompleted Is Nothing) Then
                Me.GetNhZsOperationCompleted = AddressOf Me.OnGetNhZsOperationCompleted
            End If
            Me.InvokeAsync("GetNhZs", New Object() {NhYyCode}, Me.GetNhZsOperationCompleted, userState)
        End Sub
        
        Private Sub OnGetNhZsOperationCompleted(ByVal arg As Object)
            If (Not (Me.GetNhZsCompletedEvent) Is Nothing) Then
                Dim invokeArgs As System.Web.Services.Protocols.InvokeCompletedEventArgs = CType(arg,System.Web.Services.Protocols.InvokeCompletedEventArgs)
                RaiseEvent GetNhZsCompleted(Me, New GetNhZsCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState))
            End If
        End Sub
        
        '''<remarks/>
        <System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/Yp_Download", RequestNamespace:="http://tempuri.org/", ResponseNamespace:="http://tempuri.org/", Use:=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle:=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)>  _
        Public Function Yp_Download(ByVal V_GxTable As System.Data.DataTable, ByVal V_JxTable As System.Data.DataTable, ByVal V_YpDlTable As System.Data.DataTable, ByVal V_YpNameTable As System.Data.DataTable, ByVal V_YpMxTable As System.Data.DataTable) As String
            Dim results() As Object = Me.Invoke("Yp_Download", New Object() {V_GxTable, V_JxTable, V_YpDlTable, V_YpNameTable, V_YpMxTable})
            Return CType(results(0),String)
        End Function
        
        '''<remarks/>
        Public Overloads Sub Yp_DownloadAsync(ByVal V_GxTable As System.Data.DataTable, ByVal V_JxTable As System.Data.DataTable, ByVal V_YpDlTable As System.Data.DataTable, ByVal V_YpNameTable As System.Data.DataTable, ByVal V_YpMxTable As System.Data.DataTable)
            Me.Yp_DownloadAsync(V_GxTable, V_JxTable, V_YpDlTable, V_YpNameTable, V_YpMxTable, Nothing)
        End Sub
        
        '''<remarks/>
        Public Overloads Sub Yp_DownloadAsync(ByVal V_GxTable As System.Data.DataTable, ByVal V_JxTable As System.Data.DataTable, ByVal V_YpDlTable As System.Data.DataTable, ByVal V_YpNameTable As System.Data.DataTable, ByVal V_YpMxTable As System.Data.DataTable, ByVal userState As Object)
            If (Me.Yp_DownloadOperationCompleted Is Nothing) Then
                Me.Yp_DownloadOperationCompleted = AddressOf Me.OnYp_DownloadOperationCompleted
            End If
            Me.InvokeAsync("Yp_Download", New Object() {V_GxTable, V_JxTable, V_YpDlTable, V_YpNameTable, V_YpMxTable}, Me.Yp_DownloadOperationCompleted, userState)
        End Sub
        
        Private Sub OnYp_DownloadOperationCompleted(ByVal arg As Object)
            If (Not (Me.Yp_DownloadCompletedEvent) Is Nothing) Then
                Dim invokeArgs As System.Web.Services.Protocols.InvokeCompletedEventArgs = CType(arg,System.Web.Services.Protocols.InvokeCompletedEventArgs)
                RaiseEvent Yp_DownloadCompleted(Me, New Yp_DownloadCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState))
            End If
        End Sub
        
        '''<remarks/>
        <System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/Xm_Download", RequestNamespace:="http://tempuri.org/", ResponseNamespace:="http://tempuri.org/", Use:=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle:=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)>  _
        Public Function Xm_Download(ByVal V_XmlbTable As System.Data.DataTable, ByVal V_XmNameTable As System.Data.DataTable, ByVal V_NhYyCode As String) As Object
            Dim results() As Object = Me.Invoke("Xm_Download", New Object() {V_XmlbTable, V_XmNameTable, V_NhYyCode})
            Return CType(results(0),Object)
        End Function
        
        '''<remarks/>
        Public Overloads Sub Xm_DownloadAsync(ByVal V_XmlbTable As System.Data.DataTable, ByVal V_XmNameTable As System.Data.DataTable, ByVal V_NhYyCode As String)
            Me.Xm_DownloadAsync(V_XmlbTable, V_XmNameTable, V_NhYyCode, Nothing)
        End Sub
        
        '''<remarks/>
        Public Overloads Sub Xm_DownloadAsync(ByVal V_XmlbTable As System.Data.DataTable, ByVal V_XmNameTable As System.Data.DataTable, ByVal V_NhYyCode As String, ByVal userState As Object)
            If (Me.Xm_DownloadOperationCompleted Is Nothing) Then
                Me.Xm_DownloadOperationCompleted = AddressOf Me.OnXm_DownloadOperationCompleted
            End If
            Me.InvokeAsync("Xm_Download", New Object() {V_XmlbTable, V_XmNameTable, V_NhYyCode}, Me.Xm_DownloadOperationCompleted, userState)
        End Sub
        
        Private Sub OnXm_DownloadOperationCompleted(ByVal arg As Object)
            If (Not (Me.Xm_DownloadCompletedEvent) Is Nothing) Then
                Dim invokeArgs As System.Web.Services.Protocols.InvokeCompletedEventArgs = CType(arg,System.Web.Services.Protocols.InvokeCompletedEventArgs)
                RaiseEvent Xm_DownloadCompleted(Me, New Xm_DownloadCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState))
            End If
        End Sub
        
        '''<remarks/>
        <System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/Mztc_XyDb", RequestNamespace:="http://tempuri.org/", ResponseNamespace:="http://tempuri.org/", Use:=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle:=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)>  _
        Public Function Mztc_XyDb(ByVal V_Hzyl_Zs_Code As String, ByVal V_Zs_Name As String, ByVal V_CkCode As String, ByVal V_CkMoney As Double, ByVal V_CkMemo As String, ByVal V_JsrCode As String, ByVal V_JsrName As String, ByVal Db_Tb As System.Data.DataTable) As String
            Dim results() As Object = Me.Invoke("Mztc_XyDb", New Object() {V_Hzyl_Zs_Code, V_Zs_Name, V_CkCode, V_CkMoney, V_CkMemo, V_JsrCode, V_JsrName, Db_Tb})
            Return CType(results(0),String)
        End Function
        
        '''<remarks/>
        Public Overloads Sub Mztc_XyDbAsync(ByVal V_Hzyl_Zs_Code As String, ByVal V_Zs_Name As String, ByVal V_CkCode As String, ByVal V_CkMoney As Double, ByVal V_CkMemo As String, ByVal V_JsrCode As String, ByVal V_JsrName As String, ByVal Db_Tb As System.Data.DataTable)
            Me.Mztc_XyDbAsync(V_Hzyl_Zs_Code, V_Zs_Name, V_CkCode, V_CkMoney, V_CkMemo, V_JsrCode, V_JsrName, Db_Tb, Nothing)
        End Sub
        
        '''<remarks/>
        Public Overloads Sub Mztc_XyDbAsync(ByVal V_Hzyl_Zs_Code As String, ByVal V_Zs_Name As String, ByVal V_CkCode As String, ByVal V_CkMoney As Double, ByVal V_CkMemo As String, ByVal V_JsrCode As String, ByVal V_JsrName As String, ByVal Db_Tb As System.Data.DataTable, ByVal userState As Object)
            If (Me.Mztc_XyDbOperationCompleted Is Nothing) Then
                Me.Mztc_XyDbOperationCompleted = AddressOf Me.OnMztc_XyDbOperationCompleted
            End If
            Me.InvokeAsync("Mztc_XyDb", New Object() {V_Hzyl_Zs_Code, V_Zs_Name, V_CkCode, V_CkMoney, V_CkMemo, V_JsrCode, V_JsrName, Db_Tb}, Me.Mztc_XyDbOperationCompleted, userState)
        End Sub
        
        Private Sub OnMztc_XyDbOperationCompleted(ByVal arg As Object)
            If (Not (Me.Mztc_XyDbCompletedEvent) Is Nothing) Then
                Dim invokeArgs As System.Web.Services.Protocols.InvokeCompletedEventArgs = CType(arg,System.Web.Services.Protocols.InvokeCompletedEventArgs)
                RaiseEvent Mztc_XyDbCompleted(Me, New Mztc_XyDbCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState))
            End If
        End Sub
        
        '''<remarks/>
        Public Shadows Sub CancelAsync(ByVal userState As Object)
            MyBase.CancelAsync(userState)
        End Sub
        
        Private Function IsLocalFileSystemWebService(ByVal url As String) As Boolean
            If ((url Is Nothing)  _
                        OrElse (url Is String.Empty)) Then
                Return false
            End If
            Dim wsUri As System.Uri = New System.Uri(url)
            If ((wsUri.Port >= 1024)  _
                        AndAlso (String.Compare(wsUri.Host, "localHost", System.StringComparison.OrdinalIgnoreCase) = 0)) Then
                Return true
            End If
            Return false
        End Function
    End Class
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0")>  _
    Public Delegate Sub HelloWorldCompletedEventHandler(ByVal sender As Object, ByVal e As HelloWorldCompletedEventArgs)
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0"),  _
     System.Diagnostics.DebuggerStepThroughAttribute(),  _
     System.ComponentModel.DesignerCategoryAttribute("code")>  _
    Partial Public Class HelloWorldCompletedEventArgs
        Inherits System.ComponentModel.AsyncCompletedEventArgs
        
        Private results() As Object
        
        Friend Sub New(ByVal results() As Object, ByVal exception As System.Exception, ByVal cancelled As Boolean, ByVal userState As Object)
            MyBase.New(exception, cancelled, userState)
            Me.results = results
        End Sub
        
        '''<remarks/>
        Public ReadOnly Property Result() As String
            Get
                Me.RaiseExceptionIfNecessary
                Return CType(Me.results(0),String)
            End Get
        End Property
    End Class
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0")>  _
    Public Delegate Sub GetNhZsCompletedEventHandler(ByVal sender As Object, ByVal e As GetNhZsCompletedEventArgs)
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0"),  _
     System.Diagnostics.DebuggerStepThroughAttribute(),  _
     System.ComponentModel.DesignerCategoryAttribute("code")>  _
    Partial Public Class GetNhZsCompletedEventArgs
        Inherits System.ComponentModel.AsyncCompletedEventArgs
        
        Private results() As Object
        
        Friend Sub New(ByVal results() As Object, ByVal exception As System.Exception, ByVal cancelled As Boolean, ByVal userState As Object)
            MyBase.New(exception, cancelled, userState)
            Me.results = results
        End Sub
        
        '''<remarks/>
        Public ReadOnly Property Result() As System.Data.DataTable
            Get
                Me.RaiseExceptionIfNecessary
                Return CType(Me.results(0),System.Data.DataTable)
            End Get
        End Property
    End Class
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0")>  _
    Public Delegate Sub Yp_DownloadCompletedEventHandler(ByVal sender As Object, ByVal e As Yp_DownloadCompletedEventArgs)
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0"),  _
     System.Diagnostics.DebuggerStepThroughAttribute(),  _
     System.ComponentModel.DesignerCategoryAttribute("code")>  _
    Partial Public Class Yp_DownloadCompletedEventArgs
        Inherits System.ComponentModel.AsyncCompletedEventArgs
        
        Private results() As Object
        
        Friend Sub New(ByVal results() As Object, ByVal exception As System.Exception, ByVal cancelled As Boolean, ByVal userState As Object)
            MyBase.New(exception, cancelled, userState)
            Me.results = results
        End Sub
        
        '''<remarks/>
        Public ReadOnly Property Result() As String
            Get
                Me.RaiseExceptionIfNecessary
                Return CType(Me.results(0),String)
            End Get
        End Property
    End Class
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0")>  _
    Public Delegate Sub Xm_DownloadCompletedEventHandler(ByVal sender As Object, ByVal e As Xm_DownloadCompletedEventArgs)
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0"),  _
     System.Diagnostics.DebuggerStepThroughAttribute(),  _
     System.ComponentModel.DesignerCategoryAttribute("code")>  _
    Partial Public Class Xm_DownloadCompletedEventArgs
        Inherits System.ComponentModel.AsyncCompletedEventArgs
        
        Private results() As Object
        
        Friend Sub New(ByVal results() As Object, ByVal exception As System.Exception, ByVal cancelled As Boolean, ByVal userState As Object)
            MyBase.New(exception, cancelled, userState)
            Me.results = results
        End Sub
        
        '''<remarks/>
        Public ReadOnly Property Result() As Object
            Get
                Me.RaiseExceptionIfNecessary
                Return CType(Me.results(0),Object)
            End Get
        End Property
    End Class
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0")>  _
    Public Delegate Sub Mztc_XyDbCompletedEventHandler(ByVal sender As Object, ByVal e As Mztc_XyDbCompletedEventArgs)
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0"),  _
     System.Diagnostics.DebuggerStepThroughAttribute(),  _
     System.ComponentModel.DesignerCategoryAttribute("code")>  _
    Partial Public Class Mztc_XyDbCompletedEventArgs
        Inherits System.ComponentModel.AsyncCompletedEventArgs
        
        Private results() As Object
        
        Friend Sub New(ByVal results() As Object, ByVal exception As System.Exception, ByVal cancelled As Boolean, ByVal userState As Object)
            MyBase.New(exception, cancelled, userState)
            Me.results = results
        End Sub
        
        '''<remarks/>
        Public ReadOnly Property Result() As String
            Get
                Me.RaiseExceptionIfNecessary
                Return CType(Me.results(0),String)
            End Get
        End Property
    End Class
End Namespace
