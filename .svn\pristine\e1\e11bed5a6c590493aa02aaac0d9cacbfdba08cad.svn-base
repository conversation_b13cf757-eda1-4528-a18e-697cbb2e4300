﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>
    </ProductVersion>
    <SchemaVersion>
    </SchemaVersion>
    <ProjectGuid>{DB1F7C8F-F366-440A-BE79-905B41075418}</ProjectGuid>
    <OutputType>Library</OutputType>
    <RootNamespace>ZtHis.Materials</RootNamespace>
    <AssemblyName>ZtHis.Materials</AssemblyName>
    <FileAlignment>512</FileAlignment>
    <MyType>Windows</MyType>
    <TargetFrameworkVersion>v4.0</TargetFrameworkVersion>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <DefineDebug>true</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <OutputPath>bin\Debug\</OutputPath>
    <DocumentationFile>ZtHis.Materials.xml</DocumentationFile>
    <NoWarn>42016,41999,42017,42018,42019,42032,42036,42020,42021,42022</NoWarn>
    <PlatformTarget>x86</PlatformTarget>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <DefineDebug>false</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DocumentationFile>ZtHis.Materials.xml</DocumentationFile>
    <NoWarn>42016,41999,42017,42018,42019,42032,42036,42020,42021,42022</NoWarn>
  </PropertyGroup>
  <PropertyGroup>
    <OptionExplicit>On</OptionExplicit>
  </PropertyGroup>
  <PropertyGroup>
    <OptionCompare>Binary</OptionCompare>
  </PropertyGroup>
  <PropertyGroup>
    <OptionStrict>Off</OptionStrict>
  </PropertyGroup>
  <PropertyGroup>
    <OptionInfer>On</OptionInfer>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="C1.C1Excel.4, Version=4.0.20163.212, Culture=neutral, PublicKeyToken=79882d576c6336da, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="C1.C1Report.4, Version=4.0.20192.375, Culture=neutral, PublicKeyToken=594a0605db190bb9, processorArchitecture=MSIL" />
    <Reference Include="C1.Win.C1Chart.4, Version=4.0.20163.212, Culture=neutral, PublicKeyToken=a22e16972c085838, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="C1.Win.C1Command.4, Version=4.0.20163.212, Culture=neutral, PublicKeyToken=e808566f358766d8, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="C1.Win.C1Editor.4, Version=4.0.20163.212, Culture=neutral, PublicKeyToken=79882d576c6336da, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="C1.Win.C1Input.4, Version=4.0.20163.212, Culture=neutral, PublicKeyToken=7e7ff60f0c214f9a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="C1.Win.C1List.4, Version=4.0.20163.212, Culture=neutral, PublicKeyToken=6b24f8f981dbd7bc, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="C1.Win.C1Report.4, Version=4.0.20192.375, Culture=neutral, PublicKeyToken=41780e2fc605e636, processorArchitecture=MSIL" />
    <Reference Include="C1.Win.C1Ribbon.4, Version=4.0.20163.212, Culture=neutral, PublicKeyToken=79882d576c6336da, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="C1.Win.C1TrueDBGrid.4, Version=4.0.20163.212, Culture=neutral, PublicKeyToken=75ae3fb0e2b1e0da, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="ICSharpCode.SharpZipLib, Version=0.86.0.518, Culture=neutral, PublicKeyToken=1b03e6acf1164f73, processorArchitecture=MSIL">
      <HintPath>..\packages\SharpZipLib.0.86.0\lib\20\ICSharpCode.SharpZipLib.dll</HintPath>
    </Reference>
    <Reference Include="iniOperate">
      <HintPath>..\Dll\iniOperate.dll</HintPath>
    </Reference>
    <Reference Include="NPOI, Version=*******, Culture=neutral, PublicKeyToken=0df73ec7942b34e1, processorArchitecture=MSIL">
      <HintPath>..\packages\NPOI.2.3.0\lib\net40\NPOI.dll</HintPath>
    </Reference>
    <Reference Include="NPOI.OOXML, Version=*******, Culture=neutral, PublicKeyToken=0df73ec7942b34e1, processorArchitecture=MSIL">
      <HintPath>..\packages\NPOI.2.3.0\lib\net40\NPOI.OOXML.dll</HintPath>
    </Reference>
    <Reference Include="NPOI.OpenXml4Net, Version=*******, Culture=neutral, PublicKeyToken=0df73ec7942b34e1, processorArchitecture=MSIL">
      <HintPath>..\packages\NPOI.2.3.0\lib\net40\NPOI.OpenXml4Net.dll</HintPath>
    </Reference>
    <Reference Include="NPOI.OpenXmlFormats, Version=*******, Culture=neutral, PublicKeyToken=0df73ec7942b34e1, processorArchitecture=MSIL">
      <HintPath>..\packages\NPOI.2.3.0\lib\net40\NPOI.OpenXmlFormats.dll</HintPath>
    </Reference>
    <Reference Include="Stimulsoft.Base, Version=2011.2.1026.0, Culture=neutral, PublicKeyToken=ebe6666cba19647a, processorArchitecture=MSIL" />
    <Reference Include="Stimulsoft.Controls, Version=2011.2.1026.0, Culture=neutral, PublicKeyToken=ebe6666cba19647a, processorArchitecture=MSIL" />
    <Reference Include="Stimulsoft.Design, Version=2011.2.1026.0, Culture=neutral, PublicKeyToken=ebe6666cba19647a, processorArchitecture=MSIL" />
    <Reference Include="Stimulsoft.Editor, Version=2011.2.1026.0, Culture=neutral, PublicKeyToken=ebe6666cba19647a, processorArchitecture=MSIL" />
    <Reference Include="Stimulsoft.Report, Version=2011.2.1026.0, Culture=neutral, PublicKeyToken=ebe6666cba19647a, processorArchitecture=MSIL" />
    <Reference Include="Stimulsoft.Report.Check, Version=2011.2.1026.0, Culture=neutral, PublicKeyToken=ebe6666cba19647a, processorArchitecture=MSIL" />
    <Reference Include="Stimulsoft.Report.Design, Version=2011.2.1026.0, Culture=neutral, PublicKeyToken=ebe6666cba19647a, processorArchitecture=MSIL" />
    <Reference Include="Stimulsoft.Report.Win, Version=2011.2.1026.0, Culture=neutral, PublicKeyToken=ebe6666cba19647a, processorArchitecture=MSIL" />
    <Reference Include="System" />
    <Reference Include="System.Data" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Drawing.Design" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
    <Reference Include="WaitWindow, Version=1.0.4713.25685, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\Dll\WaitWindow.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Import Include="Microsoft.VisualBasic" />
    <Import Include="System" />
    <Import Include="System.Collections" />
    <Import Include="System.Collections.Generic" />
    <Import Include="System.Data" />
    <Import Include="System.Diagnostics" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="01.物资管理\06物资盘点\DcCondition.Designer.vb">
      <DependentUpon>DcCondition.vb</DependentUpon>
    </Compile>
    <Compile Include="01.物资管理\06物资盘点\DcCondition.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="01.物资管理\06物资盘点\PublicForm.Designer.vb">
      <DependentUpon>PublicForm.vb</DependentUpon>
    </Compile>
    <Compile Include="01.物资管理\06物资盘点\PublicForm.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="01.物资管理\07物资退库\Return_Search.designer.vb">
      <DependentUpon>Return_Search.vb</DependentUpon>
    </Compile>
    <Compile Include="01.物资管理\07物资退库\Return_Search.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="01.物资管理\07物资退库\Marterials_Return.designer.vb">
      <DependentUpon>Marterials_Return.vb</DependentUpon>
    </Compile>
    <Compile Include="01.物资管理\07物资退库\Marterials_Return.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="01.物资管理\06物资盘点\Check_Search.designer.vb">
      <DependentUpon>Check_Search.vb</DependentUpon>
    </Compile>
    <Compile Include="01.物资管理\06物资盘点\Check_Search.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="01.物资管理\06物资盘点\Marterials_Check.designer.vb">
      <DependentUpon>Marterials_Check.vb</DependentUpon>
    </Compile>
    <Compile Include="01.物资管理\06物资盘点\Marterials_Check.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="01.物资管理\06物资盘点\MaterialsStockSelect.Designer.vb">
      <DependentUpon>MaterialsStockSelect.vb</DependentUpon>
    </Compile>
    <Compile Include="01.物资管理\06物资盘点\MaterialsStockSelect.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="01.物资管理\05物资移库\Marterials_Move.designer.vb">
      <DependentUpon>Marterials_Move.vb</DependentUpon>
    </Compile>
    <Compile Include="01.物资管理\05物资移库\Marterials_Move.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="01.物资管理\05物资移库\Move_Search.designer.vb">
      <DependentUpon>Move_Search.vb</DependentUpon>
    </Compile>
    <Compile Include="01.物资管理\05物资移库\Move_Search.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="01.物资管理\04物资其他出库\Materials_Other_Out.designer.vb">
      <DependentUpon>Materials_Other_Out.vb</DependentUpon>
    </Compile>
    <Compile Include="01.物资管理\04物资其他出库\Materials_Other_Out.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="01.物资管理\04物资其他出库\Other_Search.designer.vb">
      <DependentUpon>Other_Search.vb</DependentUpon>
    </Compile>
    <Compile Include="01.物资管理\04物资其他出库\Other_Search.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="03.物资查询\03物资其他入库查询\MaterialsOtherInSearch.Designer.vb">
      <DependentUpon>MaterialsOtherInSearch.vb</DependentUpon>
    </Compile>
    <Compile Include="03.物资查询\03物资其他入库查询\MaterialsOtherInSearch.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="03.物资查询\03物资其他入库查询\MOISCondition.Designer.vb">
      <DependentUpon>MOISCondition.vb</DependentUpon>
    </Compile>
    <Compile Include="03.物资查询\03物资其他入库查询\MOISCondition.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="03.物资查询\05物资其它出库查询\MaterialsOtherOutSearch.Designer.vb">
      <DependentUpon>MaterialsOtherOutSearch.vb</DependentUpon>
    </Compile>
    <Compile Include="03.物资查询\05物资其它出库查询\MaterialsOtherOutSearch.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="03.物资查询\05物资其它出库查询\MOOSCondition.Designer.vb">
      <DependentUpon>MOOSCondition.vb</DependentUpon>
    </Compile>
    <Compile Include="03.物资查询\05物资其它出库查询\MOOSCondition.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="03.物资查询\01物资库存查询\MaterialsStockSearch.Designer.vb">
      <DependentUpon>MaterialsStockSearch.vb</DependentUpon>
    </Compile>
    <Compile Include="03.物资查询\01物资库存查询\MaterialsStockSearch.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="03.物资查询\01物资库存查询\MSSCondition.Designer.vb">
      <DependentUpon>MSSCondition.vb</DependentUpon>
    </Compile>
    <Compile Include="03.物资查询\01物资库存查询\MSSCondition.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="03.物资查询\07物资盘点查询\MaterialsCheckSearch.Designer.vb">
      <DependentUpon>MaterialsCheckSearch.vb</DependentUpon>
    </Compile>
    <Compile Include="03.物资查询\07物资盘点查询\MaterialsCheckSearch.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="03.物资查询\07物资盘点查询\MCSCondition.Designer.vb">
      <DependentUpon>MCSCondition.vb</DependentUpon>
    </Compile>
    <Compile Include="03.物资查询\07物资盘点查询\MCSCondition.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="03.物资查询\06物资移库查询\MaterialsMoveSearch.Designer.vb">
      <DependentUpon>MaterialsMoveSearch.vb</DependentUpon>
    </Compile>
    <Compile Include="03.物资查询\06物资移库查询\MaterialsMoveSearch.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="03.物资查询\06物资移库查询\MMSCondition.Designer.vb">
      <DependentUpon>MMSCondition.vb</DependentUpon>
    </Compile>
    <Compile Include="03.物资查询\06物资移库查询\MMSCondition.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="03.物资查询\08物资退库查询\MaterialsReturnSearch.Designer.vb">
      <DependentUpon>MaterialsReturnSearch.vb</DependentUpon>
    </Compile>
    <Compile Include="03.物资查询\08物资退库查询\MaterialsReturnSearch.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="03.物资查询\08物资退库查询\MRSCondition.Designer.vb">
      <DependentUpon>MRSCondition.vb</DependentUpon>
    </Compile>
    <Compile Include="03.物资查询\08物资退库查询\MRSCondition.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="03.物资查询\02物资采购查询\MaterialsBuyInSearch.Designer.vb">
      <DependentUpon>MaterialsBuyInSearch.vb</DependentUpon>
    </Compile>
    <Compile Include="03.物资查询\02物资采购查询\MaterialsBuyInSearch.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="03.物资查询\02物资采购查询\MBISCondition.Designer.vb">
      <DependentUpon>MBISCondition.vb</DependentUpon>
    </Compile>
    <Compile Include="03.物资查询\02物资采购查询\MBISCondition.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="03.物资查询\04物资领用查询\MaterialsUseOutSearch.Designer.vb">
      <DependentUpon>MaterialsUseOutSearch.vb</DependentUpon>
    </Compile>
    <Compile Include="03.物资查询\04物资领用查询\MaterialsUseOutSearch.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="03.物资查询\04物资领用查询\MUOSCondition.Designer.vb">
      <DependentUpon>MUOSCondition.vb</DependentUpon>
    </Compile>
    <Compile Include="03.物资查询\04物资领用查询\MUOSCondition.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="My Project\AssemblyInfo.vb" />
    <Compile Include="My Project\Application.Designer.vb">
      <AutoGen>True</AutoGen>
      <DependentUpon>Application.myapp</DependentUpon>
    </Compile>
    <Compile Include="My Project\Resources.Designer.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Include="My Project\Settings.Designer.vb">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
    <Compile Include="01.物资管理\01物资入库\Materials_Buy_In1.Designer.vb">
      <DependentUpon>Materials_Buy_In1.vb</DependentUpon>
    </Compile>
    <Compile Include="01.物资管理\01物资入库\Materials_Buy_In1.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="01.物资管理\01物资入库\Materials_Buy_In2.Designer.vb">
      <DependentUpon>Materials_Buy_In2.vb</DependentUpon>
    </Compile>
    <Compile Include="01.物资管理\01物资入库\Materials_Buy_In2.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="01.物资管理\01物资入库\Materials_Buy_Search.Designer.vb">
      <DependentUpon>Materials_Buy_Search.vb</DependentUpon>
    </Compile>
    <Compile Include="01.物资管理\01物资入库\Materials_Buy_Search.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="02.物资基础\03物资出入库类别\MaterialsInOutClassDict11.Designer.vb">
      <DependentUpon>MaterialsInOutClassDict11.vb</DependentUpon>
    </Compile>
    <Compile Include="02.物资基础\03物资出入库类别\MaterialsInOutClassDict11.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="02.物资基础\03物资出入库类别\MaterialsInOutClassDict12.Designer.vb">
      <DependentUpon>MaterialsInOutClassDict12.vb</DependentUpon>
    </Compile>
    <Compile Include="02.物资基础\03物资出入库类别\MaterialsInOutClassDict12.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="02.物资基础\01物资字典\MaterialsDict11.Designer.vb">
      <DependentUpon>MaterialsDict11.vb</DependentUpon>
    </Compile>
    <Compile Include="02.物资基础\01物资字典\MaterialsDict11.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="02.物资基础\01物资字典\MaterialsDict12.Designer.vb">
      <DependentUpon>MaterialsDict12.vb</DependentUpon>
    </Compile>
    <Compile Include="02.物资基础\01物资字典\MaterialsDict12.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="02.物资基础\01物资字典\MaterialsDict13.Designer.vb">
      <DependentUpon>MaterialsDict13.vb</DependentUpon>
    </Compile>
    <Compile Include="02.物资基础\01物资字典\MaterialsDict13.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="02.物资基础\01物资字典\MaterialsMove.Designer.vb">
      <DependentUpon>MaterialsMove.vb</DependentUpon>
    </Compile>
    <Compile Include="02.物资基础\01物资字典\MaterialsMove.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="02.物资基础\04物资库房\MaterialsWarehouseDict11.Designer.vb">
      <DependentUpon>MaterialsWarehouseDict11.vb</DependentUpon>
    </Compile>
    <Compile Include="02.物资基础\04物资库房\MaterialsWarehouseDict11.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="02.物资基础\04物资库房\MaterialsWarehouseDict12.Designer.vb">
      <DependentUpon>MaterialsWarehouseDict12.vb</DependentUpon>
    </Compile>
    <Compile Include="02.物资基础\04物资库房\MaterialsWarehouseDict12.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="01.物资管理\02物资其他入库\MaterialsOtherIn.Designer.vb">
      <DependentUpon>MaterialsOtherIn.vb</DependentUpon>
    </Compile>
    <Compile Include="01.物资管理\02物资其他入库\MaterialsOtherIn.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="01.物资管理\02物资其他入库\MaterialsOtherInMx.Designer.vb">
      <DependentUpon>MaterialsOtherInMx.vb</DependentUpon>
    </Compile>
    <Compile Include="01.物资管理\02物资其他入库\MaterialsOtherInMx.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="01.物资管理\02物资其他入库\MaterialsOtherInQuery.Designer.vb">
      <DependentUpon>MaterialsOtherInQuery.vb</DependentUpon>
    </Compile>
    <Compile Include="01.物资管理\02物资其他入库\MaterialsOtherInQuery.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="01.物资管理\03物资领用\MaterialsUseOut.Designer.vb">
      <DependentUpon>MaterialsUseOut.vb</DependentUpon>
    </Compile>
    <Compile Include="01.物资管理\03物资领用\MaterialsUseOut.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="01.物资管理\03物资领用\MaterialsUseOutQuery.Designer.vb">
      <DependentUpon>MaterialsUseOutQuery.vb</DependentUpon>
    </Compile>
    <Compile Include="01.物资管理\03物资领用\MaterialsUseOutQuery.vb">
      <SubType>Form</SubType>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="01.物资管理\06物资盘点\DcCondition.resx">
      <DependentUpon>DcCondition.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="01.物资管理\06物资盘点\PublicForm.resx">
      <DependentUpon>PublicForm.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="01.物资管理\07物资退库\Return_Search.resx">
      <DependentUpon>Return_Search.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="01.物资管理\07物资退库\Marterials_Return.resx">
      <DependentUpon>Marterials_Return.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="01.物资管理\06物资盘点\Check_Search.resx">
      <DependentUpon>Check_Search.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="01.物资管理\06物资盘点\Marterials_Check.resx">
      <DependentUpon>Marterials_Check.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="01.物资管理\06物资盘点\MaterialsStockSelect.resx">
      <DependentUpon>MaterialsStockSelect.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="01.物资管理\05物资移库\Marterials_Move.resx">
      <DependentUpon>Marterials_Move.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="01.物资管理\05物资移库\Move_Search.resx">
      <DependentUpon>Move_Search.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="01.物资管理\04物资其他出库\Materials_Other_Out.resx">
      <DependentUpon>Materials_Other_Out.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="01.物资管理\04物资其他出库\Other_Search.resx">
      <DependentUpon>Other_Search.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="03.物资查询\03物资其他入库查询\MaterialsOtherInSearch.resx">
      <DependentUpon>MaterialsOtherInSearch.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="03.物资查询\03物资其他入库查询\MOIScondition.resx">
      <DependentUpon>MOISCondition.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="03.物资查询\05物资其它出库查询\MaterialsOtherOutSearch.resx">
      <DependentUpon>MaterialsOtherOutSearch.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="03.物资查询\05物资其它出库查询\MOOSCondition.resx">
      <DependentUpon>MOOSCondition.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="03.物资查询\01物资库存查询\MaterialsStockSearch.resx">
      <DependentUpon>MaterialsStockSearch.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="03.物资查询\01物资库存查询\MSSCondition.resx">
      <DependentUpon>MSSCondition.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="03.物资查询\07物资盘点查询\MaterialsCheckSearch.resx">
      <DependentUpon>MaterialsCheckSearch.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="03.物资查询\07物资盘点查询\MCSCondition.resx">
      <DependentUpon>MCSCondition.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="03.物资查询\06物资移库查询\MaterialsMoveSearch.resx">
      <DependentUpon>MaterialsMoveSearch.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="03.物资查询\06物资移库查询\MMSCondition.resx">
      <DependentUpon>MMSCondition.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="03.物资查询\08物资退库查询\MaterialsReturnSearch.resx">
      <DependentUpon>MaterialsReturnSearch.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="03.物资查询\08物资退库查询\MRSCondition.resx">
      <DependentUpon>MRSCondition.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="03.物资查询\02物资采购查询\MaterialsBuyInSearch.resx">
      <DependentUpon>MaterialsBuyInSearch.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="03.物资查询\02物资采购查询\MBISCondition.resx">
      <DependentUpon>MBISCondition.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="03.物资查询\04物资领用查询\MaterialsUseOutSearch.resx">
      <DependentUpon>MaterialsUseOutSearch.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="03.物资查询\04物资领用查询\MUOSCondition.resx">
      <DependentUpon>MUOSCondition.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="My Project\licenses.licx" />
    <EmbeddedResource Include="My Project\Resources.resx">
      <CustomToolNamespace>My.Resources</CustomToolNamespace>
      <Generator>VbMyResourcesResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="01.物资管理\01物资入库\Materials_Buy_In1.resx">
      <DependentUpon>Materials_Buy_In1.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="01.物资管理\01物资入库\Materials_Buy_In2.resx">
      <DependentUpon>Materials_Buy_In2.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="01.物资管理\01物资入库\Materials_Buy_Search.resx">
      <DependentUpon>Materials_Buy_Search.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="02.物资基础\03物资出入库类别\MaterialsInOutClassDict11.resx">
      <DependentUpon>MaterialsInOutClassDict11.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="02.物资基础\03物资出入库类别\MaterialsInOutClassDict12.resx">
      <DependentUpon>MaterialsInOutClassDict12.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="02.物资基础\01物资字典\MaterialsDict11.resx">
      <DependentUpon>MaterialsDict11.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="02.物资基础\01物资字典\MaterialsDict12.resx">
      <DependentUpon>MaterialsDict12.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="02.物资基础\01物资字典\MaterialsDict13.resx">
      <DependentUpon>MaterialsDict13.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="02.物资基础\01物资字典\MaterialsMove.resx">
      <DependentUpon>MaterialsMove.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="02.物资基础\04物资库房\MaterialsWarehouseDict11.resx">
      <DependentUpon>MaterialsWarehouseDict11.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="02.物资基础\04物资库房\MaterialsWarehouseDict12.resx">
      <DependentUpon>MaterialsWarehouseDict12.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="01.物资管理\02物资其他入库\MaterialsOtherIn.resx">
      <DependentUpon>MaterialsOtherIn.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="01.物资管理\02物资其他入库\MaterialsOtherInMx.resx">
      <DependentUpon>MaterialsOtherInMx.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="01.物资管理\02物资其他入库\MaterialsOtherInQuery.resx">
      <DependentUpon>MaterialsOtherInQuery.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="01.物资管理\03物资领用\MaterialsUseOut.resx">
      <DependentUpon>MaterialsUseOut.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="01.物资管理\03物资领用\MaterialsUseOutQuery.resx">
      <DependentUpon>MaterialsUseOutQuery.vb</DependentUpon>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <None Include="My Project\Application.myapp">
      <Generator>MyApplicationCodeGenerator</Generator>
      <LastGenOutput>Application.Designer.vb</LastGenOutput>
    </None>
    <None Include="My Project\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <CustomToolNamespace>My</CustomToolNamespace>
      <LastGenOutput>Settings.Designer.vb</LastGenOutput>
    </None>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\BaseClass\BaseClass.vbproj">
      <Project>{48964D0C-25C0-413C-A94A-C2246ADE46A1}</Project>
      <Name>BaseClass</Name>
    </ProjectReference>
    <ProjectReference Include="..\BaseFunc\BaseFunc.vbproj">
      <Project>{C7865854-F754-41AC-9912-829068BE5C2A}</Project>
      <Name>BaseFunc</Name>
    </ProjectReference>
    <ProjectReference Include="..\BLLOld\BLLOld.csproj">
      <Project>{42c23254-ba1f-4222-895b-276fc06bf59c}</Project>
      <Name>BLLOld</Name>
    </ProjectReference>
    <ProjectReference Include="..\Common\Common.csproj">
      <Project>{92E350A0-3691-4B8D-A07E-EBB0F10E6997}</Project>
      <Name>Common</Name>
    </ProjectReference>
    <ProjectReference Include="..\CustomControl\CustomControl.csproj">
      <Project>{12BF4168-D60E-4A6C-85BF-926130EE6A6D}</Project>
      <Name>CustomControl</Name>
    </ProjectReference>
    <ProjectReference Include="..\HisControl\HisControl.vbproj">
      <Project>{EF778791-6E30-4A07-83B9-2D73FD08810A}</Project>
      <Name>HisControl</Name>
    </ProjectReference>
    <ProjectReference Include="..\HisVar\HisVar.vbproj">
      <Project>{4DAD5E14-6224-46B2-886D-6D22CE3886BC}</Project>
      <Name>HisVar</Name>
    </ProjectReference>
    <ProjectReference Include="..\ModelOld\ModelOld.csproj">
      <Project>{1FAF80FE-D42E-4FEB-853A-B9846C1862AE}</Project>
      <Name>ModelOld</Name>
    </ProjectReference>
    <ProjectReference Include="..\Utilities\Utilities.csproj">
      <Project>{ddef90d7-bcf0-4e30-9fce-3bb2d493565d}</Project>
      <Name>Utilities</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <None Include="packages.config" />
    <None Include="Resources\miBaComplete.Glyph.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\miBaComplete.Glyph16.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\miBarAdd.Glyph.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\miBarAdd.Glyph16.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\miBarCancel.Glyph.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\miBarCancel.Glyph16.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\miBarClose.Glyph.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\miBarDelete.Glyph.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\miBarDelete.Glyph16.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\miBarDeleteAll.Glyph.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\miBarDeleteAll.Glyph16.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\miBarEdit.Glyph.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\miBarEdit.Glyph16.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\miBarExit.Glyph.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\miBarExport.Glyph.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\miBarFd_Glyph.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\miBarFd_Glyph16.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\miBarModify.Glyph.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\miBarModify.Glyph16.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\miBarParamPrint.Glyph.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\miBarParamPrint.Glyph16.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\miBarRefresh.Glyph.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\miBarRefresh.Glyph16.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\miBarSave.Glyph.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\miBarSave.Glyph16.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\miBarSearch.Glyph.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\miBarSearch.Glyph16.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\miBarSubmit.Glyph.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\miBarSubmit.Glyph16.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\被冲销16.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\冲销16.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\导出.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\导入.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\录入16.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\启用16.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\停用16.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\完成16.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\layout_edit.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\layout_sidebar.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\WIZMENU.BMP" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.VisualBasic.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>