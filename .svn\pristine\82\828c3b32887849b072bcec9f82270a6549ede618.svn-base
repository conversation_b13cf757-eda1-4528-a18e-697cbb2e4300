﻿/**  版本信息模板在安装目录下，可自行修改。
* M_Bl_Jf.cs
*
* 功 能： N/A
* 类 名： M_Bl_Jf
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2016/7/1 16:09:00   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
namespace ModelOld
{
	/// <summary>
	/// M_Bl_Jf:实体类(属性说明自动提取数据库字段的描述信息)
	/// </summary>
	[Serializable]
	public partial class M_Bl_Jf
	{
		public M_Bl_Jf()
		{}
		#region Model
		private string _yy_code;
		private string _bl_code;
		private string _jf_code;
		private decimal? _jf_money=0M;
		private DateTime? _jf_date;
		private string _jsr_code;
		private string _jf_memo;
		private string _jz_code;
		private string _bl_jffs;
		private int? _jkkid;
		private string _bank_no;
        
        private string _Ry_Name;
        private string _Ks_Name;
        private string _Ys_Name;
        private string _Jsr_Name;
        private string _Ry_BlCode;
		/// <summary>
		/// 
		/// </summary>
		public string Yy_Code
		{
			set{ _yy_code=value;}
			get{return _yy_code;}
		}
		/// <summary>
		/// 病例编码
		/// </summary>
		public string Bl_Code
		{
			set{ _bl_code=value;}
			get{return _bl_code;}
		}
		/// <summary>
		/// 押金编码
		/// </summary>
		public string Jf_Code
		{
			set{ _jf_code=value;}
			get{return _jf_code;}
		}
		/// <summary>
		/// 押金金额
		/// </summary>
		public decimal? Jf_Money
		{
			set{ _jf_money=value;}
			get{return _jf_money;}
		}
		/// <summary>
		/// 交费日期
		/// </summary>
		public DateTime? Jf_Date
		{
			set{ _jf_date=value;}
			get{return _jf_date;}
		}
		/// <summary>
		/// 经手人
		/// </summary>
		public string Jsr_Code
		{
			set{ _jsr_code=value;}
			get{return _jsr_code;}
		}
		/// <summary>
		/// 备注
		/// </summary>
		public string Jf_Memo
		{
			set{ _jf_memo=value;}
			get{return _jf_memo;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string Jz_Code
		{
			set{ _jz_code=value;}
			get{return _jz_code;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string Bl_Jffs
		{
			set{ _bl_jffs=value;}
			get{return _bl_jffs;}
		}
		/// <summary>
		/// 
		/// </summary>
		public int? JkkId
		{
			set{ _jkkid=value;}
			get{return _jkkid;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string Bank_No
		{
			set{ _bank_no=value;}
			get{return _bank_no;}
		}
       
        public string Ry_Name{
            set { _Ry_Name = value; }
            get { return _Ry_Name; }
        }
        public string Ks_Name
        {
            set { _Ks_Name = value; }
            get { return _Ks_Name; }
        }
        public string Ys_Name
        {
            set { _Ys_Name = value; }
            get { return _Ys_Name; }
        }
        public string Jsr_Name
        {
            set { _Jsr_Name = value; }
            get { return _Jsr_Name; }
        }
        public string Ry_BlCode
        {
            set { _Ry_BlCode = value; }
            get { return _Ry_BlCode; }
        }
		#endregion Model

	}
}

