﻿/**  版本信息模板在安装目录下，可自行修改。
* D_Materials_Warehouse_Dict.cs
*
* 功 能： N/A
* 类 名： D_Materials_Warehouse_Dict
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2016-12-03 09:37:11   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;

namespace DAL
{
	/// <summary>
	/// 数据访问类:D_Materials_Warehouse_Dict
	/// </summary>
	public partial class D_Materials_Warehouse_Dict
	{
		public D_Materials_Warehouse_Dict()
		{}
		#region  BasicMethod

        public string MaxCode()
        {
            string max = (string)(HisVar.HisVar.Sqldal.F_MaxCode("select max(MaterialsWh_Code) from Materials_Warehouse_Dict", 2));
            return max;
        }


		/// <summary>
		/// 是否存在该记录
		/// </summary>
		public bool Exists(string MaterialsWh_Code)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) from Materials_Warehouse_Dict");
			strSql.Append(" where MaterialsWh_Code=@MaterialsWh_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@MaterialsWh_Code", SqlDbType.Char,2)			};
			parameters[0].Value = MaterialsWh_Code;

			return HisVar.HisVar.Sqldal.Exists(strSql.ToString(),parameters);
		}


		/// <summary>
		/// 增加一条数据
		/// </summary>
		public bool Add(ModelOld.M_Materials_Warehouse_Dict model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("insert into Materials_Warehouse_Dict(");
			strSql.Append("MaterialsWh_Code,MaterialsWh_Name,MaterialsWh_Py,MaterialsWh_Wb,MaterialsWh_Memo,Serial_No,IsUse)");
			strSql.Append(" values (");
			strSql.Append("@MaterialsWh_Code,@MaterialsWh_Name,@MaterialsWh_Py,@MaterialsWh_Wb,@MaterialsWh_Memo,@Serial_No,@IsUse)");
			SqlParameter[] parameters = {
					new SqlParameter("@MaterialsWh_Code", SqlDbType.Char,2),
					new SqlParameter("@MaterialsWh_Name", SqlDbType.VarChar,50),
					new SqlParameter("@MaterialsWh_Py", SqlDbType.VarChar,50),
					new SqlParameter("@MaterialsWh_Wb", SqlDbType.VarChar,50),
					new SqlParameter("@MaterialsWh_Memo", SqlDbType.VarChar,200),
					new SqlParameter("@Serial_No", SqlDbType.Int,4),
					new SqlParameter("@IsUse", SqlDbType.Bit,1)};
			parameters[0].Value = model.MaterialsWh_Code;
			parameters[1].Value = model.MaterialsWh_Name;
			parameters[2].Value = model.MaterialsWh_Py;
            parameters[3].Value = Common.Tools.IsValueNull(model.MaterialsWh_Wb);
			parameters[4].Value = model.MaterialsWh_Memo;
            parameters[5].Value = Common.Tools.IsValueNull(model.Serial_No);
			parameters[6].Value = model.IsUse;

			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 更新一条数据
		/// </summary>
		public bool Update(ModelOld.M_Materials_Warehouse_Dict model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("update Materials_Warehouse_Dict set ");
			strSql.Append("MaterialsWh_Name=@MaterialsWh_Name,");
			strSql.Append("MaterialsWh_Py=@MaterialsWh_Py,");
			strSql.Append("MaterialsWh_Wb=@MaterialsWh_Wb,");
			strSql.Append("MaterialsWh_Memo=@MaterialsWh_Memo,");
			strSql.Append("Serial_No=@Serial_No,");
			strSql.Append("IsUse=@IsUse");
			strSql.Append(" where MaterialsWh_Code=@MaterialsWh_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@MaterialsWh_Name", SqlDbType.VarChar,50),
					new SqlParameter("@MaterialsWh_Py", SqlDbType.VarChar,50),
					new SqlParameter("@MaterialsWh_Wb", SqlDbType.VarChar,50),
					new SqlParameter("@MaterialsWh_Memo", SqlDbType.VarChar,200),
					new SqlParameter("@Serial_No", SqlDbType.Int,4),
					new SqlParameter("@IsUse", SqlDbType.Bit,1),
					new SqlParameter("@MaterialsWh_Code", SqlDbType.Char,2)};
			parameters[0].Value = model.MaterialsWh_Name;
			parameters[1].Value = model.MaterialsWh_Py;
            parameters[2].Value = Common.Tools.IsValueNull(model.MaterialsWh_Wb);
			parameters[3].Value = model.MaterialsWh_Memo;
            parameters[4].Value = Common.Tools.IsValueNull(model.Serial_No);
			parameters[5].Value = model.IsUse;
			parameters[6].Value = model.MaterialsWh_Code;

			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool Delete(string MaterialsWh_Code)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from Materials_Warehouse_Dict ");
			strSql.Append(" where MaterialsWh_Code=@MaterialsWh_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@MaterialsWh_Code", SqlDbType.Char,2)			};
			parameters[0].Value = MaterialsWh_Code;

			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 批量删除数据
		/// </summary>
		public bool DeleteList(string MaterialsWh_Codelist )
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from Materials_Warehouse_Dict ");
			strSql.Append(" where MaterialsWh_Code in ("+MaterialsWh_Codelist + ")  ");
			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString());
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public ModelOld.M_Materials_Warehouse_Dict GetModel(string MaterialsWh_Code)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select  top 1 MaterialsWh_Code,MaterialsWh_Name,MaterialsWh_Py,MaterialsWh_Wb,MaterialsWh_Memo,Serial_No,IsUse from Materials_Warehouse_Dict ");
			strSql.Append(" where MaterialsWh_Code=@MaterialsWh_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@MaterialsWh_Code", SqlDbType.Char,2)			};
			parameters[0].Value = MaterialsWh_Code;

			ModelOld.M_Materials_Warehouse_Dict model=new ModelOld.M_Materials_Warehouse_Dict();
			DataSet ds=HisVar.HisVar.Sqldal.Query(strSql.ToString(),parameters);
			if(ds.Tables[0].Rows.Count>0)
			{
				return DataRowToModel(ds.Tables[0].Rows[0]);
			}
			else
			{
				return null;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public ModelOld.M_Materials_Warehouse_Dict DataRowToModel(DataRow row)
		{
			ModelOld.M_Materials_Warehouse_Dict model=new ModelOld.M_Materials_Warehouse_Dict();
			if (row != null)
			{
				if(row["MaterialsWh_Code"]!=null)
				{
					model.MaterialsWh_Code=row["MaterialsWh_Code"].ToString();
				}
				if(row["MaterialsWh_Name"]!=null)
				{
					model.MaterialsWh_Name=row["MaterialsWh_Name"].ToString();
				}
				if(row["MaterialsWh_Py"]!=null)
				{
					model.MaterialsWh_Py=row["MaterialsWh_Py"].ToString();
				}
				if(row["MaterialsWh_Wb"]!=null)
				{
					model.MaterialsWh_Wb=row["MaterialsWh_Wb"].ToString();
				}
				if(row["MaterialsWh_Memo"]!=null)
				{
					model.MaterialsWh_Memo=row["MaterialsWh_Memo"].ToString();
				}
				if(row["Serial_No"]!=null && row["Serial_No"].ToString()!="")
				{
					model.Serial_No=int.Parse(row["Serial_No"].ToString());
				}
				if(row["IsUse"]!=null && row["IsUse"].ToString()!="")
				{
					if((row["IsUse"].ToString()=="1")||(row["IsUse"].ToString().ToLower()=="true"))
					{
						model.IsUse=true;
					}
					else
					{
						model.IsUse=false;
					}
				}
			}
			return model;
		}

        public DataSet GetListForDr()
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select MaterialsWh_Name,MaterialsWh_Memo ");
            strSql.Append(" FROM Materials_Warehouse_Dict ");
            strSql.Append(" where 1=2");
  
            return HisVar.HisVar.Sqldal.Query(strSql.ToString());
        }

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetList(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select MaterialsWh_Code,MaterialsWh_Name,MaterialsWh_Py,MaterialsWh_Wb,MaterialsWh_Memo,Serial_No,IsUse ");
			strSql.Append(" FROM Materials_Warehouse_Dict ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			return HisVar.HisVar.Sqldal.Query(strSql.ToString());
		}

		/// <summary>
		/// 获得前几行数据
		/// </summary>
		public DataSet GetList(int Top,string strWhere,string filedOrder)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select ");
			if(Top>0)
			{
				strSql.Append(" top "+Top.ToString());
			}
			strSql.Append(" MaterialsWh_Code,MaterialsWh_Name,MaterialsWh_Py,MaterialsWh_Wb,MaterialsWh_Memo,Serial_No,IsUse ");
			strSql.Append(" FROM Materials_Warehouse_Dict ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			strSql.Append(" order by " + filedOrder);
			return HisVar.HisVar.Sqldal.Query(strSql.ToString());
		}

		/// <summary>
		/// 获取记录总数
		/// </summary>
		public int GetRecordCount(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) FROM Materials_Warehouse_Dict ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			object obj = HisVar.HisVar.Sqldal.GetSingle(strSql.ToString());
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("SELECT * FROM ( ");
			strSql.Append(" SELECT ROW_NUMBER() OVER (");
			if (!string.IsNullOrEmpty(orderby.Trim()))
			{
				strSql.Append("order by T." + orderby );
			}
			else
			{
				strSql.Append("order by T.MaterialsWh_Code desc");
			}
			strSql.Append(")AS Row, T.*  from Materials_Warehouse_Dict T ");
			if (!string.IsNullOrEmpty(strWhere.Trim()))
			{
				strSql.Append(" WHERE " + strWhere);
			}
			strSql.Append(" ) TT");
			strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
			return HisVar.HisVar.Sqldal.Query(strSql.ToString());
		}

		/*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "Materials_Warehouse_Dict";
			parameters[1].Value = "MaterialsWh_Code";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return HisVar.HisVar.Sqldal.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

		#endregion  BasicMethod
		#region  ExtensionMethod

		#endregion  ExtensionMethod
	}
}

