﻿Imports System.Windows.Forms
Imports System.Drawing
Public Class ShiXianCx
#Region "变量初始化"
    Private My_Table1 As New DataTable            '药品字典
    Private My_Table2 As New DataTable            '药品字典
    ' Private My_Cm As CurrencyManager             '同步指针
    Private My_Row As DataRow                    '当 前 行
    Private My_View As DataView

    Private Zd_Emr_SiKong As New BLLOld.B_Emr_SiKong
    Private bllEmrBl As New BLLOld.B_Emr_Bl
    Private bllBl As New BLLOld.B_Bl
    Private bllMblb As New BLLOld.B_Emr_Mblb
#End Region

    Private Sub ShiXianCx_Load(sender As Object, e As System.EventArgs) Handles Me.Load
        Call Form_Init()
        Call Data_Init()
    End Sub

#Region "窗体初始化"
    Private Sub Form_Init()

        With MyGrid2
            .Clear()
            .Init_Column("病历号", "Bl_Code", "150", "中", "", False)
            .Init_Column("身份证", "Ry_Sfzh", "0 ", "左", "", False)
            .Init_Column("姓名", "Ry_Name", "120", "中", "", False)
            .Init_Column("性别", "Ry_Sex", "60", "中", "", False)
            .Init_Column("电话", "Ry_Tele", "100", "中", "", False)
            .Init_Column("疾病", "Jb_Name", "180", "中", "", False)
            .Init_Column("科室", "Ks_Name", "100 ", "中", "", False)
            .Init_Column("主治医师", "Ys_Name", "100 ", "中", "", False)
            .Init_Column("病区", "Bq_Name", "80 ", "中", "", False)
            .Init_Column("病床号", "Bc_Code", "80 ", "中", "", False)
            .Init_Column("病床", "Bc_Name", "80 ", "中", "", False)
            .Init_Column("入院时间", "Ry_RyDate", "100 ", "中", "yyyy-MM-dd", False)
            .Init_Column("出院时间", "Ry_CyDate", "100 ", "中", "yyyy-MM-dd", False)
            .Init_Column("状态", "Emr_GuiDang", "0 ", "中", "Combobox", False)


            .Columns("Emr_GuiDang").ValueItems.Values.Add(New C1.Win.C1TrueDBGrid.ValueItem(True, "解档"))
            .Columns("Emr_GuiDang").ValueItems.Values.Add(New C1.Win.C1TrueDBGrid.ValueItem(False, "归档"))

        End With
        My_Table1.Columns.Add("Bl_Code")
        My_Table1.Columns.Add("Ry_Name")
        My_Table1.Columns.Add("timelimit")
        My_Table1.Columns.Add("prompt")

    End Sub

    Private Sub Data_Init()
        'My_Table2 = bllBl.GetList(" Ry_CyJsr IS NOT NULL  AND isnull(Emr_GuiDang,0)=0 AND DATEDIFF(HOUR,Ry_CyDate,GETDATE())>" &
        '                                   "( SELECT Para_Value FROM zd_yypara WHERE Para_Code=26) OR EXISTS(SELECT 1 FROM  " &
        '                                  "(SELECT Bl_Code,DATEDIFF(HOUR,MAX(Log_Date),GETDATE()) timeInterval" &
        '                                  " FROM dbo.Emr_GDlog WHERE GuiDang_State='归档'  GROUP BY Bl_Code) t" &
        '                                  " WHERE   bl.Emr_GuiDang=1 and bl.Bl_Code=t.Bl_Code AND t.timeInterval>" &
        '                                  "( SELECT Para_Value FROM zd_yypara WHERE Para_Code=26))").Tables(0)

        My_Table2 = bllBl.GetList(" Ry_CyJsr IS NOT NULL  AND isnull(Emr_GuiDang,0)=0 AND exists (select 1 from emr_bl where emr_bl.bl_code=bl.bl_Code) and DATEDIFF(HOUR,Ry_CyDate,GETDATE())>" & HisPara.PublicConfig.GuidangHours).Tables(0)
        With MyGrid2
            .DataTable = My_Table2
        End With

        '初始化MyGrid1
        With MyGrid1
            .Clear()
            .Init_Column("病历号", "Bl_Code", "100", "中", "", False)
            .Init_Column("病人姓名", "Ry_Name", "200 ", "中", "", False)
            .Init_Column("时限", "timelimit", "700 ", "左", "", False)
            .Init_Column("提示警告信息", "prompt", "300", "左", "", False)

            Dim sty As New C1.Win.C1TrueDBGrid.Style()
            sty.ForeColor = Color.Red
            .Splits(0).DisplayColumns("prompt").AddCellStyle(C1.Win.C1TrueDBGrid.CellStyleFlag.AllCells, sty)
            .CanGroup = True
            .GroupedColumns.Add(MyGrid1.Columns("Ry_Name"))
            .GroupedColumns.Add(MyGrid1.Columns("Bl_Code"))
        End With
        QueryTimeLimit()
        With MyGrid1
            Dim dtCopy As DataTable = My_Table1.Clone()
            My_View = My_Table1.DefaultView
            My_View.Sort = "Ry_Name,Bl_Code"
            dtCopy = My_View.ToTable
            .DataTable = dtCopy
        End With

    End Sub
#End Region
#Region "控件动作"

    Private Sub MyButton1_Click(sender As System.Object, e As System.EventArgs) Handles MyButton1.Click
        Call Data_Init()
    End Sub

#End Region

#Region "自定义函数"

    Private Sub QueryTimeLimit()
        Try
            My_Table1.Clear()
            Dim cyDt, ryDt, MbDt, dtRyList, dtCyList As DataTable
            cyDt = bllBl.GetListCy().Tables(0)
            ryDt = bllBl.GetListRy().Tables(0)
            MbDt = bllEmrBl.GetListSkMb("").Tables(0)
            dtRyList = bllBl.GetList("   Ry_CyJsr IS  NULL").Tables(0)
            dtCyList = bllBl.GetList("   Ry_CyJsr IS NOT NULL").Tables(0)
            Dim exceedingDays, exceedinghours As Integer

            For Each mbRow In MbDt.Rows
                If mbRow("Yl_Event") = "入院" Then
                    For Each rRow In dtRyList.Rows

                        Dim ryRow As DataRow
                        If Trim(mbRow("Mb_Code").ToString.Trim & "") <> "" Then
                            Dim row As DataRow = My_Table1.NewRow
                            If ryDt.Select("Mblb_Code = '" & mbRow("Mblb_Code").ToString & "' And Mb_Code= '" &
                                           mbRow("Mb_Code").ToString & "' and bl_code='" & rRow("bl_code") & "'").Length > 0 Then

                                ryRow = ryDt.Select("Mblb_Code = '" & mbRow("Mblb_Code").ToString & "' And Mb_Code= '" &
                                           mbRow("Mb_Code").ToString & "' and bl_code='" & rRow("bl_code") & "'")(0)
                                If ryRow("EmrTimeInterval") - mbRow("MaxInterval") > 0 Then
                                    exceedingDays = (ryRow("EmrTimeInterval") - mbRow("MaxInterval")) \ 24
                                    exceedinghours = (ryRow("EmrTimeInterval") - mbRow("MaxInterval")) Mod 24
                                    row("Bl_Code") = ryRow("Bl_Code")
                                    row("Ry_Name") = ryRow("Ry_Name")
                                    row("timelimit") = "超出" & exceedingDays & "天" & exceedinghours & "小时"
                                    row("prompt") = "警告：入院" & ryRow("blTimeInterval") \ 24 & "天" & ryRow("blTimeInterval") Mod 24 &
                                        "小时，" & mbRow("MaxInterval") & "小时内未填写" & mbRow("Mblb_Name") & "-" & mbRow("Mb_Name")
                                    My_Table1.Rows.Add(row)
                                End If
                            Else

                                ryRow = ryDt.Select(" bl_code='" & rRow("bl_code") & "'")(0)
                                If ryRow("blTimeInterval") - mbRow("MaxInterval") > 0 Then
                                    exceedingDays = (ryRow("blTimeInterval") - mbRow("MaxInterval")) \ 24
                                    exceedinghours = (ryRow("blTimeInterval") - mbRow("MaxInterval")) Mod 24
                                    row("Bl_Code") = ryRow("Bl_Code")
                                    row("Ry_Name") = ryRow("Ry_Name")
                                    row("timelimit") = "超出" & exceedingDays & "天" & exceedinghours & "小时"
                                    row("prompt") = "警告：入院" & ryRow("blTimeInterval") \ 24 & "天" & ryRow("blTimeInterval") Mod 24 &
                                        "小时，" & mbRow("MaxInterval") & "小时内未填写" & mbRow("Mblb_Name") & "-" & mbRow("Mb_Name")
                                    My_Table1.Rows.Add(row)
                                End If
                            End If
                        Else
                            For Each r In bllMblb.GetAllMb(mbRow("mblb_code").ToString, "入院").Tables(0).Rows
                                Dim row As DataRow = My_Table1.NewRow
                                If ryDt.Select("Mblb_Code = '" & mbRow("Mblb_Code").ToString & "' And Mb_Code= '" &
                                           r("Mb_Code").ToString & "' and bl_code='" & rRow("bl_code") & "'").Length > 0 Then

                                    ryRow = ryDt.Select("Mblb_Code = '" & mbRow("Mblb_Code").ToString & "' And Mb_Code= '" &
                                               r("Mb_Code").ToString & "' and bl_code='" & rRow("bl_code") & "'")(0)
                                    If Not (ryRow("EmrTimeInterval") Is DBNull.Value Or mbRow("MaxInterval") Is DBNull.Value) AndAlso ryRow("EmrTimeInterval") - mbRow("MaxInterval") > 0 Then
                                        exceedingDays = (ryRow("EmrTimeInterval") - mbRow("MaxInterval")) \ 24
                                        exceedinghours = (ryRow("EmrTimeInterval") - mbRow("MaxInterval")) Mod 24
                                        row("Bl_Code") = ryRow("Bl_Code")
                                        row("Ry_Name") = ryRow("Ry_Name")
                                        row("timelimit") = "超出" & exceedingDays & "天" & exceedinghours & "小时"
                                        row("prompt") = "警告：入院" & ryRow("blTimeInterval") \ 24 & "天" & ryRow("blTimeInterval") Mod 24 &
                                            "小时，" & mbRow("MaxInterval") & "小时内未填写" & mbRow("Mblb_Name") & "-" & r("Mb_Name")
                                        My_Table1.Rows.Add(row)
                                    End If
                                Else
                                    ryRow = ryDt.Select(" bl_code='" & rRow("bl_code") & "'")(0)
                                    If ryRow("blTimeInterval") - mbRow("MaxInterval") > 0 Then
                                        exceedingDays = (ryRow("blTimeInterval") - mbRow("MaxInterval")) \ 24
                                        exceedinghours = (ryRow("blTimeInterval") - mbRow("MaxInterval")) Mod 24
                                        row("Bl_Code") = ryRow("Bl_Code")
                                        row("Ry_Name") = ryRow("Ry_Name")
                                        row("timelimit") = "超出" & exceedingDays & "天" & exceedinghours & "小时"
                                        row("prompt") = "警告：入院" & ryRow("blTimeInterval") \ 24 & "天" & ryRow("blTimeInterval") Mod 24 &
                                            "小时，" & mbRow("MaxInterval") & "小时内未填写" & mbRow("Mblb_Name") & "-" & r("Mb_Name")
                                        My_Table1.Rows.Add(row)
                                    End If
                                End If
                            Next
                        End If
                    Next

                Else
                    For Each cRow In dtCyList.Rows
                        Dim ryRow As DataRow
                        If Trim(mbRow("Mb_Code") & "") <> "" Then
                            Dim row As DataRow = My_Table1.NewRow
                            If cyDt.Select("Mblb_Code = '" & mbRow("Mblb_Code").ToString & "' And Mb_Code= '" &
                                           mbRow("Mb_Code").ToString & "' and bl_code='" & cRow("bl_code") & "'").Length > 0 Then

                                ryRow = cyDt.Select("Mblb_Code = '" & mbRow("Mblb_Code").ToString & "' And Mb_Code= '" &
                                           mbRow("Mb_Code").ToString & "' and bl_code='" & cRow("bl_code") & "'")(0)
                                If ryRow("EmrTimeInterval") - mbRow("MaxInterval") > 0 Then
                                    exceedingDays = (ryRow("EmrTimeInterval") - mbRow("MaxInterval")) \ 24
                                    exceedinghours = (ryRow("EmrTimeInterval") - mbRow("MaxInterval")) Mod 24
                                    row("Bl_Code") = ryRow("Bl_Code")
                                    row("Ry_Name") = ryRow("Ry_Name")
                                    row("timelimit") = "超出" & exceedingDays & "天" & exceedinghours & "小时"
                                    row("prompt") = "警告：出院" & ryRow("blTimeInterval") \ 24 & "天" & ryRow("blTimeInterval") Mod 24 &
                                        "小时，" & mbRow("MaxInterval") & "小时内未填写" & mbRow("Mblb_Name") & "-" & mbRow("Mb_Name")
                                    My_Table1.Rows.Add(row)
                                End If
                            Else

                                ryRow = cyDt.Select(" bl_code='" & cRow("bl_code") & "'")(0)
                                If ryRow("blTimeInterval") - mbRow("MaxInterval") > 0 Then
                                    exceedingDays = (ryRow("blTimeInterval") - mbRow("MaxInterval")) \ 24
                                    exceedinghours = (ryRow("blTimeInterval") - mbRow("MaxInterval")) Mod 24
                                    row("Bl_Code") = ryRow("Bl_Code")
                                    row("Ry_Name") = ryRow("Ry_Name")
                                    row("timelimit") = "超出" & exceedingDays & "天" & exceedinghours & "小时"
                                    row("prompt") = "警告：出院" & ryRow("blTimeInterval") \ 24 & "天" & ryRow("blTimeInterval") Mod 24 &
                                        "小时，" & mbRow("MaxInterval") & "小时内未填写" & mbRow("Mblb_Name") & "-" & mbRow("Mb_Name")
                                    My_Table1.Rows.Add(row)
                                End If
                            End If
                        Else
                            For Each r In bllMblb.GetAllMb(mbRow("mblb_code").ToString, "出院").Tables(0).Rows
                                Dim row As DataRow = My_Table1.NewRow
                                If cyDt.Select("Mblb_Code = '" & mbRow("Mblb_Code").ToString & "' And Mb_Code= '" &
                                           r("Mb_Code").ToString & "' and bl_code='" & cRow("bl_code") & "'").Length > 0 Then

                                    ryRow = cyDt.Select("Mblb_Code = '" & mbRow("Mblb_Code").ToString & "' And Mb_Code= '" &
                                               r("Mb_Code").ToString & "' and bl_code='" & cRow("bl_code") & "'")(0)
                                    If ryRow("EmrTimeInterval") - mbRow("MaxInterval") > 0 Then
                                        exceedingDays = (ryRow("EmrTimeInterval") - mbRow("MaxInterval")) \ 24
                                        exceedinghours = (ryRow("EmrTimeInterval") - mbRow("MaxInterval")) Mod 24
                                        row("Bl_Code") = ryRow("Bl_Code")
                                        row("Ry_Name") = ryRow("Ry_Name")
                                        row("timelimit") = "超出" & exceedingDays & "天" & exceedinghours & "小时"
                                        row("prompt") = "警告：出院" & ryRow("blTimeInterval") \ 24 & "天" & ryRow("blTimeInterval") Mod 24 &
                                            "小时，" & mbRow("MaxInterval") & "小时内未填写" & mbRow("Mblb_Name") & "-" & r("Mb_Name")
                                        My_Table1.Rows.Add(row)
                                    End If
                                Else
                                    ryRow = cyDt.Select(" bl_code='" & cRow("bl_code") & "'")(0)
                                    If ryRow("blTimeInterval") - mbRow("MaxInterval") > 0 Then
                                        exceedingDays = (ryRow("blTimeInterval") - mbRow("MaxInterval")) \ 24
                                        exceedinghours = (ryRow("blTimeInterval") - mbRow("MaxInterval")) Mod 24
                                        row("Bl_Code") = ryRow("Bl_Code")
                                        row("Ry_Name") = ryRow("Ry_Name")
                                        row("timelimit") = "超出" & exceedingDays & "天" & exceedinghours & "小时"
                                        row("prompt") = "警告：出院" & ryRow("blTimeInterval") \ 24 & "天" & ryRow("blTimeInterval") Mod 24 &
                                            "小时，" & mbRow("MaxInterval") & "小时内未填写" & mbRow("Mblb_Name") & "-" & r("Mb_Name")
                                        My_Table1.Rows.Add(row)
                                    End If
                                End If
                            Next
                        End If
                    Next
                End If
            Next


        Catch ex As Exception
            MsgBox(ex.ToString, MsgBoxStyle.Exclamation, "提示")
        End Try


    End Sub

#End Region



End Class
