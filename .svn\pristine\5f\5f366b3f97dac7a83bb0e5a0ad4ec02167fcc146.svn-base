﻿/**  版本信息模板在安装目录下，可自行修改。
* M_Materials_Return2.cs
*
* 功 能： N/A
* 类 名： M_Materials_Return2
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2016-11-30 17:16:48   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
namespace ModelOld
{
	/// <summary>
	/// 物资退库从表
	/// </summary>
	[Serializable]
	public partial class M_Materials_Return2WriteOff
	{
        public M_Materials_Return2WriteOff()
		{}
		#region Model
		private string _m_return_code;
		private string _materials_code;
        private string _materials_name;
        private string _materialssup_code;
        private string _materialssup_name;
        private string _materialsstock_code;
        private string _materialswh_code;
        private string _materialswh_name;
        private string _MaterialsStore_Num;
        private DateTime? _return_date;
		private string _m_return_detail_code;
		private string _materialslot;
		private DateTime? _materialsexpirydate;
		private decimal? _m_return_num;
		private decimal? _m_return_writeoffno;
		private decimal? _m_return_realno;
		private decimal? _m_return_price;
		private decimal? _m_return_money;
		private decimal? _m_return_realmoney;
		private string _m_returndetail_memo;


            //.Init_Column("退库编码", "M_Return_Code", "0", "左", "", False) '0-
            //.Init_Column("明细编码", "M_Return_Detail_Code", "0", "左", "", False) '0
            //.Init_Column("物资名称", "Materials_Name", "150", "左", "", True) '4-
            //.Init_Column("物资编码", "Materials_Code", "0", "左", "", False) '0
            //.Init_Column("供应商编码", "MaterialsSup_Code", "0", "左", "", False) '0
            //.Init_Column("供应商名称", "MaterialsSup_Name", "100", "左", "", False) '0
            //.Init_Column("库存编码", "MaterialsStock_Code", "0", "左", "", False) '2-
            //.Init_Column("仓库编码", "MaterialsWh_Code", "0", "左", "", False) '4-
            //.Init_Column("仓库名称", "MaterialsWh_Name", "100", "左", "", False) '4-
            //.Init_Column("物资批号", "MaterialsLot", "100", "左", "", False) '3-
            //.Init_Column("物资有效期", "MaterialsExpiryDate", "110", "左", "yyyyMMdd", False) '1-
            //.Init_Column("退库数量", "M_Return_Num", "80", "右", "####0.####", True) '0
            //.Init_Column("冲销数量", "M_Return_WriteoffNo", "80", "右", "####0.####", False) '0
            //.Init_Column("实际数量", "M_Return_RealNo", "80", "右", "####0.####", False) '0
            //.Init_Column("单价", "M_Return_Price", "70", "右", "####0.####", False) '0
            //.Init_Column("退库金额", "M_Return_Money", "100", "右", "####0.####", False) '0-
            //.Init_Column("实际退库金额", "M_Return_RealMoney", "120", "右", "####0.####", False) '0-
            //.Init_Column("备注", "M_ReturnDetail_Memo", "100", "左", "", False) '0
            //.Init_Column("选择", "isSelected", "60", "中", "check", True) '0

        public string Materials_Name
        {
            set { _materials_name = value; }
            get { return _materials_name; }
        }

        public string MaterialsSup_Code
        {
            set { _materialssup_code = value; }
            get { return _materialssup_code; }
        }
        public string MaterialsSup_Name
        {
            set { _materialssup_name = value; }
            get { return _materialssup_name; }
        }
        public string MaterialsWh_Code
        {
            set { _materialswh_code = value; }
            get { return _materialswh_code; }
        }
        public string MaterialsWh_Name
        {
            set { _materialswh_name = value; }
            get { return _materialswh_name; }
        }

        public string MaterialsStore_Num
        {
            set { _MaterialsStore_Num = value; }
            get { return _MaterialsStore_Num; }
        }

        public DateTime? Return_Date
        {
            set { _return_date = value; }
            get { return _return_date; }
        }
		/// <summary>
		/// yyMMdd+5位流水号
		/// </summary>
		public string M_Return_Code
		{
			set{ _m_return_code=value;}
			get{return _m_return_code;}
		}
		/// <summary>
		/// 物资编码
		/// </summary>
		public string Materials_Code
		{
			set{ _materials_code=value;}
			get{return _materials_code;}
		}

		/// <summary>
		/// 物资编码+6位流水号
		/// </summary>
		public string MaterialsStock_Code
		{
			set{ _materialsstock_code=value;}
			get{return _materialsstock_code;}
		}
		/// <summary>
		/// 退库明细编码
		/// </summary>
		public string M_Return_Detail_Code
		{
			set{ _m_return_detail_code=value;}
			get{return _m_return_detail_code;}
		}
		/// <summary>
		/// 物资批号
		/// </summary>
		public string MaterialsLot
		{
			set{ _materialslot=value;}
			get{return _materialslot;}
		}
		/// <summary>
		/// 物资有效期
		/// </summary>
		public DateTime? MaterialsExpiryDate
		{
			set{ _materialsexpirydate=value;}
			get{return _materialsexpirydate;}
		}
		/// <summary>
		/// 退库数量
		/// </summary>
		public decimal? M_Return_Num
		{
			set{ _m_return_num=value;}
			get{return _m_return_num;}
		}
		/// <summary>
		/// 
		/// </summary>
		public decimal? M_Return_WriteoffNo
		{
			set{ _m_return_writeoffno=value;}
			get{return _m_return_writeoffno;}
		}
		/// <summary>
		/// 
		/// </summary>
		public decimal? M_Return_RealNo
		{
			set{ _m_return_realno=value;}
			get{return _m_return_realno;}
		}
		/// <summary>
		/// 退库单价
		/// </summary>
		public decimal? M_Return_Price
		{
			set{ _m_return_price=value;}
			get{return _m_return_price;}
		}
		/// <summary>
		/// 退库金额
		/// </summary>
		public decimal? M_Return_Money
		{
			set{ _m_return_money=value;}
			get{return _m_return_money;}
		}
		/// <summary>
		/// 采购金额
		/// </summary>
		public decimal? M_Return_RealMoney
		{
			set{ _m_return_realmoney=value;}
			get{return _m_return_realmoney;}
		}
		/// <summary>
		/// 退库备注
		/// </summary>
		public string M_ReturnDetail_Memo
		{
			set{ _m_returndetail_memo=value;}
			get{return _m_returndetail_memo;}
		}
		#endregion Model

	}
}

