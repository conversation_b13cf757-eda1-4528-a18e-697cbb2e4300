﻿Imports System.Windows.Forms
Imports BaseClass

Imports System.Drawing
Public Class Test_Jy1

    Dim My_Dataset As New DataSet
    Public My_Cm1 As CurrencyManager
    Public My_Cm2 As CurrencyManager             '同步指针
    Public My_Cm3 As CurrencyManager
    Public My_Row As DataRow
    Dim V_Insert As Boolean

    Dim BllLIS_Test1 As New BLLOld.B_LIS_Test1
    Private bllKs As New BLLOld.B_Zd_YyKs
    Private bllBc As New BLLOld.B_V_YyBc
    Private bllBxlb As New BLLOld.B_Zd_Bxlb
    Private bllSample As New BLLOld.B_LIS_Element2
    Private bllYs As New BLLOld.B_Zd_YyYs
    Private bllTestXm As New BLLOld.B_LIS_TestXm


    Private Sub Test_Jy1_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        Call Form_init()
        Call Data_Init()
        Call Data_Init2()
        Call Data_Clear()
        Call Data3("")
    End Sub

#Region "窗体初始化"
    Private Sub Form_init()
        With MyGrid1
            .Clear()
            .Init_Column("检验项目", "TestItem_Code", "0", "中", "", False)
            .Init_Column("检验项目", "Item_Name", "300", "中", "", False)
            .Init_Column("结果", "ItemValue", "200", "中", "#.00", True)
            .Init_Column("对比值", "High_low", "60 ", "中", "", False)
            .Init_Column("单位", "Item_Dw", "120 ", "中", "", False)
            .Init_Column("参考值", "CK", "100 ", "中", "", False)
        End With
        MyGrid1.Splits(0).DisplayColumns("High_low").FetchStyle = True
        With MyGrid2
            .Clear()
            .Init_Column("申请来源", "Test_Lb", "150", "中", "", False)
            .Init_Column("姓名", "Ry_Name", "120", "中", "", False)
            '.Init_Column("科室", "Ks_Name", "100 ", "中", "", False)
            .Init_Column("处方编码", "His_Code", "120", "中", "", False)
            .Init_Column("检验状态", "TestState", "100 ", "中", "", False)
            .Init_Column("状态", "State", "80 ", "中", "", False)
        End With
        With MyGrid3
            .Clear()
            .Init_Column("申请来源", "Test_Lb", "150", "中", "", False)
            .Init_Column("姓名", "Ry_Name", "120", "中", "", False)
            '.Init_Column("科室", "Ks_Name", "100 ", "中", "", False)
            .Init_Column("处方编码", "His_Code", "120", "中", "", False)
            .Init_Column("检验状态", "TestState", "100 ", "中", "", False)
            .Init_Column("状态", "State", "80 ", "中", "", False)
        End With
        MyGrid2.Splits(0).DisplayColumns("State").FetchStyle = True
        MyGrid3.Splits(0).DisplayColumns("State").FetchStyle = True
        With TestLb
            .Additem = "门诊"
            .Additem = "住院"
            .Additem = "体检"
            .DisplayColumns(1).Visible = False
            .DroupDownWidth = .Width
            .SelectedIndex = 0
        End With
        With RySex
            .Additem = "男"
            .Additem = "女"
            .DisplayColumns(1).Visible = False
            .DroupDownWidth = .Width
            .SelectedIndex = 0
        End With

        With Bxlb
            .DataView = bllBxlb.GetList("Yy_Code='" & HisVar.HisVar.WsyCode & "'").Tables(0).DefaultView
            .Init_Colum("Bxlb_name", "报销类别", 120, "中")
            .Init_Colum("Bxlb_code", "类别编码", 0, "中")
            .Init_Colum("Bxlb_jc", "类别简称", 0, "中")
            .ValueMember = "Bxlb_code"
            .DisplayMember = "Bxlb_name"
            .RowFilterNotTextNull = "Bxlb_jc"
            .DroupDownWidth = .Width
            .SelectedIndex = -1
        End With
        With KsComobo
            .DataView = bllKs.GetList("Yy_Code='" & HisVar.HisVar.WsyCode & "'").Tables(0).DefaultView
            .Init_Colum("ks_name", "科室名称", 120, "中")
            .Init_Colum("ks_code", "科室编码", 0, "中")
            .Init_Colum("ks_jc", "科室简称", 0, "中")
            .ValueMember = "ks_code"
            .DisplayMember = "ks_name"
            .RowFilterNotTextNull = "ks_jc"
            .DroupDownWidth = .Width
            .SelectedIndex = -1
        End With
        With BcComobo
            .DataView = bllBc.GetList("Yy_Code='" & HisVar.HisVar.WsyCode & "'").Tables(0).DefaultView
            .Init_Colum("bc_name", "病区名称", 120, "中")
            .Init_Colum("bc_code", "病区编码", 0, "中")
            .Init_Colum("bc_jc", "病区简称", 0, "中")
            .ValueMember = "bc_code"
            .DisplayMember = "bc_name"
            .RowFilterNotTextNull = "bc_jc"
            .DroupDownWidth = .Width
            .SelectedIndex = -1
        End With
        With TestSample
            .DataView = bllSample.GetList("Elementlb_Code='003'").Tables(0).DefaultView
            .Init_Colum("Element_name", "标本类型", 120, "中")
            .Init_Colum("Element_Code", "标本编码", 0, "中")
            .Init_Colum("Element_jc", "标本简称", 0, "中")
            .ValueMember = "Element_Code"
            .DisplayMember = "Element_name"
            .RowFilterNotTextNull = "Element_jc"
            .DroupDownWidth = .Width
            .SelectedIndex = 0
        End With
        With SendJsr
            .DataView = bllYs.GetList("Yy_Code='" & HisVar.HisVar.WsyCode & "'").Tables(0).DefaultView
            .Init_Colum("Ys_name", "医生姓名", 120, "中")
            .Init_Colum("Ys_code", "医生编码", 0, "中")
            .Init_Colum("Ys_jc", "医生简称", 0, "中")
            .ValueMember = "Ys_code"
            .DisplayMember = "Ys_name"
            .RowFilterNotTextNull = "Ys_jc"
            .DroupDownWidth = .Width
            .SelectedIndex = -1
        End With
        With GetJsr
            .DataView = bllYs.GetList("Yy_Code='" & HisVar.HisVar.WsyCode & "'").Tables(0).DefaultView
            .Init_Colum("Ys_name", "医生姓名", 120, "中")
            .Init_Colum("Ys_code", "医生编码", 0, "中")
            .Init_Colum("Ys_jc", "医生简称", 0, "中")
            .ValueMember = "Ys_code"
            .DisplayMember = "Ys_name"
            .RowFilterNotTextNull = "Ys_jc"
            .DroupDownWidth = .Width
            .SelectedIndex = -1
        End With
        With TestJsr
            .DataView = bllYs.GetList("Yy_Code='" & HisVar.HisVar.WsyCode & "'").Tables(0).DefaultView
            .Init_Colum("Ys_name", "医生姓名", 120, "中")
            .Init_Colum("Ys_code", "医生编码", 0, "中")
            .Init_Colum("Ys_jc", "医生简称", 0, "中")
            .ValueMember = "Ys_code"
            .DisplayMember = "Ys_name"
            .RowFilterNotTextNull = "Ys_jc"
            .DroupDownWidth = .Width
            .SelectedIndex = -1
        End With
        With CheckJsr
            .DataView = bllYs.GetList("Yy_Code='" & HisVar.HisVar.WsyCode & "'").Tables(0).DefaultView
            .Init_Colum("Ys_name", "医生姓名", 120, "中")
            .Init_Colum("Ys_code", "医生编码", 0, "中")
            .Init_Colum("Ys_jc", "医生简称", 0, "中")
            .ValueMember = "Ys_code"
            .DisplayMember = "Ys_name"
            .RowFilterNotTextNull = "Ys_jc"
            .DroupDownWidth = .Width
            .SelectedIndex = -1
        End With
        With TestXm
            .DataView = bllTestXm.GetList("").Tables(0).DefaultView
            .Init_Colum("TestXm_name", "项目名称", 120, "中")
            .Init_Colum("TestXm_code", "项目编码", 0, "中")
            .Init_Colum("TestXm_jc", "项目简称", 0, "中")
            .ValueMember = "TestXm_code"
            .DisplayMember = "TestXm_name"
            .RowFilterNotTextNull = "TestXm_jc"
            .DroupDownWidth = .Width
            .SelectedIndex = -1
        End With
        TestCode.Enabled = False
        SendTime.DisplayFormat = Format(Now, "yyyy-MM-dd HH:mm:ss")
        GetTime.DisplayFormat = Format(Now, "yyyy-MM-dd HH:mm:ss")
        TestTime.DisplayFormat = Format(Now, "yyyy-MM-dd HH:mm:ss")
        CheckTime.DisplayFormat = Format(Now, "yyyy-MM-dd HH:mm:ss")
        SendTime.EditFormat = Format(Now, "yyyy-MM-dd HH:mm:ss")
        GetTime.EditFormat = Format(Now, "yyyy-MM-dd HH:mm:ss")
        TestTime.EditFormat = Format(Now, "yyyy-MM-dd HH:mm:ss")
        CheckTime.EditFormat = Format(Now, "yyyy-MM-dd HH:mm:ss")

    End Sub

    Private Sub Data_Init()
        HisVar.HisVar.Sqldal.QueryDt(My_Dataset, "SELECT Test_Code,Test_Lb,His_Code,Ry_Name,Ry_Sfzh,Ry_Sex,JKkNo,Ry_Age,Ry_Age_Month,Ry_Age_Week,Bxlb_Code,LIS_Test1.Ks_Code,Bc_Code,TestXm_Code,TestSample,TestSample_BarCode" & _
                    ",SendJsr,SendDoctor,SendTime,GetJsr,GetDoctor,GetTime,TestJsr,TestTime,CheckJsr,CheckTime,LisPrintTimes,SelfPrintTimes,Diagnose,Memo,TestItem1,TestState FROM LIS_Test1 where  TestState in ('录入','待采集','待检验') order by Test_Lb,Ry_Name", "申检人员", True)
        My_Cm2 = CType(BindingContext(My_Dataset, "申检人员"), CurrencyManager)
        MyGrid2.DataTable = My_Dataset.Tables("申检人员")


    End Sub

    Private Sub Data_Init2()

        HisVar.HisVar.Sqldal.QueryDt(My_Dataset, "SELECT Test_Code,Test_Lb,His_Code,Ry_Name,Ry_Sfzh,Ry_Sex,JKkNo,Ry_Age,Ry_Age_Month,Ry_Age_Week,Bxlb_Code,LIS_Test1.Ks_Code,Bc_Code,TestXm_Code,TestSample,TestSample_BarCode" & _
                    ",SendJsr,SendDoctor,SendTime,GetJsr,GetDoctor,GetTime,TestJsr,TestTime,CheckJsr,CheckTime,LisPrintTimes,SelfPrintTimes,Diagnose,Memo,TestItem1,TestState FROM LIS_Test1 where  TestState='待审核' order by Test_Lb,Ry_Name", "已检人员", True)
        My_Cm3 = CType(BindingContext(My_Dataset, "已检人员"), CurrencyManager)
        MyGrid3.DataTable = My_Dataset.Tables("已检人员")
    End Sub

    Private Sub Enable(ByVal Bool As Boolean)
        TestLb.Enabled = Bool
        HisCode.Enabled = Bool
        RyName.Enabled = Bool
        RySfzh.Enabled = Bool
        RySex.Enabled = Bool
        JkkNo.Enabled = Bool
        Bxlb.Enabled = Bool
        KsComobo.Enabled = Bool
        BcComobo.Enabled = Bool
        TestXm.Enabled = Bool
    End Sub

    Private Sub Data3(ByVal str As String)
        Dim l As String
        If RySex.Text = "男" Then
            l = ",convert(varchar(100),MaleMin) +'--'+convert(varchar(100),MaleMax) as CK"
        Else
            l = ",convert(varchar(100),FemaleMin) +'--'+convert(varchar(100),FemaleMax) as CK"
        End If
        HisVar.HisVar.Sqldal.QueryDt(My_Dataset, "select LIS_Test2.TestItem_Code,Item_Name,Item_Dw,MaleMax,MaleMin,FemaleMax,FemaleMin,ItemValue,High_low" & l & "  from LIS_Test2,LIS_TestItem where LIS_Test2.TestItem_Code=LIS_TestItem.TestItem_Code and Test_Code ='" & str & "' order by ItemOrder ", "检验结果", True)
        My_Cm1 = CType(BindingContext(My_Dataset, "检验结果"), CurrencyManager)
        MyGrid1.DataTable = My_Dataset.Tables("检验结果")

    End Sub
#End Region

#Region "数据操作"
    '清空控件
    Private Sub Data_Clear()
        V_Insert = True
        TestCode.Enabled = False
        TestCode.Text = BllLIS_Test1.MaxCode()
        TestLb.SelectedIndex = 0
        HisCode.Text = ""
        RyName.Text = ""
        RySfzh.Text = ""
        RySex.SelectedIndex = 0
        JkkNo.Text = ""
        RyAge.Value = 0
        RyAgeMonth.Value = 0
        RyAgeWeek.Value = 0
        Bxlb.SelectedIndex = -1
        KsComobo.SelectedIndex = -1
        BcComobo.SelectedIndex = -1
        TestSample.SelectedIndex = 0
        TestXm.Text = ""
        SendJsr.SelectedIndex = -1
        GetJsr.SelectedIndex = -1
        TestJsr.SelectedIndex = -1
        CheckJsr.SelectedIndex = -1
        SendTime.Value = Now
        GetTime.Value = Now
        TestTime.Value = Now
        CheckTime.Value = Now
        Diagnose.Text = ""
        Memo.Text = ""
        TestState.Text = "录入"
        Call Enable(True)
    End Sub
    '控件赋值
    Private Sub Data_Show(ByVal row As DataRow)
        V_Insert = False
        TestCode.Text = row.Item("Test_Code") & ""
        TestLb.Text = row.Item("Test_Lb") & ""
        HisCode.Text = row.Item("His_Code") & ""
        RyName.Text = row.Item("Ry_Name") & ""
        RySfzh.Text = row.Item("Ry_Sfzh") & ""
        RySex.Text = row.Item("Ry_Sex") & ""
        JkkNo.Text = row.Item("JKkNo") & ""
        RyAge.Value = row.Item("Ry_Age") & ""
        RyAgeMonth.Value = row.Item("Ry_Age_Month") & ""
        RyAgeWeek.Value = row.Item("Ry_Age_Week") & ""
        Bxlb.SelectedValue = row.Item("Bxlb_Code") & ""
        KsComobo.SelectedValue = row.Item("Ks_Code") & ""
        BcComobo.SelectedValue = row.Item("Bc_Code") & ""
        TestSample.Text = row.Item("TestSample") & ""
        TestXm.SelectedValue = row.Item("TestXm_Code") & ""
        SendJsr.SelectedValue = row.Item("SendJsr") & ""
        GetJsr.SelectedValue = row.Item("GetJsr") & ""
        TestJsr.SelectedValue = row.Item("TestJsr") & ""
        CheckJsr.SelectedValue = row.Item("CheckJsr") & ""
        SendTime.Value = row.Item("SendTime")
        GetTime.Value = row.Item("GetTime")
        TestTime.Value = row.Item("TestTime")
        CheckTime.Value = row.Item("CheckTime")
        Diagnose.Text = row.Item("Diagnose") & ""
        Memo.Text = row.Item("Memo") & ""
        TestState.Text = row.Item("TestState") & ""
        If row.Item("TestState") & "" = "待检验" Then
            BtnOk.Enabled = True
            BtnSh.Enabled = False
        ElseIf row.Item("TestState") & "" = "待审核" Then
            BtnOk.Enabled = False
            BtnSh.Enabled = True
        End If
    End Sub
    '检验结果保存
    Private Sub Jg_Save()
        If TestState.Text = "完成" Then
            MsgBox("审核完成，不能进行结果修改！", MsgBoxStyle.Information, "提示")
            Exit Sub
        End If
        Dim highlow As String = ""
        For Each _row In My_Dataset.Tables("检验结果").Rows
            If RySex.Text = "男" Then
                If _row("ItemValue") > _row("MaleMax") Then
                    highlow = 2
                ElseIf _row("ItemValue") < _row("MaleMin") Then
                    highlow = 1
                Else
                    highlow = 0
                End If
            ElseIf RySex.Text = "女" Then
                If _row("ItemValue") > _row("FeMaleMax") Then
                    highlow = 2
                ElseIf _row("ItemValue") < _row("FeMaleMin") Then
                    highlow = 1
                Else
                    highlow = 0
                End If
            End If

            HisVar.HisVar.Sqldal.ExecuteSql("Update Lis_Test2 set ItemValue='" & _row("ItemValue") & "',High_Low='" & highlow & "' where Test_Code='" & TestCode.Text & "' and TestItem_Code='" & _row("TestItem_Code") & "'")
            '
        Next
        Call Data3(TestCode.Text)
    End Sub
#End Region

#Region "控件动作"

    Private Sub MyGrid2_DoubleClick(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyGrid2.DoubleClick
        If (MyGrid2.Row + 1) > MyGrid2.RowCount Then
        Else
            My_Row = My_Cm2.List(MyGrid2.Row).Row
            Call Data_Show(My_Row)
            Call Enable(False)
            CheckJsr.Enabled = False
            CheckTime.Enabled = False
            Call Data3(My_Row.Item("Test_Code"))
            If My_Row.Item("TestState") = "待检验" Then
                BtnTh.Enabled = True
            Else
                BtnTh.Enabled = False
            End If
        End If

    End Sub

    Private Sub MyGrid3_DoubleClick(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyGrid3.DoubleClick
        If (MyGrid3.Row + 1) > MyGrid3.RowCount Then
        Else
            My_Row = My_Cm3.List(MyGrid3.Row).Row
            Call Data_Show(My_Row)
            Call Enable(False)
            CheckJsr.Enabled = True
            CheckTime.Enabled = True
            Call Data3(My_Row.Item("Test_Code"))
        End If
    End Sub

    '检验
    Private Sub BtnOk_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles BtnOk.Click
        If TestXm.Text = "" Then
            MsgBox("检验项目不能为空！")
            Exit Sub
        End If
        'If Bxlb.Text = "" Then
        '    MsgBox("报销类别不能为空！")
        '    Exit Sub
        'End If
        If SendJsr.Text = "" Then
            MsgBox("申请医师不能为空！", MsgBoxStyle.Information, "提示")
            Exit Sub
        End If
        If GetJsr.Text = "" Then
            MsgBox("采集医师不能为空！", MsgBoxStyle.Information, "提示")
            Exit Sub
        End If
        If TestJsr.Text = "" Then
            MsgBox("检验医师不能为空！", MsgBoxStyle.Information, "提示")
            Exit Sub
        End If
        If V_Insert = True Then
            If TestLb.Text <> "住院" And BcComobo.Text = "" Then
                HisVar.HisVar.Sqldal.ExecuteSql("Insert into LIS_Test1 (Test_Code,Test_Lb,His_Code,Ry_Name,Ry_Sfzh,Ry_Sex,JKkNo,Ry_Age,Ry_Age_Month,Ry_Age_Week,Bxlb_Code,Ks_Code,TestXm_Code,TestSample,SendJsr,SendDoctor,SendTime,Diagnose,Memo,TestState) " & _
                                    " values ('" & TestCode.Text & "','" & TestLb.Text & "','" & HisCode.Text & "','" & RyName.Text & "','" & RySfzh.Text & "','" & RySex.Text & "','" & JkkNo.Text & "','" & RyAge.Value & "','" & RyAgeMonth.Value & "','" & RyAgeWeek.Value & "','" & Bxlb.SelectedValue & "','" & KsComobo.SelectedValue & "','" & TestXm.SelectedValue & "','" & TestSample.Text & "','" & SendJsr.SelectedValue & "','" & SendJsr.Text & "','" & SendTime.Value & "','" & Diagnose.Text & "','" & Memo.Text & "','待检验')")
            Else
                HisVar.HisVar.Sqldal.ExecuteSql("Insert into LIS_Test1 (Test_Code,Test_Lb,His_Code,Ry_Name,Ry_Sfzh,Ry_Sex,JKkNo,Ry_Age,Ry_Age_Month,Ry_Age_Week,Bxlb_Code,Ks_Code,Bc_Code,TestXm_Code,TestSample,SendJsr,SendDoctor,SendTime,Diagnose,Memo,TestState) " & _
                                 " values ('" & TestCode.Text & "','" & TestLb.Text & "','" & HisCode.Text & "','" & RyName.Text & "','" & RySfzh.Text & "','" & RySex.Text & "','" & JkkNo.Text & "','" & RyAge.Value & "','" & RyAgeMonth.Value & "','" & RyAgeWeek.Value & "','" & Bxlb.SelectedValue & "','" & KsComobo.SelectedValue & "','" & BcComobo.SelectedValue & "','" & TestXm.SelectedValue & "','" & TestSample.Text & "','" & SendJsr.SelectedValue & "','" & SendJsr.Text & "','" & SendTime.Value & "','" & Diagnose.Text & "','" & Memo.Text & "','待检验')")

            End If

        Else
            HisVar.HisVar.Sqldal.ExecuteSql("Update   LIS_Test1 set SendJsr='" & SendJsr.SelectedValue & "',SendDocTor='" & SendJsr.Text & "',SendTime='" & SendTime.Value & "',GetJsr='" & GetJsr.SelectedValue & "',GetDocTor='" & GetJsr.Text & "',GetTime='" & GetTime.Value & "',TestJsr='" & TestJsr.SelectedValue & "',TestTime='" & TestTime.Value & "',TestState='待审核' where Test_Code='" & TestCode.Text & "'")
        End If
        Call Data_Init()
    End Sub
    '审核
    Private Sub BtnSh_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles BtnSh.Click
        '检验结果出来后enable=true
        'HisVar.HisVar.Sqldal.GetSingle("select ")
        If SendJsr.Text = "" Then
            MsgBox("申请医师不能为空！", MsgBoxStyle.Information, "提示")
            Exit Sub
        End If
        If GetJsr.Text = "" Then
            MsgBox("采集医师不能为空！", MsgBoxStyle.Information, "提示")
            Exit Sub
        End If
        If TestJsr.Text = "" Then
            MsgBox("检验医师不能为空！", MsgBoxStyle.Information, "提示")
            Exit Sub
        End If
        If CheckJsr.Text = "" Then
            MsgBox("审核医师不能为空！", MsgBoxStyle.Information, "提示")
            Exit Sub
        End If
        For Each _row In My_Dataset.Tables("检验结果").Rows
            If _row("ItemValue") & "" = "" Then
                MsgBox("结果数据未填完，不能进行审核！", MsgBoxStyle.Information, "提示")
                Exit Sub
            End If
        Next
        HisVar.HisVar.Sqldal.ExecuteSql("Update   LIS_Test1 set CheckJsr='" & CheckJsr.SelectedValue & "',CheckTime='" & TestTime.Value & "',TestState='完成' where Test_Code='" & TestCode.Text & "'")
       
        Call Data_Init2()
        TestState.Text = "完成"
    End Sub

    '清空、保存结果、打印、读卡、拒检
    Private Sub BtnClear_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles BtnClear.Click, BtnSaveValue.Click, BtnPrint.Click, BtnDk.Click, BtnTh.Click
        Select Case sender.text
            Case "清空"
                Call Data_Clear()
            Case "保存结果"
                Jg_Save()
            Case "打印"

            Case "读卡"
                Dim CardLb As String = MzZy.HD_ConnectDevice(True, True, True, False, False, False, False, False)
                If CardLb <> "读卡失败" Then

                    RyName.Text = Model.Jkk.XM.ToString() & ""
                    If Model.Jkk.XB.ToString() = "01" Or Model.Jkk.XB.ToString() = "1" Then
                        RySex.Text = "男"
                    ElseIf Model.Jkk.XB.ToString() = "02" Or Model.Jkk.XB.ToString() = "2" Then
                        RySex.Text = "女"
                    End If

                    RySfzh.Text = Model.Jkk.SFZH.ToString() & ""
                    JkkNo.Text = Model.Jkk.BankNo.ToString() & ""

                Else
                    MsgBox("读取信息失败，请检查卡及设备后重新读取！", MsgBoxStyle.Information, "提示")
                End If
                MzZy.CloseDevice()
            Case "拒绝申请"
                If V_Insert = True Then
                    MsgBox("请选择待检患者！", MsgBoxStyle.Information, "提示")
                    Exit Sub
                End If

                If MsgBox("确定拒绝检验" + RyName.Text + "的样品检验吗？", MsgBoxStyle.Information + MsgBoxStyle.OkCancel + MsgBoxStyle.DefaultButton1, "提示:") = MsgBoxResult.Cancel Then Exit Sub
                HisVar.HisVar.Sqldal.ExecuteSql("Update LIS_Test1 set TestState='拒绝申请' where Test_Code='" & TestCode.Text & "'")
                Call Data_Init()
                Call Data_Clear()
        End Select


    End Sub

    Private Sub BtnReturn_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles BtnReturn.Click
        Call Data3(TestCode.Text)
    End Sub

    Private Sub TabControlEx1_Selected(ByVal sender As System.Object, ByVal e As System.Windows.Forms.TabControlEventArgs) Handles TabControlEx1.Selected
        If TabControlEx1.SelectedTab Is TabPage1 Then
            Call Data_Init()
        Else
            Call Data_Init2()
        End If
    End Sub

    Private Sub MyGrid1_FetchCellStyle(ByVal sender As Object, ByVal e As C1.Win.C1TrueDBGrid.FetchCellStyleEventArgs) Handles MyGrid1.FetchCellStyle
        If e.Column.Name = "对比值" Then

            If MyGrid1.Columns("对比值").CellValue(e.Row) = "1" Then
                e.CellStyle.ForegroundImage = ZtHis.Lis.My.Resources.下
            ElseIf MyGrid1.Columns("对比值").CellValue(e.Row) = "2" Then
                e.CellStyle.ForegroundImage = ZtHis.Lis.My.Resources.上
            Else
                e.CellStyle.ForegroundImage = ZtHis.Lis.My.Resources.空
            End If
            e.CellStyle.ForeGroundPicturePosition = C1.Win.C1TrueDBGrid.ForeGroundPicturePositionEnum.PictureOnly
        End If
    End Sub

    Private Sub MyGrid1_AfterColEdit(ByVal sender As System.Object, ByVal e As C1.Win.C1TrueDBGrid.ColEventArgs) Handles MyGrid1.AfterColEdit
        Jg_Save()
    End Sub

    Private Sub MyGrid2_FetchCellStyle(ByVal sender As System.Object, ByVal e As C1.Win.C1TrueDBGrid.FetchCellStyleEventArgs) Handles MyGrid2.FetchCellStyle
        If e.Column.Name = "状态" Then
            Dim falg As String = MyGrid2.Columns("TestState").CellValue(e.Row).ToString
            If falg = "录入" Then
                e.CellStyle.ForegroundImage = ZtHis.Lis.My.Resources.录入
            ElseIf falg = "待采集" Then
                e.CellStyle.ForegroundImage = ZtHis.Lis.My.Resources.待采集
            ElseIf falg = "待检验" Then
                e.CellStyle.ForegroundImage = ZtHis.Lis.My.Resources.待检验
            ElseIf falg = "待审核" Then
                e.CellStyle.ForegroundImage = ZtHis.Lis.My.Resources.待审核
            ElseIf falg = "拒绝申请" Then
                e.CellStyle.ForegroundImage = ZtHis.Lis.My.Resources.拒检
            End If
            e.CellStyle.ForeGroundPicturePosition = C1.Win.C1TrueDBGrid.ForeGroundPicturePositionEnum.PictureOnly
        End If
    End Sub
    Private Sub MyGrid3_FetchCellStyle(ByVal sender As System.Object, ByVal e As C1.Win.C1TrueDBGrid.FetchCellStyleEventArgs) Handles MyGrid3.FetchCellStyle
        If e.Column.Name = "状态" Then
            Dim falg As String = MyGrid3.Columns("TestState").CellValue(e.Row).ToString
            If falg = "录入" Then
                e.CellStyle.ForegroundImage = ZtHis.Lis.My.Resources.录入
            ElseIf falg = "待采集" Then
                e.CellStyle.ForegroundImage = ZtHis.Lis.My.Resources.待采集
            ElseIf falg = "待检验" Then
                e.CellStyle.ForegroundImage = ZtHis.Lis.My.Resources.待检验
            ElseIf falg = "待审核" Then
                e.CellStyle.ForegroundImage = ZtHis.Lis.My.Resources.待审核
            ElseIf falg = "拒绝申请" Then
                e.CellStyle.ForegroundImage = ZtHis.Lis.My.Resources.拒检
            End If
            e.CellStyle.ForeGroundPicturePosition = C1.Win.C1TrueDBGrid.ForeGroundPicturePositionEnum.PictureOnly
        End If
    End Sub
#End Region


#Region "输入法设置"
    '中文
    Private Sub C1TextBox7_GotFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles RyName.GotFocus, SendJsr.GotFocus, Diagnose.GotFocus, Memo.GotFocus
        InputLanguage.CurrentInputLanguage = HisVar.HisVar.InputCode
    End Sub

    Private Sub C1Numeric1_KeyDown(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles RySfzh.GotFocus, TestXm.GotFocus, JkkNo.GotFocus
        InputLanguage.CurrentInputLanguage = InputLanguage.InstalledInputLanguages(0)
    End Sub

#End Region

  
   
  

End Class