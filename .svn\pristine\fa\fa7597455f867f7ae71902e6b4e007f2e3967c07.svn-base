<?xml version="1.0" encoding="utf-8"?>
<configuration>
    <configSections>
        <sectionGroup name="applicationSettings" type="System.Configuration.ApplicationSettingsGroup, System, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
            <section name="HisVar.My.MySettings" type="System.Configuration.ClientSettingsSection, System, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false"/>
        </sectionGroup>
    </configSections>
    <system.diagnostics>
        <sources>
            <!-- 本节定义 My.Application.Log 的登录配置-->
            <source name="DefaultSource" switchName="DefaultSwitch">
                <listeners>
                    <add name="FileLog"/>
                    <!-- 取消注释以下一节可写入应用程序事件日志-->
                    <!--<add name="EventLog"/>-->
                </listeners>
            </source>
        </sources>
        <switches>
            <add name="DefaultSwitch" value="Information"/>
        </switches>
        <sharedListeners>
            <add name="FileLog" type="Microsoft.VisualBasic.Logging.FileLogTraceListener, Microsoft.VisualBasic, Version=8.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL" initializeData="FileLogWriter"/>
            <!-- 取消注释以下一节并用应用程序名替换 APPLICATION_NAME 可写入应用程序事件日志-->
            <!--<add name="EventLog" type="System.Diagnostics.EventLogTraceListener" initializeData="APPLICATION_NAME"/> -->
        </sharedListeners>
    </system.diagnostics>
    <applicationSettings>
        <HisVar.My.MySettings>
            <setting name="HisVar_WebReference_DotNetService" serializeAs="String">
                <value>http://127.0.0.1/His_DataBase/DotNetService.asmx</value>
            </setting>
            <setting name="HisVar_MztcXyDb_OracleService" serializeAs="String">
                <value>http://localhost/Qx_Database/OracleService.asmx</value>
            </setting>
            <setting name="HisVar_DzblFileSend_DzblFileSend" serializeAs="String">
                <value>http://localhost/His_Database/DzblFileSend.asmx</value>
            </setting>
            <setting name="HisVar_JkkService_WebService1" serializeAs="String">
                <value>http://localhost:8312/WebService1.asmx</value>
            </setting>
            <setting name="HisVar_YpNetCg_WebService" serializeAs="String">
                <value>http://************/Ds_Service_temp/WebService.asmx</value>
            </setting>
        </HisVar.My.MySettings>
    </applicationSettings>
<startup><supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.0"/></startup></configuration>
