﻿Imports System.Data.SqlClient
Imports HisControl
Imports BaseClass

Public Class YkYf_Ck3

#Region "定义变量"
    Dim My_DataSet As New DataSet
#End Region

#Region "传参"
    Dim Rform As BaseForm
    Dim Rinsert As Boolean
    Dim Rrow As DataRow
    Dim RZbtb As DataTable
    Dim Rtdbgrid As C1.Win.C1TrueDBGrid.C1TrueDBGrid
    Dim Rcode As String
    Dim Rrc As C_RowChange
    Dim Form_Lb As String
#End Region

    Public Sub New(ByVal tform As BaseForm, ByVal tinsert As Boolean, ByVal trow As DataRow, ByVal tZbtb As DataTable, ByVal ttdbgrid As C1.Win.C1TrueDBGrid.C1TrueDBGrid, ByVal tcode As String, ByRef trc As C_RowChange, ByVal tform_lb As String)
        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()
        Rform = tform
        Rinsert = tinsert
        Rrow = trow
        RZbtb = tZbtb
        Rtdbgrid = ttdbgrid
        Rcode = tcode
        Rrc = trc
        Form_Lb = tform_lb
        ' 在 InitializeComponent() 调用之后添加任何初始化。
    End Sub

    Protected Overrides Sub OnKeyDown(ByVal e As System.Windows.Forms.KeyEventArgs)
        MyBase.OnKeyDown(e)
        If e.Control = True And e.KeyCode = Keys.S Then
            Comm1.Select()
            Call Comm_Click(Comm1, Nothing)
        End If
    End Sub

    Private Sub Yk_Ks3_FormClosed(ByVal sender As Object, ByVal e As System.Windows.Forms.FormClosedEventArgs) Handles Me.FormClosed
        RemoveHandler Rrc.RowChanged, New BaseClass.RowChangedHandler(AddressOf Data_Show)
        My_DataSet.Dispose()
        Rtdbgrid.Select()
    End Sub

    Private Sub Yk_Rk31_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        AddHandler Rrc.RowChanged, New BaseClass.RowChangedHandler(AddressOf Data_Show)
        Call Form_Init()
        If Rinsert = True Then
            Data_Clear()
        Else
            Data_Show(Rrow)
        End If
    End Sub

#Region "窗体__事件"

    Private Sub Form_Init()
        '按扭初始化
        Call P_Comm(Me.Comm1)
        Call P_Comm(Me.Comm2)

        With Me.C1Numeric1
            .Value = 0
            .FormatType = C1.Win.C1Input.FormatTypeEnum.CustomFormat
            .CustomFormat = "###,###,###0.####"
            .VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
            .ShowContextMenu = False
            .EmptyAsNull = True
        End With

        With Me.C1Numeric2
            .Value = 0
            .FormatType = C1.Win.C1Input.FormatTypeEnum.CustomFormat
            .CustomFormat = "###,###,###0.00####"
            .VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
            .ShowContextMenu = False
            .EmptyAsNull = True
        End With

        With Me.C1NumericEdit1
            .Value = 0
            .FormatType = C1.Win.C1Input.FormatTypeEnum.CustomFormat
            .CustomFormat = "###,###,###0.00####"
            .VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
            .ShowContextMenu = False
            .EmptyAsNull = True
        End With

        With Me.C1NumericEdit2
            .Value = 0
            .FormatType = C1.Win.C1Input.FormatTypeEnum.CustomFormat
            .CustomFormat = "###,###,###0.00####"
            .VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
            .ShowContextMenu = False
            .EmptyAsNull = True
        End With


        With Me.C1DateEdit1
            .Value = DBNull.Value
            .DateTimeInput = True     '显示日期的开关
            .AutoChangePosition = False
            .CaseSensitive = True
            .VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
            .InitialSelection = C1.Win.C1Input.InitialSelectionEnum.CaretAtStart
            .VisibleButtons = C1.Win.C1Input.DropDownControlButtonFlags.None
            .FormatType = C1.Win.C1Input.FormatTypeEnum.CustomFormat
            .EditFormat.FormatType = C1.Win.C1Input.FormatTypeEnum.CustomFormat
            .EditFormat.CustomFormat = "yyyy-MM-dd"
            .DisplayFormat.FormatType = C1.Win.C1Input.FormatTypeEnum.CustomFormat
            .DisplayFormat.CustomFormat = "yyyy-MM-dd"
            .MaskInfo.AutoTabWhenFilled = True
            .AcceptsTab = True
            .EmptyAsNull = True
            .ValueIsDbNull = True
            With .ErrorInfo
                .BeepOnError = True
                .CanLoseFocus = False
                .ErrorAction = C1.Win.C1Input.ErrorActionEnum.None
                .ErrorProvider = Me.ErrorProvider1
            End With
        End With
        Me.Text = Form_Lb & "明细录入"

        If Form_Lb = "药库科室支领" Or Form_Lb = "药库药品批发" Then
            HisVar.HisVar.Sqldal.QueryDt(My_DataSet, "Select  Yp_Jc,Yp_Name,Jx_Name,Mx_Gyzz,Mx_Gg,Mx_Cd,Mx_Code,Jx_Code,Mx_Cgdw,Mx_Xsdw,Mx_Cfbl,Dl_Code,Yp_Code,Yk_Sl,Yk_Cgj,Yk_Xsj,Yk_Pfj,Yp_Ph,Yp_Yxq,Xx_Code,IsJb From V_YpKc Where Yy_Code='" & HisVar.HisVar.WsyCode & "' and Yp_Yxq>='" & Format(Now, "yyyy-MM-dd") & "' Order by Yp_Jc", "药品字典", True)
        ElseIf Form_Lb = "药房科室支领" Or Form_Lb = "药房药品批发" Then
            HisVar.HisVar.Sqldal.QueryDt(My_DataSet, "Select  Yp_Jc,Yp_Name,Jx_Name,Mx_Gyzz,Mx_Gg,Mx_Cd,Mx_Code,Jx_Code,Mx_Cgdw,Mx_Xsdw,Mx_Cfbl,Dl_Code,Yp_Code,Yf_Sl" & Mid(HisVar.HisVar.YfCode, 6) & " as Yk_Sl,Yk_Cgj/Mx_Cfbl as Yk_Cgj,Yk_Xsj/Mx_Cfbl as Yk_Xsj,Yk_Pfj/Mx_Cfbl as Yk_Pfj,Yp_Ph,Yp_Yxq,Xx_Code,IsJb From V_YpKc Where Yy_Code='" & HisVar.HisVar.WsyCode & "' and Yp_Yxq>='" & Format(Now, "yyyy-MM-dd") & "' Order by Yp_Jc", "药品字典", True)
        ElseIf Form_Lb = "药房退回药库" Then
            HisVar.HisVar.Sqldal.QueryDt(My_DataSet, "Select  Yp_Jc,Yp_Name,Jx_Name,Mx_Gyzz,Mx_Gg,Mx_Cd,Mx_Code,Jx_Code,Mx_Cgdw,Mx_Xsdw,Mx_Cfbl,Dl_Code,Yp_Code,Yf_Sl" & Mid(HisVar.HisVar.YfCode, 6) & " as Yk_Sl,Yk_Cgj/Mx_Cfbl as Yk_Cgj,Yk_Xsj/Mx_Cfbl as Yk_Xsj,Yk_Pfj/Mx_Cfbl as Yk_Pfj,Yp_Ph,Yp_Yxq,Xx_Code,IsJb  From V_YpKc Where Yy_Code='" & HisVar.HisVar.WsyCode & "' And Yf_Sl" & Mid(HisVar.HisVar.YfCode, 6) & ">0 Order by Yp_Jc", "药品字典", True)
            CheckBox1.Visible = False
        ElseIf Form_Lb = "药库调拨药房" Then
            If Rinsert = False Then
                HisVar.HisVar.Sqldal.QueryDt(My_DataSet, "Select  Yp_Jc,Yp_Name,Jx_Name,Mx_Gyzz,Mx_Gg,Mx_Cd,Mx_Code,Jx_Code,Mx_Cgdw,Mx_Xsdw,Mx_Cfbl,Dl_Code,Yp_Code,(Yk_Sl-isnull(Db_Sl,0))AS Yk_Sl,Yk_Cgj,Yk_Xsj,Yk_Pfj,Yp_Ph,Yp_Yxq,V_YpKc.Xx_Code,IsJb  From V_YpKc Left Join (select Xx_Code,Sum(Ck_Sl) AS Db_Sl from Yk_Yf1,Yk_Yf2 Where Yk_Yf1.Ck_Code=Yk_Yf2.Ck_Code and Ck_Qr=0 And Yk_Yf1.Yy_Code='" & HisVar.HisVar.WsyCode & "' and Yk_Yf1.Ck_Code<>'" & Rcode & "' group By Xx_Code) a on V_Ypkc.Xx_Code=a.Xx_Code  Where Yy_Code='" & HisVar.HisVar.WsyCode & "' And Yk_Sl>0 and Yp_Yxq>='" & Format(Now, "yyyy-MM-dd") & "' Order by Yp_Jc", "药品字典", True)
            Else
                HisVar.HisVar.Sqldal.QueryDt(My_DataSet, "Select  Yp_Jc,Yp_Name,Jx_Name,Mx_Gyzz,Mx_Gg,Mx_Cd,Mx_Code,Jx_Code,Mx_Cgdw,Mx_Xsdw,Mx_Cfbl,Dl_Code,Yp_Code,(Yk_Sl-isnull(Db_Sl,0))AS Yk_Sl,Yk_Cgj,Yk_Xsj,Yk_Pfj,Yp_Ph,Yp_Yxq,V_YpKc.Xx_Code,IsJb  From V_YpKc Left Join (select Xx_Code,Sum(Ck_Sl) AS Db_Sl from Yk_Yf1,Yk_Yf2 Where Yk_Yf1.Ck_Code=Yk_Yf2.Ck_Code and Ck_Qr=0 And Yk_Yf1.Yy_Code='" & HisVar.HisVar.WsyCode & "' group By Xx_Code) a on V_Ypkc.Xx_Code=a.Xx_Code  Where Yy_Code='" & HisVar.HisVar.WsyCode & "' And Yk_Sl>0 and  (Yk_Sl-isnull(Db_Sl,0))>0 and Yp_Yxq>='" & Format(Now, "yyyy-MM-dd") & "' Order by Yp_Jc", "药品字典", True)
            End If
            CheckBox1.Visible = False
        End If

        Dim My_Combo As BaseClass.C_Combo2 = New BaseClass.C_Combo2(C1Combo1, My_DataSet.Tables("药品字典").DefaultView, "Yp_Name", "Xx_Code", 970)
        With My_Combo
            .Init_TDBCombo()
            .Init_Colum("Yp_Jc", "简称", 80, "左")
            .Init_Colum("Yp_Name", "名称", 150, "左")
            .Init_Colum("Jx_Name", "剂型", 65, "左")
            .Init_Colum("Mx_Gyzz", "批准文号", 70, "左")
            .Init_Colum("Mx_Gg", "规格", 65, "左")
            .Init_Colum("Mx_Cd", "产地", 80, "左")
            .Init_Colum("Yk_Sl", "库存", 50, "右")
            .Init_Colum("Yk_Cgj", "采购价", 55, "右")
            .Init_Colum("Yk_Xsj", "销售价", 50, "右")
            .Init_Colum("Yk_Pfj", "批发价", 50, "右")
            .Init_Colum("Yp_Ph", "批号", 60, "左")
            .Init_Colum("Yp_Yxq", "有效期", 67, "左")
            .Init_Colum("IsJb", "基本药品", 80, "中")

            .Init_Colum("Xx_Code", "", 0, "中")
            .Init_Colum("Mx_Code", "", 0, "中")
            .Init_Colum("Jx_Code", "", 0, "中")
            .Init_Colum("Mx_Cgdw", "", 0, "中")
            .Init_Colum("Mx_Xsdw", "", 0, "中")
            .Init_Colum("Mx_Cfbl", "", 0, "中")
            .Init_Colum("Dl_Code", "", 0, "中")
            .Init_Colum("Yp_Code", "", 0, "中")
            .MaxDropDownItems(15)
            .SelectedIndex(-1)
        End With
        With C1Combo1
            .Columns("Yk_Sl").NumberFormat = "0.###"
            .Columns("Yk_Cgj").NumberFormat = "0.00####"
            .Columns("Yk_Xsj").NumberFormat = "0.00####"
            .Columns("Yk_Pfj").NumberFormat = "0.00####"
            .Columns("Yp_Yxq").NumberFormat = "yyyy-MM-dd"
            .AutoCompletion = False
            .AutoSelect = False
        End With
        C1Combo1.AutoCompletion = False
        C1Combo1.AutoSelect = False
        '药品字典视图

        My_DataSet.Tables("药品字典").DefaultView.Sort = "Yp_Jc Asc "

        If iniOperate.iniopreate.GetINI("显示零库存", "科室支领", "", HisVar.HisVar.Parapath & "\Config.Ini") & "" = "" Then
            CheckBox1.CheckState = CheckState.Checked
        Else
            CheckBox1.CheckState = iniOperate.iniopreate.GetINI("显示零库存", "科室支领", "", HisVar.HisVar.Parapath & "\Config.Ini")
        End If

        If CheckBox1.Checked = True Then
            My_DataSet.Tables("药品字典").DefaultView.RowFilter = ""
        Else
            My_DataSet.Tables("药品字典").DefaultView.RowFilter = "Yk_Sl>0"
        End If
    End Sub

#End Region

#Region "其它项目"

    Private Sub CheckBox1_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles CheckBox1.CheckedChanged

        If CheckBox1.Checked = True Then
            My_DataSet.Tables("药品字典").DefaultView.RowFilter = ""
        Else
            My_DataSet.Tables("药品字典").DefaultView.RowFilter = "Yk_Sl>0"
        End If

        If Rinsert = False Then
            C1Combo1.SelectedValue = Rrow.Item("Xx_Code") & ""
        Else
            Call Data_Clear()
        End If

        iniOperate.iniopreate.WriteINI("显示零库存", "科室支领", CheckBox1.CheckState, HisVar.HisVar.Parapath & "\Config.Ini")
    End Sub

    Private Sub C1Numeric_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles C1TextBox7.KeyPress, C1DateEdit1.KeyPress, C1Numeric1.KeyPress
        If e.KeyChar <> Chr(Keys.Return) Then Exit Sub
        e.Handled = True
        System.Windows.Forms.SendKeys.Send("{Tab}")
    End Sub

    Private Sub C1Numeric1_Validated(ByVal sender As Object, ByVal e As System.EventArgs) Handles C1Numeric1.Validated
        If C1Numeric1.Value Is DBNull.Value Then C1Numeric1.Value = 0
        If C1Numeric2.Value Is DBNull.Value Then C1Numeric2.Value = 0
        If C1NumericEdit1.Value Is DBNull.Value Then C1NumericEdit1.Value = 0
        Label27.Text = Format(C1Numeric1.Value * C1Numeric2.Value, "0.00####")
        Label21.Text = Format(C1Numeric1.Value * C1NumericEdit1.Value, "0.00####")
        Label19.Text = Format(C1Numeric1.Value * C1NumericEdit2.Value, "0.00####")
    End Sub

#End Region

#Region "控件__动作"

    Private Sub Comm_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Comm1.Click, Comm2.Click
        Select Case sender.tag
            Case "保存"
                If C1Combo1.SelectedValue = "" Then
                    Beep()
                    MsgBox("药品编码不能为空!", MsgBoxStyle.OkOnly + MsgBoxStyle.Exclamation, "提示:")
                    C1Combo1.Select()
                    Exit Sub
                End If

                If C1Numeric1.Text = "" Then
                    Beep()
                    MsgBox("数量不能为空!", MsgBoxStyle.OkOnly + MsgBoxStyle.Exclamation, "提示:")
                    C1Numeric1.Select()
                    Exit Sub
                End If

                If C1Numeric1.Value > CDbl(Label15.Text) Then
                    Beep()
                    MsgBox("数量不能大于库存数量!", MsgBoxStyle.OkOnly + MsgBoxStyle.Exclamation, "提示:")
                    C1Numeric1.Select()
                    Exit Sub
                End If
                If Form_Lb = "药房退回药库" Or Form_Lb = "药库调拨药房" Then
                    If C1Numeric1.Value <= 0 Then
                        Beep()
                        MsgBox("请输入有效的数量!", MsgBoxStyle.OkOnly + MsgBoxStyle.Exclamation, "提示:")
                        C1Numeric1.Select()
                        Exit Sub
                    End If
                End If

                Call Save_Add()

                Rform.F_Sum()
            Case "取消"
                C1Combo1.SelectedValue = -1
                C1Combo1.Text = ""
                Rtdbgrid.Focus()
                Me.Close()
        End Select
    End Sub

    Private Sub C1Move_Click(ByVal sender As Object, ByVal e As C1.Win.C1Command.ClickEventArgs) Handles Move2.Click, Move3.Click, Move5.Click
        If sender.text = "新增" Then
            Rinsert = True
            Call Data_Clear()
        Else
            Rinsert = False
            Select Case sender.text
                Case "上移"
                    Rtdbgrid.MovePrevious()
                Case "下移"
                    Rtdbgrid.MoveNext()
            End Select

        End If

    End Sub

    Private Sub C1Numeric1_ValueChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles C1Numeric1.ValueChanged

        If C1Combo1.SelectedValue = 0 Then Exit Sub
        If Form_Lb = "药库科室支领" Or Form_Lb = "药库药品批发" Or Form_Lb = "药库调拨药房" Then
            Label22.Text = Format(C1Numeric1.Value * C1Combo1.Columns("Mx_Cfbl").Value, "0.######") & C1Combo1.Columns("Mx_Xsdw").Value
        ElseIf Form_Lb = "药房科室支领" Or Form_Lb = "药房药品批发" Or Form_Lb = "药房退回药库" Then
            Label22.Text = Format(C1Numeric1.Value / C1Combo1.Columns("Mx_Cfbl").Value, "0.######") & C1Combo1.Columns("Mx_Cgdw").Value
        End If

    End Sub
#Region "C1Combo1"

    Private Sub C1Combo1_RowChange(ByVal sender As Object, ByVal e As System.EventArgs) Handles C1Combo1.RowChange
        If C1Combo1.Text = "" Then
        Else
            If C1Combo1.WillChangeToValue = "" Then

            Else
                Label24.Text = C1Combo1.Columns("Mx_Gyzz").Value & ""
                Label5.Text = C1Combo1.Columns("Jx_Name").Value & ""
                Label17.Text = Format(C1Combo1.Columns("Mx_Cfbl").Value, "0.#####")
                Label25.Text = C1Combo1.Columns("Mx_Gg").Value & ""
                Label26.Text = C1Combo1.Columns("Mx_Cd").Value & ""
                C1Numeric2.Value = C1Combo1.Columns("Yk_Cgj").Value
                C1NumericEdit1.Value = C1Combo1.Columns("Yk_Xsj").Value
                C1NumericEdit2.Value = C1Combo1.Columns("Yk_Pfj").Value
                If Form_Lb = "药库科室支领" Or Form_Lb = "药库药品批发" Or Form_Lb = "药库调拨药房" Then
                    Label23.Text = C1Combo1.Columns("Mx_Cgdw").Value & ""
                    Label08.Text = "/" & C1Combo1.Columns("Mx_Cgdw").Value & ""
                    Label09.Text = "/" & C1Combo1.Columns("Mx_Cgdw").Value & ""
                    Label28.Text = "/" & C1Combo1.Columns("Mx_Cgdw").Value & ""
                    Label8.Text = C1Combo1.Columns("Mx_Cgdw").Value & ""
                    Label22.Text = Format(C1Numeric1.Value * C1Combo1.Columns("Mx_Cfbl").Value, "0.#####") & " " & C1Combo1.Columns("Mx_Xsdw").Value
                ElseIf Form_Lb = "药房科室支领" Or Form_Lb = "药房药品批发" Or Form_Lb = "药房退回药库" Then
                    Label23.Text = C1Combo1.Columns("Mx_Xsdw").Value & ""
                    Label08.Text = "/" & C1Combo1.Columns("Mx_Xsdw").Value & ""
                    Label09.Text = "/" & C1Combo1.Columns("Mx_Xsdw").Value & ""
                    Label28.Text = "/" & C1Combo1.Columns("Mx_Xsdw").Value & ""
                    Label8.Text = C1Combo1.Columns("Mx_Xsdw").Value & ""
                    Label22.Text = Format(C1Numeric1.Value / C1Combo1.Columns("Mx_Cfbl").Value, "0.#####") & " " & C1Combo1.Columns("Mx_CgDw").Value
                End If
                Label15.Text = Format(C1Combo1.Columns("Yk_Sl").Value, "0.#####")
                Label4.Text = C1Combo1.Columns("Yp_Ph").Value & ""
                C1DateEdit1.Value = C1Combo1.Columns("Yp_Yxq").Value & ""

            End If
        End If
    End Sub

    Private Sub C1Combo1_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles C1Combo1.KeyUp
        If (e.KeyValue > 47 And e.KeyValue < 58) Or (e.KeyValue > 96 And e.KeyValue < 123) Or (e.KeyValue > 64 And e.KeyValue < 91) Or (e.KeyValue = 8) Or (e.KeyValue = 45) Or (e.KeyValue = 46) Then
            Dim s As String = C1Combo1.Text
            If CheckBox1.Checked = True Then
                If C1Combo1.Text = "" Then
                    C1Combo1.DataSource.RowFilter = ""
                Else
                    C1Combo1.DataSource.RowFilter = "Yp_Jc like '*" & C1Combo1.Text & "*'"
                End If
            Else
                If C1Combo1.Text = "" Then
                    C1Combo1.DataSource.RowFilter = "Yk_Sl>0"
                Else
                    C1Combo1.DataSource.RowFilter = "Yp_Jc like '*" & C1Combo1.Text & "*' and Yk_Sl>0"
                End If
            End If

            If (e.KeyValue = 8) Then
                If C1Combo1.Text <> "" Then
                    C1Combo1.DroppedDown = False
                    C1Combo1.DroppedDown = True
                Else
                    C1Combo1.DroppedDown = False
                End If
            End If
            C1Combo1.Text = s
            C1Combo1.SelectionStart = C1Combo1.Text.Length
            C1Combo1.SelectionLength = 0
        End If
    End Sub

    Private Sub C1Combo1_KeyDown(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles C1Combo1.KeyDown
        If e.KeyValue = 13 Then
            If C1Combo1.WillChangeToIndex < 1 Then
                If (CType(C1Combo1.DataSource, DataView).Count) = 0 Then
                    MsgBox("药品: '" + Me.C1Combo1.Text + "' 不存在！", MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
                    System.Windows.Forms.SendKeys.Send("^{A}")
                    Exit Sub
                End If
                C1Combo1.SelectedIndex = -1
                C1Combo1.SelectedIndex = 0
            Else
                Dim index As Integer
                index = C1Combo1.WillChangeToIndex
                C1Combo1.SelectedIndex = -1
                C1Combo1.SelectedIndex = index
            End If
            e.Handled = True
            System.Windows.Forms.SendKeys.Send("{Tab}")

        End If
    End Sub
#End Region

#End Region

#Region "数据__编辑"

    Private Sub Data_Clear()
        Rinsert = True
        If Move5.Enabled = True Then Move5.Enabled = False
        C1Combo1.Enabled = True
        C1Combo1.EditorBackColor = SystemColors.Window
        C1Combo1.SelectedIndex = -1
        Label24.Text = ""
        Label26.Text = ""                                    '生产厂家
        Label25.Text = ""
        Label5.Text = ""
        Label17.Text = "" '产品规格
        Label4.Text = ""
        C1DateEdit1.Value = ""
        C1Numeric1.Value = 0
        Label23.Text = ""
        Label22.Text = ""
        Label15.Text = ""
        C1Numeric2.Value = 0
        C1NumericEdit1.Value = 0
        C1NumericEdit2.Value = 0
        Label27.Text = Format(0, "0.00")
        Label21.Text = Format(0, "0.00")
        Label19.Text = Format(0, "0.00")
        C1TextBox7.Text = ""                                    '备注
        C1Combo1.Select()
    End Sub

    Private Sub Data_Show(ByVal tmp_Row As DataRow)
        If CheckBox1.Checked = True Then
            C1Combo1.DataSource.RowFilter = "1=1 "
        Else
            C1Combo1.DataSource.RowFilter = "Yk_Sl>0 "
        End If

        Rinsert = False

        If Rtdbgrid.RowCount = 0 Then Exit Sub
        If Rtdbgrid.Row >= Rtdbgrid.RowCount Then Exit Sub
        If Move5.Enabled = False Then Move5.Enabled = True
        T_Label2.Text = CStr((Rtdbgrid.Row) + 1)
        Rrow = tmp_Row
        With Rrow
            C1Combo1.Enabled = False
            C1Combo1.EditorBackColor = SystemColors.Info
            CheckBox1.Enabled = False
            C1Combo1.SelectedValue = Rrow.Item("Xx_Code") & ""

            If Form_Lb = "药库科室支领" Then
                C1Numeric1.Value = .Item("Ck_Sl")
                C1TextBox7.Text = .Item("Ck_Memo") & ""             '备注
            ElseIf Form_Lb = "药房科室支领" Or Form_Lb = "药库调拨药房" Then
                C1Numeric1.Value = .Item("Ck_Sl")
                C1TextBox7.Text = .Item("Ck_Memo") & ""             '备注
            ElseIf Form_Lb = "药库药品批发" Or Form_Lb = "药房药品批发" Then
                C1Numeric1.Value = .Item("Ck_Sl")
                C1TextBox7.Text = .Item("Ck_Memo") & ""             '备注
            ElseIf Form_Lb = "药房退回药库" Then
                C1Numeric1.Value = .Item("Tk_Sl")                   '采购数量
                C1TextBox7.Text = .Item("Tk_Memo") & ""
            End If
            Label27.Text = Format(C1Numeric1.Value * C1Numeric2.Value, "0.00####")
            Label21.Text = Format(C1Numeric1.Value * C1NumericEdit1.Value, "0.00####")
            Label19.Text = Format(C1Numeric1.Value * C1NumericEdit2.Value, "0.00####")

        End With
        C1Numeric1.Select()

    End Sub

    Private Sub Save_Add()
        Dim My_NewRow As DataRow
        If Rinsert = True Then
            My_NewRow = RZbtb.NewRow
        Else
            My_NewRow = Rrow
        End If

        With My_NewRow
            .BeginEdit()
            .Item("Yy_Code") = HisVar.HisVar.WsyCode
            .Item("Xx_Code") = C1Combo1.SelectedValue                         '药品明细编码
            If Form_Lb = "药库科室支领" Or Form_Lb = "药房科室支领" Or Form_Lb = "药库调拨药房" Then
                .Item("Ck_Code") = Rcode
                .Item("Ck_Sl") = C1Numeric1.Value
                .Item("Ck_Cgj") = C1Combo1.Columns("Yk_Cgj").Value  '采购单价
                .Item("Ck_Pfj") = C1Combo1.Columns("Yk_Pfj").Value
                .Item("Ck_Xsj") = C1Combo1.Columns("Yk_Xsj").Value
                If Form_Lb = "药库科室支领" Then
                    .Item("Ck_Money") = C1Combo1.Columns("Yk_Cgj").Value * C1Numeric1.Value
                ElseIf Form_Lb = "药房科室支领" Or Form_Lb = "药库调拨药房" Then
                    .Item("Ck_Money") = C1Combo1.Columns("Yk_Xsj").Value * C1Numeric1.Value
                End If
                .Item("Ck_Memo") = C1TextBox7.Text & ""             '备注
            ElseIf Form_Lb = "药库药品批发" Or Form_Lb = "药房药品批发" Then
                .Item("Ck_Code") = Rcode
                .Item("Ck_Sl") = C1Numeric1.Value
                .Item("Ck_Dj") = C1NumericEdit2.Value
                .Item("Ck_Money") = C1NumericEdit2.Value * C1Numeric1.Value             '采购金额
                .Item("Ck_Memo") = C1TextBox7.Text & ""             '备注
            ElseIf Form_Lb = "药房退回药库" Then
                .Item("Tk_Code") = Rcode
                .Item("Tk_Sl") = C1Numeric1.Value                   '采购单价
                .Item("Tk_Dj") = C1NumericEdit1.Value                   '采购单价
                .Item("Tk_Money") = C1NumericEdit1.Value * C1Numeric1.Value
                .Item("Tk_Memo") = C1TextBox7.Text & ""             '备注
            End If
            .Item("Yp_Ph") = Label4.Text & ""
            .Item("Yp_Yxq") = C1DateEdit1.Value
            .Item("Jx_Name") = Label5.Text
            .Item("Dl_Code") = C1Combo1.Columns("Dl_Code").Value & ""
            .Item("Yp_Name") = C1Combo1.Text & ""
            .Item("Mx_Gg") = Label25.Text & ""               '药品规格
            .Item("Mx_Gyzz") = Label24.Text.ToUpper & ""     '批准文号
            .Item("Mx_Cd") = Label26.Text & ""             '生产单位
            .Item("Mx_CgDw") = C1Combo1.Columns("Mx_Cgdw").Text & ""             '销售单位
            .Item("Mx_XsDw") = C1Combo1.Columns("Mx_XsDw").Value & ""
            .Item("IsJb") = C1Combo1.Columns("IsJb").Value & ""
            .EndEdit()
        End With

        Call Para_Int(My_NewRow)
      
    End Sub

    Private Sub Para_Int(ByVal Row As DataRow)
        Dim Insert_String As String = ""
        Dim Update_String As String = ""
        Dim para() As SqlParameter = Nothing
        Dim ilist As List(Of SqlParameter) = New List(Of SqlParameter)()

        If Form_Lb = "药库科室支领" Or Form_Lb = "药房科室支领" Or Form_Lb = "药库调拨药房" Then
            ilist.Add(New SqlParameter("@Yy_Code", SqlDbType.Char))
            ilist.Add(New SqlParameter("@Ck_Sl", SqlDbType.Decimal))
            ilist.Add(New SqlParameter("@Ck_Cgj", SqlDbType.Decimal))
            ilist.Add(New SqlParameter("@Ck_Pfj", SqlDbType.Decimal))
            ilist.Add(New SqlParameter("@Ck_Xsj", SqlDbType.Decimal))
            ilist.Add(New SqlParameter("@Ck_Money", SqlDbType.Decimal))
            ilist.Add(New SqlParameter("@Ck_Memo", SqlDbType.VarChar))
            ilist.Add(New SqlParameter("@Ck_Code", SqlDbType.Char))
            ilist.Add(New SqlParameter("@Xx_Code", SqlDbType.VarChar))
            If Form_Lb = "药库科室支领" Then
                Insert_String = "Insert Into Yk_Ks2(Yy_Code,Ck_Sl,Ck_Cgj,Ck_Pfj,Ck_Xsj,Ck_Money,Ck_Memo,Ck_Code,Xx_Code)Values(@Yy_Code,@Ck_Sl,@Ck_Cgj,@Ck_Pfj,@Ck_Xsj,@Ck_Money,@Ck_Memo,@Ck_Code,@Xx_Code)"
                Update_String = "Update Yk_Ks2 Set Yy_Code=@Yy_Code,Ck_Sl=@Ck_Sl,Ck_Cgj=@Ck_Cgj,Ck_Pfj=@Ck_Pfj,Ck_Xsj=@Ck_Xsj,Ck_Money=@Ck_Money,Ck_Memo=@Ck_Memo Where Ck_Code=@Ck_Code and Xx_Code=@Xx_Code"
            ElseIf Form_Lb = "药房科室支领" Then
                Insert_String = "Insert Into Yf_Ks2(Yy_Code,Ck_Sl,Ck_Cgj,Ck_Pfj,Ck_Xsj,Ck_Money,Ck_Memo,Ck_Code,Xx_Code)Values(@Yy_Code,@Ck_Sl,@Ck_Cgj,@Ck_Pfj,@Ck_Xsj,@Ck_Money,@Ck_Memo,@Ck_Code,@Xx_Code)"
                Update_String = "Update Yf_Ks2 Set Yy_Code=@Yy_Code,Ck_Sl=@Ck_Sl,Ck_Cgj=@Ck_Cgj,Ck_Pfj=@Ck_Pfj,Ck_Xsj=@Ck_Xsj,Ck_Money=@Ck_Money,Ck_Memo=@Ck_Memo Where Ck_Code=@Ck_Code and Xx_Code=@Xx_Code"
            ElseIf Form_Lb = "药库调拨药房" Then
                Insert_String = "Insert Into Yk_Yf2(Yy_Code,Ck_Sl,Ck_Cgj,Ck_Pfj,Ck_Xsj,Ck_Money,Ck_Memo,Ck_Code,Xx_Code)Values(@Yy_Code,@Ck_Sl,@Ck_Cgj,@Ck_Pfj,@Ck_Xsj,@Ck_Money,@Ck_Memo,@Ck_Code,@Xx_Code)"
                Update_String = "Update Yk_Yf2 Set Yy_Code=@Yy_Code,Ck_Sl=@Ck_Sl,Ck_Cgj=@Ck_Cgj,Ck_Pfj=@Ck_Pfj,Ck_Xsj=@Ck_Xsj,Ck_Money=@Ck_Money,Ck_Memo=@Ck_Memo Where Ck_Code=@Ck_Code and Xx_Code=@Xx_Code"
            End If
        ElseIf Form_Lb = "药库药品批发" Or Form_Lb = "药房药品批发" Then
            ilist.Add(New SqlParameter("@Yy_Code", SqlDbType.Char))
            ilist.Add(New SqlParameter("@Ck_Sl", SqlDbType.Decimal))
            ilist.Add(New SqlParameter("@Ck_Dj", SqlDbType.Decimal))
            ilist.Add(New SqlParameter("@Ck_Money", SqlDbType.Decimal))
            ilist.Add(New SqlParameter("@Ck_Memo", SqlDbType.VarChar))
            ilist.Add(New SqlParameter("@Ck_Code", SqlDbType.Char))
            ilist.Add(New SqlParameter("@Xx_Code", SqlDbType.VarChar))
            If Form_Lb = "药库药品批发" Then
                Insert_String = "Insert Into Yk_Pf2(Yy_Code,Ck_Sl,Ck_Dj,Ck_Money,Ck_Memo,Ck_Code,Xx_Code)Values(@Yy_Code,@Ck_Sl,@Ck_Dj,@Ck_Money,@Ck_Memo,@Ck_Code,@Xx_Code)"
                Update_String = "Update Yk_Pf2 Set Yy_Code=@Yy_Code,Ck_Sl=@Ck_Sl,Ck_Dj=@Ck_Dj,Ck_Money=@Ck_Money,Ck_Memo=@Ck_Memo Where Ck_Code=@Ck_Code and Xx_Code=@Xx_Code"
            ElseIf Form_Lb = "药房药品批发" Then
                Insert_String = "Insert Into Yf_Pf2(Yy_Code,Ck_Sl,Ck_Dj,Ck_Money,Ck_Memo,Ck_Code,Xx_Code)Values(@Yy_Code,@Ck_Sl,@Ck_Dj,@Ck_Money,@Ck_Memo,@Ck_Code,@Xx_Code)"
                Update_String = "Update Yf_Pf2 Set Yy_Code=@Yy_Code,Ck_Sl=@Ck_Sl,Ck_Dj=@Ck_Dj,Ck_Money=@Ck_Money,Ck_Memo=@Ck_Memo Where Ck_Code=@Ck_Code and Xx_Code=@Xx_Code"
            End If
        ElseIf Form_Lb = "药房退回药库" Then
            ilist.Add(New SqlParameter("@Yy_Code", SqlDbType.Char))
            ilist.Add(New SqlParameter("@Tk_Sl", SqlDbType.Decimal))
            ilist.Add(New SqlParameter("@Tk_Dj", SqlDbType.Decimal))
            ilist.Add(New SqlParameter("@Tk_Money", SqlDbType.Decimal))
            ilist.Add(New SqlParameter("@Tk_Memo", SqlDbType.VarChar))
            ilist.Add(New SqlParameter("@Tk_Code", SqlDbType.Char))
            ilist.Add(New SqlParameter("@Xx_Code", SqlDbType.VarChar))
            Insert_String = "Insert Into Yf_Tk2(Yy_Code,Tk_Sl,Tk_Dj,Tk_Money,Tk_Memo,Tk_Code,Xx_Code)Values(@Yy_Code,@Tk_Sl,@Tk_Dj,@Tk_Money,@Tk_Memo,@Tk_Code,@Xx_Code)"
            Update_String = "Update Yf_Tk2 Set Yy_Code=@Yy_Code,Tk_Sl=@Tk_Sl,Tk_Dj=@Tk_Dj,Tk_Money=@Tk_Money,Tk_Memo=@Tk_Memo Where Tk_Code=@Tk_Code and Xx_Code=@Xx_Code"
        End If
        '数据保存
        Try
            para = ilist.ToArray()
            If Rinsert = True Then
                If Para_Excute(para, Row, Insert_String) = "OK" Then
                    RZbtb.Rows.Add(Row)
                    Rtdbgrid.MoveLast()
                    Row.AcceptChanges()
                End If
               
                Call Data_Clear()
            Else
                Para_Excute(para, Row, Update_String)
                Row.AcceptChanges()
                MsgBox("数据修改成功,请继续其他操作!", MsgBoxStyle.Information, "提示")
                Me.Close()
            End If
        Catch ex As Exception
            Beep()
            MsgBox(ex.Message, MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
        End Try
    End Sub

    Private Function Para_Excute(ByVal V_Paras() As SqlClient.SqlParameter, ByVal V_Row As DataRow, ByVal V_Command_Str As String) As String
        For I = 0 To V_Paras.Length - 1
            V_Paras(I).Value = V_Row.Item(Mid(V_Paras(I).ParameterName, 2))
        Next
        Try

            HisVar.HisVar.Sqldal.ExecuteSql(V_Command_Str, V_Paras)
            Return "OK"
        Catch ex As Exception
            MsgBox(ex.Message.ToString, MsgBoxStyle.Information, "提示")
            Return ex.Message.ToString
        End Try
    End Function

#End Region

#Region "鼠标__外观"

    Private Sub P_Comm(ByVal Bt As Button)

        With Bt
            Select Case .Tag
                Case "保存"
                    .BackgroundImage = MyResources.C_Resources.getimage("命令_保存1")

                Case "取消"
                    .Location = New Point(Comm1.Left + Comm1.Width + 1, Comm1.Top)
                    .BackgroundImage = MyResources.C_Resources.getimage("命令_取消1")
                    .Width = Me.Comm1.Width
            End Select

            .Text = ""
            .FlatStyle = FlatStyle.Flat
            .FlatAppearance.BorderSize = 0
            .BackgroundImageLayout = ImageLayout.Center
        End With

    End Sub

    Private Sub Comm_MouseEnter(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Comm1.MouseEnter, Comm2.MouseEnter
        Select Case sender.tag
            Case "保存"
                Me.Comm1.BackgroundImage = MyResources.C_Resources.getimage("命令_保存2")
                Me.Comm1.Cursor = Cursors.Hand

            Case "取消"
                Me.Comm2.BackgroundImage = MyResources.C_Resources.getimage("命令_取消2")
                Me.Comm2.Cursor = Cursors.Hand

        End Select

    End Sub

    Private Sub Comm_MouseLeave(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Comm1.MouseLeave, Comm2.MouseLeave
        Select Case sender.tag

            Case "保存"
                Me.Comm1.BackgroundImage = MyResources.C_Resources.getimage("命令_保存1")
                Me.Comm1.Cursor = Cursors.Default

            Case "取消"
                Me.Comm2.BackgroundImage = MyResources.C_Resources.getimage("命令_取消1")
                Me.Comm2.Cursor = Cursors.Default

        End Select

    End Sub

    Private Sub Comm_MouseDown(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles Comm1.MouseDown, Comm2.MouseDown
        Select Case sender.tag
            Case "保存"
                Me.Comm1.BackgroundImage = MyResources.C_Resources.getimage("命令_保存3")

            Case "取消"
                Me.Comm2.BackgroundImage = MyResources.C_Resources.getimage("命令_取消3")
        End Select
    End Sub

    Private Sub Comm_MouseUp(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles Comm1.MouseUp, Comm2.MouseUp
        Select Case sender.tag
            Case "保存"
                Me.Comm1.BackgroundImage = MyResources.C_Resources.getimage("命令_保存2")

            Case "取消"
                Me.Comm2.BackgroundImage = MyResources.C_Resources.getimage("命令_取消2")
        End Select
    End Sub


#End Region

#Region "输入法设置"
    '中文
    Private Sub C1TextBox1_Enter(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles C1TextBox7.GotFocus
        InputLanguage.CurrentInputLanguage = HisVar.HisVar.InputCode
    End Sub

    Private Sub C1Numeric1_GotFocus(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles C1Combo1.GotFocus, C1Numeric2.GotFocus
        InputLanguage.CurrentInputLanguage = InputLanguage.InstalledInputLanguages(0)
    End Sub

#End Region

End Class