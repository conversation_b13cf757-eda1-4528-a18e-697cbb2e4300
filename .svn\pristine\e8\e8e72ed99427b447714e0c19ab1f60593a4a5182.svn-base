﻿<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<StiSerializer version="1.02" type="Net" application="StiReport">
  <Dictionary Ref="1" type="Dictionary" isKey="true">
    <BusinessObjects isList="true" count="0" />
    <Databases isList="true" count="0" />
    <DataSources isList="true" count="0" />
    <Relations isList="true" count="0" />
    <Report isRef="0" />
    <Variables isList="true" count="9">
      <value>,医院名称,医院名称,System.String,,False,False</value>
      <value>,姓名,姓名,System.String,,False,False</value>
      <value>,科别,科别,System.String,,False,False</value>
      <value>,打印时间,打印时间,System.String,,False,False</value>
      <value>,门诊编码,门诊编码,System.String,,False,False</value>
      <value>,经手人,经手人,System.String,,False,False</value>
      <value>,挂号费,挂号费,System.String,,False,False</value>
      <value>,诊查费,诊查费,System.String,,False,False</value>
      <value>,合计,合计,System.String,,False,False</value>
    </Variables>
  </Dictionary>
  <EngineVersion>EngineV2</EngineVersion>
  <GlobalizationStrings isList="true" count="0" />
  <MetaTags isList="true" count="0" />
  <Pages isList="true" count="1">
    <Page1 Ref="2" type="Page" isKey="true">
      <Border>None;Black;0;Solid;False;4;Black</Border>
      <Brush>Transparent</Brush>
      <Components isList="true" count="1">
        <ReportTitleBand1 Ref="3" type="ReportTitleBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,0.4,10.9,7.45</ClientRectangle>
          <Components isList="true" count="37">
            <Text1 Ref="4" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,5.1,1.1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,12,Regular,Point,False,134</Font>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text1</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{医院名称}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text1>
            <Text5 Ref="5" type="Text" isKey="true">
              <Border>Left;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,2.4,1.6,1.2</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>f0edda5a8f9749aba5b1eb4a6a827cde</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text5</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>姓    名:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text5>
            <Text35 Ref="6" type="Text" isKey="true">
              <Border>Top, Right;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>1.6,1.6,3.5,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>858177b73b064fa3abf6743d4ac44d6c</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text35</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{门诊编码}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text35>
            <Text36 Ref="7" type="Text" isKey="true">
              <Border>Right;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>1.6,2.4,3.5,1.2</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>c1b2048370484437949a390ac496199f</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text36</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{姓名}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text36>
            <Text80 Ref="8" type="Text" isKey="true">
              <Border>Right;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>1.6,5.4,3.5,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>984d2b720ace4a519467d236874cc13f</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text80</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{打印时间}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="9" type="CustomFormat" isKey="true">
                <StringFormat>yyyy-MM-dd </StringFormat>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text80>
            <Text4 Ref="10" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>5.6,0,4.9,1.1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,12,Regular,Point,False,134</Font>
              <Guid>fb25582ac74644b49f18779784b64226</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text4</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{医院名称}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text4>
            <Text6 Ref="11" type="Text" isKey="true">
              <Border>Left;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,5.4,1.6,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>4a4d04ee7ff84d5bae45d62379812144</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text6</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>日    期:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text6>
            <Text81 Ref="12" type="Text" isKey="true">
              <Border>Left, Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,6,1.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>17e039b6553a4be8be60a1b6e497eb9e</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text81</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>挂 号 员:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text81>
            <Text82 Ref="13" type="Text" isKey="true">
              <Border>Right, Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>1.6,6,3.5,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>5ff1452f8459461d937744eb934c04de</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text82</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{经手人}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text82>
            <Text3 Ref="14" type="Text" isKey="true">
              <Border>Left;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,3.6,1.6,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>0a277a5007b3462c80a0fa3728e83502</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text3</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>挂 号 费:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text3>
            <Text7 Ref="15" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>1.6,3.6,2.7,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>0c682775b35b480ca587236232b2c494</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text7</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{挂号费}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text7>
            <Text8 Ref="16" type="Text" isKey="true">
              <Border>Left;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,4.8,1.6,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>f1a0723f287b4f6b80e87dd46d183d46</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text8</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>合    计:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text8>
            <Text9 Ref="17" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>1.6,4.8,2.7,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>cb06cfa4598c44bc9a8f5cba9232381b</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text9</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{合计}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="18" type="CustomFormat" isKey="true">
                <StringFormat>#0.00</StringFormat>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text9>
            <Text10 Ref="19" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,1.1,5.1,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>2b53ca95055346bdb66acfb2e5cb1108</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text10</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>挂号票</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text10>
            <Text11 Ref="20" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>5.6,1.1,4.9,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>701f9066a20b43cb9b07ea72f90f8d98</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text11</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>挂号票</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text11>
            <Text2 Ref="21" type="Text" isKey="true">
              <Border>Top, Left;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,1.6,1.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text2</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>就诊编号:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text2>
            <Text12 Ref="22" type="Text" isKey="true">
              <Border>Left;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>5.6,2.4,1.4,1.2</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>1633ae8f653649fcbfbb09418b0033fe</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text12</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>姓  名:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text12>
            <Text13 Ref="23" type="Text" isKey="true">
              <Border>Top, Right;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>7,1.6,3.5,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>c96a2f31b2504eb18b70008bd965d71d</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text13</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{门诊编码}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text13>
            <Text14 Ref="24" type="Text" isKey="true">
              <Border>Right;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>7,2.4,3.5,1.2</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>09ada7052b324ca0893c9ba8c10263b6</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text14</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{姓名}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text14>
            <Text15 Ref="25" type="Text" isKey="true">
              <Border>Right;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>7,5.4,3.5,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>8ff93eff88a84324841719b12acc3f75</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text15</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{打印时间}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="26" type="CustomFormat" isKey="true">
                <StringFormat>yyyy-MM-dd</StringFormat>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text15>
            <Text16 Ref="27" type="Text" isKey="true">
              <Border>Left;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>5.6,5.4,1.4,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>9be3eea16c074fd7971ddeab4589f988</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text16</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>日  期:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text16>
            <Text17 Ref="28" type="Text" isKey="true">
              <Border>Left, Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>5.6,6,1.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>6c2a74be5afe4fabb239da2019ebec4b</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text17</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>挂号员:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text17>
            <Text18 Ref="29" type="Text" isKey="true">
              <Border>Right, Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>7,6,3.5,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>2571558c9c044106bc7523c1f182194d</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text18</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{经手人}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text18>
            <Text19 Ref="30" type="Text" isKey="true">
              <Border>Left;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>5.6,3.6,1.4,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>d42c3a1bb12a4ab6b6dc31e611bfd689</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text19</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>挂号费:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text19>
            <Text20 Ref="31" type="Text" isKey="true">
              <Border>Right;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>7,3.6,3.5,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>02294e69d7a54dd984a0a78d6cf784c2</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text20</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{挂号费}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text20>
            <Text21 Ref="32" type="Text" isKey="true">
              <Border>Left;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>5.6,4.8,1.4,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>7c3f7e5eb3e84adfbdf7d7e5820267f9</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text21</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>合  计:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text21>
            <Text22 Ref="33" type="Text" isKey="true">
              <Border>Right;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>7,4.8,3.5,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>4265192e7deb4f959ab61e79f2bdaf52</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text22</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{合计}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text22>
            <Text23 Ref="34" type="Text" isKey="true">
              <Border>Top, Left;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>5.6,1.6,1.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>56e80decc45c475f8bfa398cef91a137</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text23</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>编  号:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text23>
            <Text24 Ref="35" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,6.8,5.1,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>245d8c507347422f98a79f19a53bb3e4</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text24</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>凭此票据就诊交费(当日有效)</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text24>
            <Text25 Ref="36" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>5.6,6.8,4.9,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>205deeb13f764e30a399476fa582743e</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text25</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>此联医生保留</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text25>
            <Text26 Ref="37" type="Text" isKey="true">
              <Border>Right;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>4.3,3.6,0.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>90cea8891e004f08aff210c15149cbf3</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text26</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>元</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text26>
            <Text27 Ref="38" type="Text" isKey="true">
              <Border>Right;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>4.3,4.8,0.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>fe400eae45564d15a82e911350651372</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text27</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>元</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text27>
            <Text28 Ref="39" type="Text" isKey="true">
              <Border>Left;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,4.2,1.6,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>6c98cc344fe44abb83e51bd0cddc71aa</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text28</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>诊 查 费:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text28>
            <Text29 Ref="40" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>1.6,4.2,2.7,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>2146778eea08442bb055f6d454decebb</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text29</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{诊查费}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text29>
            <Text30 Ref="41" type="Text" isKey="true">
              <Border>Left;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>5.6,4.2,1.4,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>f94ef3c15f5642efbd82b9c414ee2db2</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text30</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>诊查费:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text30>
            <Text31 Ref="42" type="Text" isKey="true">
              <Border>Right;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>7,4.2,3.5,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>8e84214aef494cfc96d69e69d9625527</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text31</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{诊查费}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text31>
            <Text32 Ref="43" type="Text" isKey="true">
              <Border>Right;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>4.3,4.2,0.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>d8a06050d32a46d5806b99547f1e7480</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text32</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>元</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text32>
          </Components>
          <Conditions isList="true" count="0" />
          <Name>ReportTitleBand1</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
        </ReportTitleBand1>
      </Components>
      <Conditions isList="true" count="0" />
      <Guid>006e6db275c0490a978068679f5ede10</Guid>
      <LargeHeight>True</LargeHeight>
      <Margins>0,1.1,0,0</Margins>
      <Name>Page1</Name>
      <PageHeight>8</PageHeight>
      <PageWidth>12</PageWidth>
      <Report isRef="0" />
      <Watermark Ref="44" type="Stimulsoft.Report.Components.StiWatermark" isKey="true">
        <Font>Arial,100</Font>
        <TextBrush>[50:0:0:0]</TextBrush>
      </Watermark>
    </Page1>
  </Pages>
  <PrinterSettings Ref="45" type="Stimulsoft.Report.Print.StiPrinterSettings" isKey="true" />
  <ReferencedAssemblies isList="true" count="8">
    <value>System.Dll</value>
    <value>System.Drawing.Dll</value>
    <value>System.Windows.Forms.Dll</value>
    <value>System.Data.Dll</value>
    <value>System.Xml.Dll</value>
    <value>Stimulsoft.Controls.Dll</value>
    <value>Stimulsoft.Base.Dll</value>
    <value>Stimulsoft.Report.Dll</value>
  </ReferencedAssemblies>
  <ReportAlias>门诊挂号票据（181106)</ReportAlias>
  <ReportChanged>11/6/2018 10:45:40 AM</ReportChanged>
  <ReportCreated>2/16/2012 3:42:02 PM</ReportCreated>
  <ReportFile>.\Rpt\门诊挂号票据.mrt</ReportFile>
  <ReportGuid>e5fcca2872384b0699f4e977ea0bac26</ReportGuid>
  <ReportName>门诊挂号票据（181106)</ReportName>
  <ReportUnit>Centimeters</ReportUnit>
  <ReportVersion>2011.2.1026</ReportVersion>
  <Script>using System;
using System.Drawing;
using System.Windows.Forms;
using System.Data;
using Stimulsoft.Controls;
using Stimulsoft.Base.Drawing;
using Stimulsoft.Report;
using Stimulsoft.Report.Dialogs;
using Stimulsoft.Report.Components;

namespace Reports
{
    public class Report : Stimulsoft.Report.StiReport
    {
        public Report()        {
            this.InitializeComponent();
        }

        #region StiReport Designer generated code - do not modify
        #endregion StiReport Designer generated code - do not modify
    }
}
</Script>
  <ScriptLanguage>CSharp</ScriptLanguage>
  <Styles isList="true" count="0" />
</StiSerializer>