﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace ERX.Model
{
    /// <summary>
    /// 电子处方上传预核验
    /// </summary>
    public class MdluploadChkIn
    {
        public string mdtrtCertType { get; set; }   //就诊凭证类型
        public string mdtrtCertNo { get; set; }   //就诊凭证编号
        public string cardSn { get; set; }   //卡识别码
        public string ecToken { get; set; }   //电子凭证令牌
        public string authNo { get; set; }   //电子凭证线上身份核验流水号
        public string bizTypeCode { get; set; }   //业务类型代码
        public string insuPlcNo { get; set; }   //参保地编号 
        public string hospRxno { get; set; }   //定点医疗机构处方编号
        public string initRxno { get; set; }   //续方的原处方编号
        public string rxTypeCode { get; set; }   //处方类别代码
        public string prscTime { get; set; }   //开方时间
        public string rxDrugCnt { get; set; }   //药品类目数（剂数） 
        public string rxUsedWayCodg { get; set; }   //处方整剂用法编号
        public string rxUsedWayName { get; set; }   //处方整剂用法名称
        public string rxFrquCodg { get; set; }   //处方整剂频次编号
        public string rxFrquName { get; set; }   //处方整剂频次名称
        public string rxDosunt { get; set; }   //处方整剂剂量单位
        public string  rxDoscnt { get; set; }   //处方整剂单次剂量数 
        public string rxDrordDscr { get; set; }   //处方整剂医嘱说明
        public string valiDays { get; set; }   //处方有效天数
        public string  valiEndTime { get; set; }   //有效截止时间
        public string reptFlag { get; set; }   //复用（多次）使用标志 
        public string maxReptCnt { get; set; }   //最大使用次数 
        public string reptdCnt { get; set; }   //己使用次数
        public string minInrvDays { get; set; }   //使用最小间隔（天数） 
        public string rxCotnFlag { get; set; }   //续方标志 
        public string longRxFlag { get; set; }   //长期处方标志 
        public List<MdlrxdrugdetailIn> rxdrugdetail { get; set; }
        public List<MdldiseinfoIn> diseinfo { get; set; }

        public MdlmdtrtinfoIn mdtrtinfo { get; set; }


    }

    public class MdlrxdrugdetailIn
    {
        public string medListCodg { get; set; }   //医疗目录编码 
        public string fixmedinsHilistId { get; set; }   //v定点医药机构目录编号
        public string hospPrepFlag { get; set; }   //医疗机构制剂标志 
        public string rxItemTypeCode { get; set; }   //处方项目分类代码
        public string rxItemTypeName { get; set; }   //处方项目分类名称 
        public string tcmdrugTypeName { get; set; }   //中药类别名称
        public string tcmdrugTypeCode { get; set; }   //中药类别代码 
        public string tcmherbFoote { get; set; }   //草药脚注 
        public string mednTypeCode { get; set; }   //药物类型代码 
        public string mednTypeName { get; set; }   //药物类型
        public string mainMedcFlag { get; set; }   //主要用药标志
        public string urgtFlag { get; set; }   //加急标志
        public string basMednFlag { get; set; }   //基本药物标志
        public string impDrugFlag { get; set; }   //是否进口药品
        public string drugProdname { get; set; }   //药品商品名
        public string drugGenname { get; set; }   //药品通用名
        public string drugDosform { get; set; }   //药品剂型 
        public string drugSpec { get; set; }   //药品规格
        public string prdrName { get; set; }   //生厂厂家
        public string medcWayCodg { get; set; }   //用药途径代码
        public string medcWayDscr { get; set; }   //用药途径描述 
        public string medcBegntime { get; set; }   //用药开始时间
        public string medcEndtime { get; set; }   //用药结束时间 
        public string  medcDays { get; set; }   //用药天数
        public string drugPric { get; set; }   //药品单价
        public string drugSumamt { get; set; }   //药品总金额
        public string drugCnt { get; set; }   //药品发药总量（处方流转和取药时的药品医保结算使用的数量）
        public string drugDosunt { get; set; }   //药品发药单位（处方流转和取药时的药品医保结算使用的单位；即发药单位，如“片”或“盒”）
        public string  drugTotlcnt { get; set; }   //用药总量（按所需库存（包装）单位计算取整后的数量） 
        public string drugTotlcntEmp { get; set; }   //用药总量单位（即库存包装单位，如“盒”）
        public string  sinDoscnt { get; set; }   //单次用量
        public string sinDosunt { get; set; }   //单次剂量单位（即开方单位或剂量单位，如“mg”）
        public string usedFrquCodg { get; set; }   //使用频次编码
        public string usedFrquName { get; set; }   //使用频次名称 
        public string hospApprFlag { get; set; }   //医院审批标志 
        public string extras { get; set; }   //扩展数据
       

    }

    public class MdlmdtrtinfoIn
    {
        public string fixmedinsName { get; set; }   //定点医疗机构名称 
        public string fixmedinsCode { get; set; }   //定点医疗机构编号
        public string mdtrtId { get; set; }   //医保就诊ID
        public string medType { get; set; }   //医疗类别
        public string iptOtpNo { get; set; }   //住院/门诊号
        public string otpIptFlag { get; set; }   //门诊住院标识
        public string psnNo { get; set; }   //医保人员编号
        public string patnName { get; set; }   //患者姓名
        public string psnCertType { get; set; }   //人员证件类型
        public string certno { get; set; }   //证件号码 
        public string  patnAge { get; set; }   //年龄 
        public string patnHgt { get; set; }   //患者身高 
        public string patnWt { get; set; }   //患者体重 
        public string gend { get; set; }   //性别
        public string gesoVal { get; set; }   //妊娠(孕周) 
        public string nwbFlag { get; set; }   //新生儿标志 
        public string nwbAge { get; set; }   //新生儿日、月龄 
        public string suckPrdFlag { get; set; }   //哺乳期标志
        public string algsHis { get; set; }   //过敏史
        public string prscDeptName { get; set; }   //开方科室名称 
        public string prscDeptCode { get; set; }   //v开方科室编号
        public string drCode { get; set; }   //开方医保医师代码
        public string prscDrName { get; set; }   //开方医师姓名 
        public string prscDrCertType { get; set; }   //开方医师证件类型
        public string prscDrCertno { get; set; }   //开方医师证件号码 
        public string drProfttlCodg { get; set; }   //医生职称编码
        public string drProfttlName { get; set; }   //医生职称名称
        public string drDeptCode { get; set; }   //医生科室编码
        public string drDeptName { get; set; }   //医生科室名称
        public string mdtrtTime { get; set; }   //就诊时间 
        public string diseCodg { get; set; }   //病种编码 
        public string diseName { get; set; }   //v病种名称
        public string spDiseFlag { get; set; }   //v特殊病种标志
        public string maindiagCode { get; set; }   //主诊断代码
        public string maindiagName { get; set; }   //主诊断名称
        public string diseCondDscr { get; set; }   //疾病病情描述 
        public string hiFeesetlType { get; set; }   //v医保费用结算类型
        public string hiFeesetlName { get; set; }   //医保费用类别名称 
        public string  rgstFee { get; set; }   //挂号费 
        public string medfeeSumamt { get; set; }   //医疗费总额 
        public string fstdiagFlag { get; set; }   //是否初诊
        public object extras { get; set; }   //扩展数据 

    }

    public class MdldiseinfoIn
    {
        public string diagType { get; set; }   //诊断类别
        public string maindiagFlag { get; set; }   //主诊断标志 
        public string diagSrtNo { get; set; }   //诊断排序号 
        public string diagCode { get; set; }   //诊断代码
        public string diagName { get; set; }   //诊断名称
        public string diagDept { get; set; }   //v诊断科室
        public string diagDrNo { get; set; }   //诊断医生编码
        public string diagDrName { get; set; }   //诊断医生姓名
        public string  diagTime { get; set; }   //诊断时间 
        public string tcmDiseCode { get; set; }   //中医病名代码 
        public string tcmDiseName { get; set; }   //中医病名
        public string tcmsympCode { get; set; }   //中医症候代码 
        public string tcmsymp { get; set; }   //中医症候

    }
    public class MdluploadChkOut
    {
        public string rxTraceCode { get; set; }   //处方追溯码 
        public string hiRxno { get; set; }   //医保处方编号
    }
}
