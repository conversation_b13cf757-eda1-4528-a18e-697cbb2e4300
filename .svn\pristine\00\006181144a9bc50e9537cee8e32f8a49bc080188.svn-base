﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>9.0.30729</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{392EA0BC-9A6E-4FE5-B87E-591EEE67BF2F}</ProjectGuid>
    <OutputType>Library</OutputType>
    <StartupObject>
    </StartupObject>
    <RootNamespace>护士站</RootNamespace>
    <AssemblyName>护士站</AssemblyName>
    <FileAlignment>512</FileAlignment>
    <MyType>Windows</MyType>
    <TargetFrameworkVersion>v4.0</TargetFrameworkVersion>
    <OptionExplicit>On</OptionExplicit>
    <OptionCompare>Binary</OptionCompare>
    <OptionStrict>Off</OptionStrict>
    <OptionInfer>On</OptionInfer>
    <ManifestCertificateThumbprint>23DDE51CDA06346D628A9E872BF57B16F21DD06A</ManifestCertificateThumbprint>
    <ManifestKeyFile>MyKey.pfx</ManifestKeyFile>
    <GenerateManifests>true</GenerateManifests>
    <SignManifests>true</SignManifests>
    <IsWebBootstrapper>true</IsWebBootstrapper>
    <ApplicationIcon>
    </ApplicationIcon>
    <ApplicationManifest>My Project\app.manifest</ApplicationManifest>
    <FileUpgradeFlags>
    </FileUpgradeFlags>
    <UpgradeBackupLocation>
    </UpgradeBackupLocation>
    <OldToolsVersion>3.5</OldToolsVersion>
    <TargetFrameworkProfile>
    </TargetFrameworkProfile>
    <PublishUrl>C:\inetpub\wwwroot\Sq_His\</PublishUrl>
    <Install>true</Install>
    <InstallFrom>Web</InstallFrom>
    <UpdateEnabled>true</UpdateEnabled>
    <UpdateMode>Foreground</UpdateMode>
    <UpdateInterval>7</UpdateInterval>
    <UpdateIntervalUnits>Days</UpdateIntervalUnits>
    <UpdatePeriodically>false</UpdatePeriodically>
    <UpdateRequired>false</UpdateRequired>
    <MapFileExtensions>true</MapFileExtensions>
    <InstallUrl>http://10.120.240.60/Sq_His/</InstallUrl>
    <ProductName>医院管理系统</ProductName>
    <PublisherName>中软智通（唐山）科技有限公司</PublisherName>
    <WebPage>index.htm</WebPage>
    <OpenBrowserOnPublish>false</OpenBrowserOnPublish>
    <ApplicationRevision>101</ApplicationRevision>
    <ApplicationVersion>1.0.0.%2a</ApplicationVersion>
    <UseApplicationTrust>false</UseApplicationTrust>
    <BootstrapperEnabled>true</BootstrapperEnabled>
    <BootstrapperComponentsLocation>Relative</BootstrapperComponentsLocation>
    <BootstrapperComponentsUrl>http://192.168.69.104/Sq_His/</BootstrapperComponentsUrl>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <DefineDebug>true</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <OutputPath>..\output\</OutputPath>
    <DocumentationFile>护士站.xml</DocumentationFile>
    <NoWarn>42016,41999,42017,42018,42019,42032,42036,42020,42021,42022,42353,42354,42355</NoWarn>
    <CodeAnalysisRuleSet>AllRules.ruleset</CodeAnalysisRuleSet>
    <PlatformTarget>x86</PlatformTarget>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <DefineDebug>false</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DocumentationFile>护士站.xml</DocumentationFile>
    <NoWarn>42016,41999,42017,42018,42019,42032,42036,42020,42021,42022,42353,42354,42355</NoWarn>
    <CodeAnalysisRuleSet>AllRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="C1.Win.C1Command.4, Version=4.0.20163.212, Culture=neutral, PublicKeyToken=e808566f358766d8, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="C1.Win.C1FlexGrid.4, Version=4.0.20163.212, Culture=neutral, PublicKeyToken=79882d576c6336da, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="C1.Win.C1Input.4, Version=4.0.20163.212, Culture=neutral, PublicKeyToken=7e7ff60f0c214f9a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="C1.Win.C1List.4, Version=4.0.20163.212, Culture=neutral, PublicKeyToken=6b24f8f981dbd7bc, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="C1.Win.C1Ribbon.4, Version=4.0.20163.212, Culture=neutral, PublicKeyToken=79882d576c6336da, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="C1.Win.C1TrueDBGrid.4, Version=4.0.20163.212, Culture=neutral, PublicKeyToken=75ae3fb0e2b1e0da, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="CustomMarshalers" />
    <Reference Include="DCSoft.Writer, Version=1.2014.1217.1, Culture=neutral, PublicKeyToken=2e40e961ea876340, processorArchitecture=MSIL">
      <HintPath>..\Dll\DCSoft.Writer.dll</HintPath>
    </Reference>
    <Reference Include="iniOperate, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\Dll\iniOperate.dll</HintPath>
    </Reference>
    <Reference Include="SqlDal, Version=1.0.0.6, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\Dll\SqlDal.dll</HintPath>
    </Reference>
    <Reference Include="Stimulsoft.Base, Version=2011.2.1026.0, Culture=neutral, PublicKeyToken=ebe6666cba19647a, processorArchitecture=MSIL" />
    <Reference Include="Stimulsoft.Report, Version=2011.2.1026.0, Culture=neutral, PublicKeyToken=ebe6666cba19647a, processorArchitecture=MSIL" />
    <Reference Include="Stimulsoft.Report.Win, Version=2011.2.1026.0, Culture=neutral, PublicKeyToken=ebe6666cba19647a, processorArchitecture=MSIL" />
    <Reference Include="System" />
    <Reference Include="System.Data" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Xml.Linq" />
  </ItemGroup>
  <ItemGroup>
    <Import Include="Microsoft.VisualBasic" />
    <Import Include="System" />
    <Import Include="System.Collections" />
    <Import Include="System.Collections.Generic" />
    <Import Include="System.Data" />
    <Import Include="System.Drawing" />
    <Import Include="System.Diagnostics" />
    <Import Include="System.Windows.Forms" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="01.配床\Zd_Bcfp.Designer.vb">
      <DependentUpon>Zd_Bcfp.vb</DependentUpon>
    </Compile>
    <Compile Include="01.配床\Zd_Bcfp.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="02.押金\Xs_Zy_Yj11.designer.vb">
      <DependentUpon>Xs_Zy_Yj11.vb</DependentUpon>
    </Compile>
    <Compile Include="02.押金\Xs_Zy_Yj11.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="02.押金\Xs_Zy_Yj12.Designer.vb">
      <DependentUpon>Xs_Zy_Yj12.vb</DependentUpon>
    </Compile>
    <Compile Include="02.押金\Xs_Zy_Yj12.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="02.申请出院\Xs_Zy_Sqcy.designer.vb">
      <DependentUpon>Xs_Zy_Sqcy.vb</DependentUpon>
    </Compile>
    <Compile Include="02.申请出院\Xs_Zy_Sqcy.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="03.汇总领药\Hsz_Cqcf_Dr.Designer.vb">
      <DependentUpon>Hsz_Cqcf_Dr.vb</DependentUpon>
    </Compile>
    <Compile Include="03.汇总领药\Hsz_Cqcf_Dr.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="03.汇总领药\Hsz_Hzly.Designer.vb">
      <DependentUpon>Hsz_Hzly.vb</DependentUpon>
    </Compile>
    <Compile Include="03.汇总领药\Hsz_Hzly.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="04.汇总领药查询\Cx_Hzly.Designer.vb">
      <DependentUpon>Cx_Hzly.vb</DependentUpon>
    </Compile>
    <Compile Include="04.汇总领药查询\Cx_Hzly.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="05.输液卡\Hsz_Syk.designer.vb">
      <DependentUpon>Hsz_Syk.vb</DependentUpon>
    </Compile>
    <Compile Include="05.输液卡\Hsz_Syk.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="06.体温表\Temparature.Designer.vb">
      <DependentUpon>Temparature.vb</DependentUpon>
    </Compile>
    <Compile Include="06.体温表\Temparature.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="06.体温表\TemparatureChart.Designer.vb">
      <DependentUpon>TemparatureChart.vb</DependentUpon>
    </Compile>
    <Compile Include="06.体温表\TemparatureChart.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="07.护士站工作台\NurseStation.Designer.vb">
      <DependentUpon>NurseStation.vb</DependentUpon>
    </Compile>
    <Compile Include="07.护士站工作台\NurseStation.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="08.医生站工作台\EmrStation.Designer.vb">
      <DependentUpon>EmrStation.vb</DependentUpon>
    </Compile>
    <Compile Include="08.医生站工作台\EmrStation.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="My Project\AssemblyInfo.vb" />
    <Compile Include="My Project\Application.Designer.vb">
      <AutoGen>True</AutoGen>
      <DependentUpon>Application.myapp</DependentUpon>
    </Compile>
    <Compile Include="My Project\Resources.Designer.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Include="My Project\Settings.Designer.vb">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="01.配床\Zd_Bcfp.resx">
      <DependentUpon>Zd_Bcfp.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="02.押金\Xs_Zy_Yj11.resx">
      <DependentUpon>Xs_Zy_Yj11.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="02.押金\Xs_Zy_Yj12.resx">
      <DependentUpon>Xs_Zy_Yj12.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="02.申请出院\Xs_Zy_Sqcy.resx">
      <DependentUpon>Xs_Zy_Sqcy.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="03.汇总领药\Hsz_Cqcf_Dr.resx">
      <DependentUpon>Hsz_Cqcf_Dr.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="03.汇总领药\Hsz_Hzly.resx">
      <DependentUpon>Hsz_Hzly.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="04.汇总领药查询\Cx_Hzly.resx">
      <DependentUpon>Cx_Hzly.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="05.输液卡\Hsz_Syk.resx">
      <DependentUpon>Hsz_Syk.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="06.体温表\Temparature.resx">
      <DependentUpon>Temparature.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="06.体温表\TemparatureChart.resx">
      <DependentUpon>TemparatureChart.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="07.护士站工作台\NurseStation.resx">
      <DependentUpon>NurseStation.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="08.医生站工作台\EmrStation.resx">
      <DependentUpon>EmrStation.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="My Project\licenses.licx" />
    <EmbeddedResource Include="My Project\Resources.resx">
      <Generator>VbMyResourcesResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.vb</LastGenOutput>
      <CustomToolNamespace>My.Resources</CustomToolNamespace>
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <None Include="My Project\Application.myapp">
      <Generator>MyApplicationCodeGenerator</Generator>
      <LastGenOutput>Application.Designer.vb</LastGenOutput>
    </None>
    <None Include="My Project\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <CustomToolNamespace>My</CustomToolNamespace>
      <LastGenOutput>Settings.Designer.vb</LastGenOutput>
    </None>
  </ItemGroup>
  <ItemGroup>
    <None Include="My Project\app.manifest" />
  </ItemGroup>
  <ItemGroup>
    <PublishFile Include="ActiveReports.Chart">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="ActiveReports.Viewer3">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="ActiveReports3">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="Resources\Background.bmp">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Exclude</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>File</FileType>
    </PublishFile>
    <PublishFile Include="Resources\Check1.bmp">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Exclude</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>File</FileType>
    </PublishFile>
    <PublishFile Include="Resources\Check2.bmp">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Exclude</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>File</FileType>
    </PublishFile>
    <PublishFile Include="Resources\Chkoff95.bmp">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Exclude</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>File</FileType>
    </PublishFile>
    <PublishFile Include="Resources\Chkon95.bmp">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Exclude</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>File</FileType>
    </PublishFile>
    <PublishFile Include="Resources\EmbedLink.gif">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Exclude</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>File</FileType>
    </PublishFile>
    <PublishFile Include="Resources\newtop_6.jpg">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Exclude</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>File</FileType>
    </PublishFile>
    <PublishFile Include="Resources\top_2.jpg">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Exclude</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>File</FileType>
    </PublishFile>
    <PublishFile Include="Resources\top_21.jpg">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Exclude</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>File</FileType>
    </PublishFile>
    <PublishFile Include="Resources\top_3.jpg">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Exclude</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>File</FileType>
    </PublishFile>
    <PublishFile Include="Resources\top_31.jpg">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Exclude</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>File</FileType>
    </PublishFile>
    <PublishFile Include="Resources\top_pic1_1.jpg">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Exclude</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>File</FileType>
    </PublishFile>
    <PublishFile Include="Resources\TXT11.bmp">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Exclude</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>File</FileType>
    </PublishFile>
    <PublishFile Include="Resources\WIZMENU.BMP">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Exclude</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>File</FileType>
    </PublishFile>
    <PublishFile Include="Resources\Wl010401.bmp">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Exclude</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>File</FileType>
    </PublishFile>
    <PublishFile Include="Resources\WL020306.BMP">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Exclude</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>File</FileType>
    </PublishFile>
    <PublishFile Include="Resources\WL0203061.BMP">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Exclude</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>File</FileType>
    </PublishFile>
    <PublishFile Include="Resources\Z_Edit.BMP">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Exclude</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>File</FileType>
    </PublishFile>
    <PublishFile Include="Resources\Z_PROP.BMP">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Exclude</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>File</FileType>
    </PublishFile>
    <PublishFile Include="Resources\Z_REPORT.BMP">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Exclude</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>File</FileType>
    </PublishFile>
    <PublishFile Include="Resources\Z_SHOW.BMP">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Exclude</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>File</FileType>
    </PublishFile>
    <PublishFile Include="Resources\Z_编辑.BMP">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Exclude</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>File</FileType>
    </PublishFile>
    <PublishFile Include="Resources\全部价格修改1.bmp">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Exclude</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>File</FileType>
    </PublishFile>
    <PublishFile Include="Resources\全部价格修改2.bmp">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Exclude</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>File</FileType>
    </PublishFile>
    <PublishFile Include="Resources\全部价格修改3.bmp">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Exclude</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>File</FileType>
    </PublishFile>
    <PublishFile Include="Resources\删除.bmp">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Exclude</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>File</FileType>
    </PublishFile>
    <PublishFile Include="Resources\发送.bmp">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Exclude</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>File</FileType>
    </PublishFile>
    <PublishFile Include="Resources\命令_保存1.bmp">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Exclude</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>File</FileType>
    </PublishFile>
    <PublishFile Include="Resources\命令_保存2.bmp">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Exclude</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>File</FileType>
    </PublishFile>
    <PublishFile Include="Resources\命令_保存3.bmp">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Exclude</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>File</FileType>
    </PublishFile>
    <PublishFile Include="Resources\命令_删除.bmp">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Exclude</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>File</FileType>
    </PublishFile>
    <PublishFile Include="Resources\命令_发药1.bmp">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Exclude</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>File</FileType>
    </PublishFile>
    <PublishFile Include="Resources\命令_发药2.bmp">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Exclude</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>File</FileType>
    </PublishFile>
    <PublishFile Include="Resources\命令_发药3.bmp">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Exclude</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>File</FileType>
    </PublishFile>
    <PublishFile Include="Resources\命令_取消1.bmp">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Exclude</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>File</FileType>
    </PublishFile>
    <PublishFile Include="Resources\命令_取消2.bmp">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Exclude</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>File</FileType>
    </PublishFile>
    <PublishFile Include="Resources\命令_取消3.bmp">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Exclude</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>File</FileType>
    </PublishFile>
    <PublishFile Include="Resources\命令_增加.bmp">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Exclude</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>File</FileType>
    </PublishFile>
    <PublishFile Include="Resources\命令_接收1.jpg">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Exclude</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>File</FileType>
    </PublishFile>
    <PublishFile Include="Resources\命令_接收2.jpg">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Exclude</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>File</FileType>
    </PublishFile>
    <PublishFile Include="Resources\命令_接收3.jpg">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Exclude</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>File</FileType>
    </PublishFile>
    <PublishFile Include="Resources\命令_数据结算1.bmp">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Exclude</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>File</FileType>
    </PublishFile>
    <PublishFile Include="Resources\命令_数据结算2.bmp">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Exclude</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>File</FileType>
    </PublishFile>
    <PublishFile Include="Resources\命令_数据结算3.bmp">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Exclude</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>File</FileType>
    </PublishFile>
    <PublishFile Include="Resources\命令_更新.bmp">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Exclude</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>File</FileType>
    </PublishFile>
    <PublishFile Include="Resources\命令_查找1.bmp">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Exclude</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>File</FileType>
    </PublishFile>
    <PublishFile Include="Resources\命令_查找2.bmp">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Exclude</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>File</FileType>
    </PublishFile>
    <PublishFile Include="Resources\命令_查找3.bmp">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Exclude</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>File</FileType>
    </PublishFile>
    <PublishFile Include="Resources\命令_药库价格修改1.bmp">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Exclude</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>File</FileType>
    </PublishFile>
    <PublishFile Include="Resources\命令_药库价格修改2.bmp">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Exclude</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>File</FileType>
    </PublishFile>
    <PublishFile Include="Resources\命令_药库价格修改3.bmp">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Exclude</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>File</FileType>
    </PublishFile>
    <PublishFile Include="Resources\命令_药房价格修改1.bmp">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Exclude</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>File</FileType>
    </PublishFile>
    <PublishFile Include="Resources\命令_药房价格修改2.bmp">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Exclude</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>File</FileType>
    </PublishFile>
    <PublishFile Include="Resources\命令_药房价格修改3.bmp">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Exclude</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>File</FileType>
    </PublishFile>
    <PublishFile Include="Resources\命令_请求发药1.bmp">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Exclude</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>File</FileType>
    </PublishFile>
    <PublishFile Include="Resources\命令_请求发药2.bmp">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Exclude</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>File</FileType>
    </PublishFile>
    <PublishFile Include="Resources\命令_请求发药3.bmp">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Exclude</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>File</FileType>
    </PublishFile>
    <PublishFile Include="Resources\命令_退回1.bmp">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Exclude</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>File</FileType>
    </PublishFile>
    <PublishFile Include="Resources\命令_退回2.bmp">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Exclude</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>File</FileType>
    </PublishFile>
    <PublishFile Include="Resources\命令_退回3.bmp">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Exclude</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>File</FileType>
    </PublishFile>
    <PublishFile Include="Resources\增加.bmp">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Exclude</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>File</FileType>
    </PublishFile>
    <PublishFile Include="Resources\打印.bmp">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Exclude</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>File</FileType>
    </PublishFile>
    <PublishFile Include="Resources\打印1.bmp">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Exclude</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>File</FileType>
    </PublishFile>
    <PublishFile Include="Resources\打印2.bmp">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Exclude</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>File</FileType>
    </PublishFile>
    <PublishFile Include="Resources\打印3.bmp">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Exclude</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>File</FileType>
    </PublishFile>
    <PublishFile Include="Resources\打印参数.bmp">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Exclude</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>File</FileType>
    </PublishFile>
    <PublishFile Include="Resources\更新.bmp">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Exclude</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>File</FileType>
    </PublishFile>
    <PublishFile Include="Resources\未命名.bmp">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Exclude</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>File</FileType>
    </PublishFile>
    <PublishFile Include="Resources\查找.bmp">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Exclude</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>File</FileType>
    </PublishFile>
  </ItemGroup>
  <ItemGroup>
    <BootstrapperPackage Include="Microsoft.Net.Client.3.5">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1 Client Profile</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.2.0">
      <Visible>False</Visible>
      <ProductName>.NET Framework 2.0 %28x86%29</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.3.0">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.0 %28x86%29</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.3.5">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.3.5.SP1">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Windows.Installer.3.1">
      <Visible>False</Visible>
      <ProductName>Windows Installer 3.1</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\BaseClass\BaseClass.vbproj">
      <Project>{48964D0C-25C0-413C-A94A-C2246ADE46A1}</Project>
      <Name>BaseClass</Name>
    </ProjectReference>
    <ProjectReference Include="..\BaseFunc\BaseFunc.vbproj">
      <Project>{C7865854-F754-41AC-9912-829068BE5C2A}</Project>
      <Name>BaseFunc</Name>
    </ProjectReference>
    <ProjectReference Include="..\BLLOld\BLLOld.csproj">
      <Project>{42c23254-ba1f-4222-895b-276fc06bf59c}</Project>
      <Name>BLLOld</Name>
    </ProjectReference>
    <ProjectReference Include="..\BLL\BLL.csproj">
      <Project>{46b795c2-6efa-41e6-948e-66f92e591b6a}</Project>
      <Name>BLL</Name>
    </ProjectReference>
    <ProjectReference Include="..\Common.BaseForm\Common.BaseForm.csproj">
      <Project>{1DD7020C-8603-438A-8015-34702DABC229}</Project>
      <Name>Common.BaseForm</Name>
    </ProjectReference>
    <ProjectReference Include="..\CustomControl\CustomControl.csproj">
      <Project>{12BF4168-D60E-4A6C-85BF-926130EE6A6D}</Project>
      <Name>CustomControl</Name>
    </ProjectReference>
    <ProjectReference Include="..\HisControl\HisControl.vbproj">
      <Project>{EF778791-6E30-4A07-83B9-2D73FD08810A}</Project>
      <Name>HisControl</Name>
    </ProjectReference>
    <ProjectReference Include="..\HisPara\HisPara.vbproj">
      <Project>{3E790840-B7EB-4875-A334-D0386A5633CB}</Project>
      <Name>HisPara</Name>
    </ProjectReference>
    <ProjectReference Include="..\HisVar\HisVar.vbproj">
      <Project>{4DAD5E14-6224-46B2-886D-6D22CE3886BC}</Project>
      <Name>HisVar</Name>
    </ProjectReference>
    <ProjectReference Include="..\ModelOld\ModelOld.csproj">
      <Project>{1FAF80FE-D42E-4FEB-853A-B9846C1862AE}</Project>
      <Name>ModelOld</Name>
    </ProjectReference>
    <ProjectReference Include="..\Model\Model.csproj">
      <Project>{3fb6ea13-2c32-4d08-a426-c22224f72121}</Project>
      <Name>Model</Name>
    </ProjectReference>
    <ProjectReference Include="..\PublicForm\PublicForm.vbproj">
      <Project>{C2D40F84-CBD2-4BB9-92CF-C995FC2F4A25}</Project>
      <Name>PublicForm</Name>
    </ProjectReference>
    <ProjectReference Include="..\Resources\MyResources.vbproj">
      <Project>{52FE1A23-CE20-42DA-8AFD-1B47B2BBCCC7}</Project>
      <Name>MyResources</Name>
    </ProjectReference>
    <ProjectReference Include="..\ZtHis.Emr\ZtHis.Emr.vbproj">
      <Project>{D05310B8-9C6A-4961-8664-8133AE171F15}</Project>
      <Name>ZtHis.Emr</Name>
    </ProjectReference>
    <ProjectReference Include="..\ZTHisEnum\ZTHisEnum.csproj">
      <Project>{940cdbcc-e9a4-4771-be47-343404a40123}</Project>
      <Name>ZTHisEnum</Name>
    </ProjectReference>
    <ProjectReference Include="..\ZTHisInpatient\ZTHisInpatient.csproj">
      <Project>{d79bb10e-64c3-4865-bee0-2d72454f47df}</Project>
      <Name>ZTHisInpatient</Name>
    </ProjectReference>
    <ProjectReference Include="..\ZTHisLis\ZTHisLis.csproj">
      <Project>{e800477a-4ffe-4308-a238-44b853fb32fb}</Project>
      <Name>ZTHisLis</Name>
    </ProjectReference>
    <ProjectReference Include="..\ZTHisNurse\ZTHisNurse.csproj">
      <Project>{37857e11-1d23-4c1e-9180-cf588a3a0885}</Project>
      <Name>ZTHisNurse</Name>
    </ProjectReference>
    <ProjectReference Include="..\ZTHisOutpatient\ZTHisOutpatient.csproj">
      <Project>{0B858E15-ED2E-45D4-96C2-8A2FF0364FC5}</Project>
      <Name>ZTHisOutpatient</Name>
    </ProjectReference>
    <ProjectReference Include="..\ZTHisPara\ZTHisPara.csproj">
      <Project>{9ca37597-119c-4f39-8063-effc91c2b20d}</Project>
      <Name>ZTHisPara</Name>
    </ProjectReference>
    <ProjectReference Include="..\ZTHisPublicFunction\ZTHisPublicFunction.csproj">
      <Project>{484f5b0c-f19f-448d-b819-e183bfc2fd96}</Project>
      <Name>ZTHisPublicFunction</Name>
    </ProjectReference>
    <ProjectReference Include="..\ZTHisVar\ZTHisVar.csproj">
      <Project>{26bf0cc0-ae2a-459a-b41b-73f691f5299d}</Project>
      <Name>ZTHisVar</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <COMReference Include="AxNsoOfficeLib">
      <Guid>{B21A69CE-901E-42FF-8F96-39616D2F16D1}</Guid>
      <VersionMajor>1</VersionMajor>
      <VersionMinor>0</VersionMinor>
      <Lcid>0</Lcid>
      <WrapperTool>aximp</WrapperTool>
      <Isolated>False</Isolated>
    </COMReference>
    <COMReference Include="NsoOfficeLib">
      <Guid>{B21A69CE-901E-42FF-8F96-39616D2F16D1}</Guid>
      <VersionMajor>1</VersionMajor>
      <VersionMinor>0</VersionMinor>
      <Lcid>0</Lcid>
      <WrapperTool>tlbimp</WrapperTool>
      <Isolated>False</Isolated>
      <EmbedInteropTypes>True</EmbedInteropTypes>
    </COMReference>
  </ItemGroup>
  <ItemGroup>
    <WCFMetadata Include="Connected Services\" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.VisualBasic.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>