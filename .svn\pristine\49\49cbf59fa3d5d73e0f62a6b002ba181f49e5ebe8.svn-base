﻿/**  版本信息模板在安装目录下，可自行修改。
* D_Materials_Buy_In2.cs
*
* 功 能： N/A
* 类 名： D_Materials_Buy_In2
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2016/11/29 13:55:42   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
using System.Collections.Generic;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using ModelOld;

namespace DAL
{
	/// <summary>
	/// 数据访问类:D_Materials_Buy_In2
	/// </summary>
	public partial class D_Materials_Buy_In2
	{
		public D_Materials_Buy_In2()
		{}
		#region  BasicMethod

		/// <summary>
		/// 是否存在该记录
		/// </summary>
		public bool Exists(string M_Buy_Detail_Code)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) from Materials_Buy_In2");
			strSql.Append(" where M_Buy_Detail_Code=@M_Buy_Detail_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@M_Buy_Detail_Code", SqlDbType.Char,15)			};
			parameters[0].Value = M_Buy_Detail_Code;

			return HisVar.HisVar.Sqldal.Exists(strSql.ToString(),parameters);
		}
        /// <summary>
        /// 最大编码
        /// </summary>
        public string MaxCode(string M_Buy_Code)
        {
            string max = M_Buy_Code + (string)(HisVar.HisVar.Sqldal.F_MaxCode("select max(substring(M_Buy_Detail_Code,12,4)) from Materials_Buy_In2 where M_Buy_Code='" + M_Buy_Code + "'", 4));
            return max;
        }

		/// <summary>
		/// 增加一条数据
		/// </summary>
		public bool Add(ModelOld.M_Materials_Buy_In2 model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("insert into Materials_Buy_In2(");
            strSql.Append("M_Buy_Code,Materials_Code,MaterialsStock_Code,M_Buy_Detail_Code,MaterialsLot,MaterialsExpiryDate,M_Buy_Num,M_Buy_WriteoffNo,M_Buy_RealNo,M_BuyIn_Num,M_BuyIn_WriteoffNo,M_BuyIn_RealNo,M_Buy_Price,M_BuyIn_Price,M_Buy_Money,M_Buy_RealMoney,Pack_Unit,Convert_Ratio,Bulk_Unit,M_BuyDetail_Memo)");
			strSql.Append(" values (");
            strSql.Append("@M_Buy_Code,@Materials_Code,@MaterialsStock_Code,@M_Buy_Detail_Code,@MaterialsLot,@MaterialsExpiryDate,@M_Buy_Num,@M_Buy_WriteoffNo,@M_Buy_RealNo,@M_BuyIn_Num,@M_BuyIn_WriteoffNo,@M_BuyIn_RealNo,@M_Buy_Price,@M_BuyIn_Price,@M_Buy_Money,@M_Buy_RealMoney,@Pack_Unit,@Convert_Ratio,@Bulk_Unit,@M_BuyDetail_Memo)");
			SqlParameter[] parameters = {
					new SqlParameter("@M_Buy_Code", SqlDbType.Char,11),
					new SqlParameter("@Materials_Code", SqlDbType.Char,10),
					new SqlParameter("@MaterialsStock_Code", SqlDbType.Char,16),
					new SqlParameter("@M_Buy_Detail_Code", SqlDbType.Char,15),
					new SqlParameter("@MaterialsLot", SqlDbType.VarChar,50),
					new SqlParameter("@MaterialsExpiryDate", SqlDbType.SmallDateTime),
					new SqlParameter("@M_Buy_Num", SqlDbType.Decimal,10),
					new SqlParameter("@M_Buy_WriteoffNo", SqlDbType.Decimal,10),
					new SqlParameter("@M_Buy_RealNo", SqlDbType.Decimal,10),
					new SqlParameter("@M_BuyIn_Num", SqlDbType.Decimal,10),
					new SqlParameter("@M_BuyIn_WriteoffNo", SqlDbType.Decimal,10),
					new SqlParameter("@M_BuyIn_RealNo", SqlDbType.Decimal,10),
					new SqlParameter("@M_Buy_Price", SqlDbType.Decimal,10),
                    new SqlParameter("@M_BuyIn_Price", SqlDbType.Decimal,10),
					new SqlParameter("@M_Buy_Money", SqlDbType.Decimal,10),
					new SqlParameter("@M_Buy_RealMoney", SqlDbType.Decimal,10),
					new SqlParameter("@Pack_Unit", SqlDbType.VarChar,10),
					new SqlParameter("@Convert_Ratio", SqlDbType.Int,4),
					new SqlParameter("@Bulk_Unit", SqlDbType.VarChar,10),
					new SqlParameter("@M_BuyDetail_Memo", SqlDbType.VarChar,200)};
			parameters[0].Value = model.M_Buy_Code;
			parameters[1].Value = model.Materials_Code;
			parameters[2].Value = Common.Tools.IsValueNull(model.MaterialsStock_Code);
			parameters[3].Value = model.M_Buy_Detail_Code;
			parameters[4].Value = model.MaterialsLot;
			parameters[5].Value = model.MaterialsExpiryDate;
			parameters[6].Value = model.M_Buy_Num;
			parameters[7].Value = model.M_Buy_WriteoffNo;
			parameters[8].Value = model.M_Buy_RealNo;
			parameters[9].Value = model.M_BuyIn_Num;
			parameters[10].Value = model.M_BuyIn_WriteoffNo;
			parameters[11].Value = model.M_BuyIn_RealNo;
			parameters[12].Value = model.M_Buy_Price;
            parameters[13].Value = model.M_BuyIn_Price;
			parameters[14].Value = model.M_Buy_Money;
			parameters[15].Value = model.M_Buy_RealMoney;
			parameters[16].Value = model.Pack_Unit;
			parameters[17].Value = model.Convert_Ratio;
			parameters[18].Value = model.Bulk_Unit;
			parameters[19].Value = model.M_BuyDetail_Memo;

			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 更新一条数据
		/// </summary>
		public bool Update(ModelOld.M_Materials_Buy_In2 model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("update Materials_Buy_In2 set ");
			strSql.Append("M_Buy_Code=@M_Buy_Code,");
			strSql.Append("Materials_Code=@Materials_Code,");
			strSql.Append("MaterialsStock_Code=@MaterialsStock_Code,");
			strSql.Append("MaterialsLot=@MaterialsLot,");
			strSql.Append("MaterialsExpiryDate=@MaterialsExpiryDate,");
			strSql.Append("M_Buy_Num=@M_Buy_Num,");
			strSql.Append("M_Buy_WriteoffNo=@M_Buy_WriteoffNo,");
			strSql.Append("M_Buy_RealNo=@M_Buy_RealNo,");
			strSql.Append("M_BuyIn_Num=@M_BuyIn_Num,");
			strSql.Append("M_BuyIn_WriteoffNo=@M_BuyIn_WriteoffNo,");
			strSql.Append("M_BuyIn_RealNo=@M_BuyIn_RealNo,");
			strSql.Append("M_Buy_Price=@M_Buy_Price,");
            strSql.Append("M_BuyIn_Price=@M_BuyIn_Price,");
			strSql.Append("M_Buy_Money=@M_Buy_Money,");
			strSql.Append("M_Buy_RealMoney=@M_Buy_RealMoney,");
			strSql.Append("Pack_Unit=@Pack_Unit,");
			strSql.Append("Convert_Ratio=@Convert_Ratio,");
			strSql.Append("Bulk_Unit=@Bulk_Unit,");
			strSql.Append("M_BuyDetail_Memo=@M_BuyDetail_Memo");
			strSql.Append(" where M_Buy_Detail_Code=@M_Buy_Detail_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@M_Buy_Code", SqlDbType.Char,11),
					new SqlParameter("@Materials_Code", SqlDbType.Char,10),
					new SqlParameter("@MaterialsStock_Code", SqlDbType.Char,16),
					new SqlParameter("@MaterialsLot", SqlDbType.VarChar,50),
					new SqlParameter("@MaterialsExpiryDate", SqlDbType.SmallDateTime),
					new SqlParameter("@M_Buy_Num", SqlDbType.Decimal,10),
					new SqlParameter("@M_Buy_WriteoffNo", SqlDbType.Decimal,10),
					new SqlParameter("@M_Buy_RealNo", SqlDbType.Decimal,10),
					new SqlParameter("@M_BuyIn_Num", SqlDbType.Decimal,10),
					new SqlParameter("@M_BuyIn_WriteoffNo", SqlDbType.Decimal,10),
					new SqlParameter("@M_BuyIn_RealNo", SqlDbType.Decimal,10),
					new SqlParameter("@M_Buy_Price", SqlDbType.Decimal,10),
                    new SqlParameter("@M_BuyIn_Price", SqlDbType.Decimal,10),
					new SqlParameter("@M_Buy_Money", SqlDbType.Decimal,10),
					new SqlParameter("@M_Buy_RealMoney", SqlDbType.Decimal,10),
					new SqlParameter("@Pack_Unit", SqlDbType.VarChar,10),
					new SqlParameter("@Convert_Ratio", SqlDbType.Int,4),
					new SqlParameter("@Bulk_Unit", SqlDbType.VarChar,10),
					new SqlParameter("@M_BuyDetail_Memo", SqlDbType.VarChar,200),
					new SqlParameter("@M_Buy_Detail_Code", SqlDbType.Char,15)};
			parameters[0].Value = model.M_Buy_Code;
			parameters[1].Value = model.Materials_Code;
            parameters[2].Value = Common.Tools.IsValueNull(model.MaterialsStock_Code);
			parameters[3].Value = model.MaterialsLot;
			parameters[4].Value = model.MaterialsExpiryDate;
			parameters[5].Value = model.M_Buy_Num;
			parameters[6].Value = model.M_Buy_WriteoffNo;
			parameters[7].Value = model.M_Buy_RealNo;
			parameters[8].Value = model.M_BuyIn_Num;
			parameters[9].Value = model.M_BuyIn_WriteoffNo;
			parameters[10].Value = model.M_BuyIn_RealNo;
			parameters[11].Value = model.M_Buy_Price;
            parameters[12].Value = model.M_BuyIn_Price;
            parameters[13].Value = model.M_Buy_Money;
            parameters[14].Value = model.M_Buy_RealMoney;
            parameters[15].Value = model.Pack_Unit;
            parameters[16].Value = model.Convert_Ratio;
            parameters[17].Value = model.Bulk_Unit;
            parameters[18].Value = model.M_BuyDetail_Memo;
            parameters[19].Value = model.M_Buy_Detail_Code;

			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
       
		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool Delete(string M_Buy_Detail_Code)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from Materials_Buy_In2 ");
			strSql.Append(" where M_Buy_Detail_Code=@M_Buy_Detail_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@M_Buy_Detail_Code", SqlDbType.Char,15)			};
			parameters[0].Value = M_Buy_Detail_Code;

			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 批量删除数据
		/// </summary>
		public bool DeleteList(string M_Buy_Detail_Codelist )
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from Materials_Buy_In2 ");
			strSql.Append(" where M_Buy_Detail_Code in ("+M_Buy_Detail_Codelist + ")  ");
			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString());
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}


        /// <summary>
        /// 得到一个对象实体集
        /// </summary>
        public List<ModelOld.M_Materials_Buy_In2> GetModelList(string M_Buy_Code)
        {
            List<ModelOld.M_Materials_Buy_In2> modelList = new List<M_Materials_Buy_In2>();
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select M_Buy_Code,Materials_Code,MaterialsStock_Code,M_Buy_Detail_Code,MaterialsLot,MaterialsExpiryDate,M_Buy_Num,M_Buy_WriteoffNo,M_Buy_RealNo,M_BuyIn_Num,M_BuyIn_WriteoffNo,M_BuyIn_RealNo,M_Buy_Price, M_BuyIn_Price,M_Buy_Money,M_Buy_RealMoney,Pack_Unit,Convert_Ratio,Bulk_Unit,M_BuyDetail_Memo from Materials_Buy_In2 ");
            strSql.Append(" where M_Buy_Code=@M_Buy_Code ");
            SqlParameter[] parameters = {
					new SqlParameter("@M_Buy_Code", SqlDbType.Char,11)			};
            parameters[0].Value = M_Buy_Code;

           
            DataSet ds = HisVar.HisVar.Sqldal.Query(strSql.ToString(), parameters);

            if (ds.Tables[0].Rows.Count > 0)
            {
                foreach (DataRow _row in ds.Tables[0].Rows)
                {
                    modelList.Add(DataRowToModel(_row));
                }
                return modelList;
            }
            else
            {
                return null;
            }
        }

		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public ModelOld.M_Materials_Buy_In2 GetModel(string M_Buy_Detail_Code)
		{
			
			StringBuilder strSql=new StringBuilder();
            strSql.Append("select  top 1 M_Buy_Code,Materials_Code,MaterialsStock_Code,M_Buy_Detail_Code,MaterialsLot,MaterialsExpiryDate,M_Buy_Num,M_Buy_WriteoffNo,M_Buy_RealNo,M_BuyIn_Num,M_BuyIn_WriteoffNo,M_BuyIn_RealNo,M_Buy_Price, M_BuyIn_Price,M_Buy_Money,M_Buy_RealMoney,Pack_Unit,Convert_Ratio,Bulk_Unit,M_BuyDetail_Memo from Materials_Buy_In2 ");
			strSql.Append(" where M_Buy_Detail_Code=@M_Buy_Detail_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@M_Buy_Detail_Code", SqlDbType.Char,15)			};
			parameters[0].Value = M_Buy_Detail_Code;

			ModelOld.M_Materials_Buy_In2 model=new ModelOld.M_Materials_Buy_In2();
			DataSet ds=HisVar.HisVar.Sqldal.Query(strSql.ToString(),parameters);
			if(ds.Tables[0].Rows.Count>0)
			{
				return DataRowToModel(ds.Tables[0].Rows[0]);
			}
			else
			{
				return null;
			}
		}
        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ModelOld.M_Materials_Buy_In2 GetModelwhere(string str)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  top 1 M_Buy_Code,Materials_Code,MaterialsStock_Code,M_Buy_Detail_Code,MaterialsLot,MaterialsExpiryDate,M_Buy_Num,M_Buy_WriteoffNo,M_Buy_RealNo,M_BuyIn_Num,M_BuyIn_WriteoffNo,M_BuyIn_RealNo,M_Buy_Price,M_BuyIn_Price,M_Buy_Money,M_Buy_RealMoney,Pack_Unit,Convert_Ratio,Bulk_Unit,M_BuyDetail_Memo from Materials_Buy_In2 ");
            if (str.Trim() != "")
            {
                strSql.Append(" where " + str);
            }
            ModelOld.M_Materials_Buy_In2 model = new ModelOld.M_Materials_Buy_In2();
            DataSet ds = HisVar.HisVar.Sqldal.Query(strSql.ToString());
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public ModelOld.M_Materials_Buy_In2 DataRowToModel(DataRow row)
		{
			ModelOld.M_Materials_Buy_In2 model=new ModelOld.M_Materials_Buy_In2();
			if (row != null)
			{
				if(row["M_Buy_Code"]!=null)
				{
					model.M_Buy_Code=row["M_Buy_Code"].ToString();
				}
				if(row["Materials_Code"]!=null)
				{
					model.Materials_Code=row["Materials_Code"].ToString();
				}
				if(row["MaterialsStock_Code"]!=DBNull.Value)
				{
					model.MaterialsStock_Code=row["MaterialsStock_Code"].ToString();
				}
				if(row["M_Buy_Detail_Code"]!=null)
				{
					model.M_Buy_Detail_Code=row["M_Buy_Detail_Code"].ToString();
				}
				if(row["MaterialsLot"]!=null)
				{
					model.MaterialsLot=row["MaterialsLot"].ToString();
				}
				if(row["MaterialsExpiryDate"]!=null && row["MaterialsExpiryDate"].ToString()!="")
				{
					model.MaterialsExpiryDate=DateTime.Parse(row["MaterialsExpiryDate"].ToString());
				}
				if(row["M_Buy_Num"]!=null && row["M_Buy_Num"].ToString()!="")
				{
					model.M_Buy_Num=decimal.Parse(row["M_Buy_Num"].ToString());
				}
				if(row["M_Buy_WriteoffNo"]!=null && row["M_Buy_WriteoffNo"].ToString()!="")
				{
					model.M_Buy_WriteoffNo=decimal.Parse(row["M_Buy_WriteoffNo"].ToString());
				}
				if(row["M_Buy_RealNo"]!=null && row["M_Buy_RealNo"].ToString()!="")
				{
					model.M_Buy_RealNo=decimal.Parse(row["M_Buy_RealNo"].ToString());
				}
				if(row["M_BuyIn_Num"]!=null && row["M_BuyIn_Num"].ToString()!="")
				{
					model.M_BuyIn_Num=decimal.Parse(row["M_BuyIn_Num"].ToString());
				}
				if(row["M_BuyIn_WriteoffNo"]!=null && row["M_BuyIn_WriteoffNo"].ToString()!="")
				{
					model.M_BuyIn_WriteoffNo=decimal.Parse(row["M_BuyIn_WriteoffNo"].ToString());
				}
				if(row["M_BuyIn_RealNo"]!=null && row["M_BuyIn_RealNo"].ToString()!="")
				{
					model.M_BuyIn_RealNo=decimal.Parse(row["M_BuyIn_RealNo"].ToString());
				}
				if(row["M_Buy_Price"]!=null && row["M_Buy_Price"].ToString()!="")
				{
					model.M_Buy_Price=decimal.Parse(row["M_Buy_Price"].ToString());
				}
                if (row["M_BuyIn_Price"] != null && row["M_BuyIn_Price"].ToString() != "")
                {
                    model.M_BuyIn_Price = decimal.Parse(row["M_BuyIn_Price"].ToString());
                }
				if(row["M_Buy_Money"]!=null && row["M_Buy_Money"].ToString()!="")
				{
					model.M_Buy_Money=decimal.Parse(row["M_Buy_Money"].ToString());
				}
				if(row["M_Buy_RealMoney"]!=null && row["M_Buy_RealMoney"].ToString()!="")
				{
					model.M_Buy_RealMoney=decimal.Parse(row["M_Buy_RealMoney"].ToString());
				}
				if(row["Pack_Unit"]!=null)
				{
					model.Pack_Unit=row["Pack_Unit"].ToString();
				}
				if(row["Convert_Ratio"]!=null && row["Convert_Ratio"].ToString()!="")
				{
					model.Convert_Ratio=int.Parse(row["Convert_Ratio"].ToString());
				}
				if(row["Bulk_Unit"]!=null)
				{
					model.Bulk_Unit=row["Bulk_Unit"].ToString();
				}
				if(row["M_BuyDetail_Memo"]!=null)
				{
					model.M_BuyDetail_Memo=row["M_BuyDetail_Memo"].ToString();
				}
			}
			return model;
		}

		/// <summary>
		/// 获得数据列表
		/// </summary>
        public DataSet GetList(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select Materials_Buy_In1.M_Buy_Code,MaterialsSup_Code,MaterialsWh_Code,Order_Date,Arrival_Date,Input_Date,Finish_Date,TotalMoney,M_Buy_Memo,OrdersStatus,WriteOff_Code,WriteOffStatus,Materials_Dict.Materials_Code,MaterialsStock_Code,MateManu_Name ,M_Buy_Detail_Code,MaterialsLot,MaterialsExpiryDate,M_Buy_Num,M_Buy_WriteoffNo,M_Buy_RealNo as M_Buy_Num_old,M_Buy_RealNo,M_BuyIn_Num,M_BuyIn_WriteoffNo,M_BuyIn_RealNo,M_Buy_Price,M_BuyIn_Price,M_Buy_Money,M_Buy_RealMoney,Materials_Buy_In2.Pack_Unit,Materials_Buy_In2.Convert_Ratio,Materials_Buy_In2.Bulk_Unit,M_BuyDetail_Memo,Materials_Name,Materials_Spec ");
            strSql.Append(" from Materials_Buy_In1,Materials_Buy_In2 ,Materials_Dict  where Materials_Buy_In2.Materials_Code =Materials_Dict.Materials_Code and Materials_Buy_In1.M_Buy_Code=Materials_Buy_In2.M_Buy_Code");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" And " + strWhere);
            }
            return HisVar.HisVar.Sqldal.Query(strSql.ToString());
        }

        public DataSet GetWriteOffList(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT  Materials_Buy_In2.M_Buy_Code , ");
            strSql.Append("        Materials_Buy_In2.Materials_Code , ");
            strSql.Append("        Materials_Name , ");
            strSql.Append("        Materials_Buy_In2.MaterialsStock_Code , ");
            strSql.Append("        Materials_Buy_In2.M_Buy_Detail_Code , ");
            strSql.Append("        Materials_Spec, ");
            strSql.Append("        Materials_Buy_In2.MaterialsLot , ");
            strSql.Append("        Materials_Buy_In2.MaterialsExpiryDate , ");
            strSql.Append("        Materials_Buy_In2.Pack_Unit , ");
            strSql.Append("        Materials_Buy_In2.Bulk_Unit , ");
            strSql.Append("        Materials_Buy_In2.Convert_Ratio , ");
            strSql.Append("        b.M_Buy_RealNo as Can_RealWriteOffNo, ");
            strSql.Append("        Materials_Buy_In2.M_Buy_Num , ");
            strSql.Append("        Materials_Buy_In2.M_Buy_WriteoffNo , ");
            strSql.Append("        Materials_Buy_In2.M_Buy_RealNo , ");
            strSql.Append("        Materials_Buy_In2.M_BuyIn_Num , ");
            strSql.Append("        Materials_Buy_In2.M_BuyIn_WriteoffNo , ");
            strSql.Append("        Materials_Buy_In2.M_BuyIn_RealNo , ");
            strSql.Append("        Materials_Buy_In2.M_Buy_Price , ");
            strSql.Append("        Materials_Buy_In2.M_BuyIn_Price , ");
            strSql.Append("        Materials_Buy_In2.M_Buy_Money , ");
            strSql.Append("        Materials_Buy_In2.M_Buy_RealMoney , ");
            strSql.Append("        Materials_Buy_In2.M_BuyDetail_Memo  ");
            strSql.Append("FROM    Materials_Buy_In2 ");
            strSql.Append("        JOIN Materials_Dict ON Materials_Buy_In2.Materials_Code = Materials_Dict.Materials_Code ");
            strSql.Append("		,Materials_Buy_In1,Materials_Buy_In2 b ");
            strSql.Append("WHERE Materials_Buy_In2.M_Buy_Code=Materials_Buy_In1.M_Buy_Code AND WriteOff_Code=b.M_Buy_Code ");
            strSql.Append("AND Materials_Buy_In2.MaterialsStock_Code=b.MaterialsStock_Code ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" and " + strWhere);
            }
            return HisVar.HisVar.Sqldal.Query(strSql.ToString());
        }

		/// <summary>
		/// 获得前几行数据
		/// </summary>
		public DataSet GetList(int Top,string strWhere,string filedOrder)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select ");
			if(Top>0)
			{
				strSql.Append(" top "+Top.ToString());
			}
            strSql.Append(" M_Buy_Code,Materials_Code,MaterialsStock_Code,M_Buy_Detail_Code,MaterialsLot,MaterialsExpiryDate,M_Buy_Num,M_Buy_WriteoffNo,M_Buy_RealNo,M_BuyIn_Num,M_BuyIn_WriteoffNo,M_BuyIn_RealNo,M_Buy_Price,M_BuyIn_Price,M_Buy_Money,M_Buy_RealMoney,Pack_Unit,Convert_Ratio,Bulk_Unit,M_BuyDetail_Memo ");
			strSql.Append(" FROM Materials_Buy_In2 ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			strSql.Append(" order by " + filedOrder);
			return HisVar.HisVar.Sqldal.Query(strSql.ToString());
		}

		/// <summary>
		/// 获取记录总数
		/// </summary>
		public int GetRecordCount(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) FROM Materials_Buy_In2 ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			object obj = HisVar.HisVar.Sqldal.GetSingle(strSql.ToString());
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("SELECT * FROM ( ");
			strSql.Append(" SELECT ROW_NUMBER() OVER (");
			if (!string.IsNullOrEmpty(orderby.Trim()))
			{
				strSql.Append("order by T." + orderby );
			}
			else
			{
				strSql.Append("order by T.M_Buy_Detail_Code desc");
			}
			strSql.Append(")AS Row, T.*  from Materials_Buy_In2 T ");
			if (!string.IsNullOrEmpty(strWhere.Trim()))
			{
				strSql.Append(" WHERE " + strWhere);
			}
			strSql.Append(" ) TT");
			strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
			return HisVar.HisVar.Sqldal.Query(strSql.ToString());
		}

		/*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "Materials_Buy_In2";
			parameters[1].Value = "M_Buy_Detail_Code";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return HisVar.HisVar.Sqldal.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

		#endregion  BasicMethod
		#region  ExtensionMethod

		#endregion  ExtensionMethod
	}
}

