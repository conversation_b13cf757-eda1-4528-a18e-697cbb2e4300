﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class EmrMb3
    Inherits HisControl.BaseChild

    'Form 重写 Dispose，以清理组件列表。
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Windows 窗体设计器所必需的
    Private components As System.ComponentModel.IContainer

    '注意: 以下过程是 Windows 窗体设计器所必需的
    '可以使用 Windows 窗体设计器修改它。
    '不要使用代码编辑器修改它。
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(EmrMb3))
        Me.C1Holder1 = New C1.Win.C1Command.C1CommandHolder()
        Me.Move1 = New C1.Win.C1Command.C1Command()
        Me.Move2 = New C1.Win.C1Command.C1Command()
        Me.Move3 = New C1.Win.C1Command.C1Command()
        Me.Move4 = New C1.Win.C1Command.C1Command()
        Me.Move5 = New C1.Win.C1Command.C1Command()
        Me.Control1 = New C1.Win.C1Command.C1CommandControl()
        Me.T_Label1 = New C1.Win.C1Input.C1Label()
        Me.Control2 = New C1.Win.C1Command.C1CommandControl()
        Me.T_Label2 = New C1.Win.C1Input.C1Label()
        Me.Control4 = New C1.Win.C1Command.C1Command()
        Me.Control3 = New C1.Win.C1Command.C1CommandControl()
        Me.T_Label3 = New C1.Win.C1Input.C1Label()
        Me.ToolTip1 = New System.Windows.Forms.ToolTip(Me.components)
        Me.TableLayoutPanel1 = New System.Windows.Forms.TableLayoutPanel()
        Me.MemoTextBox1 = New CustomControl.MyTextBox()
        Me.Panel1 = New System.Windows.Forms.Panel()
        Me.MyButton2 = New CustomControl.MyButton()
        Me.MyButton1 = New CustomControl.MyButton()
        Me.T_Line3 = New System.Windows.Forms.Label()
        Me.ToolBar1 = New C1.Win.C1Command.C1ToolBar()
        Me.Link6 = New C1.Win.C1Command.C1CommandLink()
        Me.Link1 = New C1.Win.C1Command.C1CommandLink()
        Me.Link2 = New C1.Win.C1Command.C1CommandLink()
        Me.Link7 = New C1.Win.C1Command.C1CommandLink()
        Me.Link3 = New C1.Win.C1Command.C1CommandLink()
        Me.Link4 = New C1.Win.C1Command.C1CommandLink()
        Me.Link5 = New C1.Win.C1Command.C1CommandLink()
        Me.Link8 = New C1.Win.C1Command.C1CommandLink()
        Me.T_Line1 = New System.Windows.Forms.Label()
        Me.JcTextBox = New CustomControl.MyTextBox()
        Me.NameTextBox = New CustomControl.MyTextBox()
        Me.CodeTextBox = New CustomControl.MyTextBox()
        Me.AgeNumericEdit1 = New CustomControl.MyNumericEdit()
        Me.Panel2 = New System.Windows.Forms.Panel()
        Me.RadioButton专科 = New System.Windows.Forms.RadioButton()
        Me.RadioButton标准 = New System.Windows.Forms.RadioButton()
        Me.ks_Combo = New CustomControl.MyDtComobo()
        Me.DoctorDtComobo1 = New CustomControl.MyDtComobo()
        Me.CheckBoxMust = New System.Windows.Forms.CheckBox()
        Me.SexDtComobo1 = New CustomControl.MySingleComobo()
        Me.CheckBoxMulti = New System.Windows.Forms.CheckBox()
        Me.MblbSingleComobo1 = New CustomControl.MyDtComobo()
        CType(Me.C1Holder1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.T_Label1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.T_Label2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.T_Label3, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.TableLayoutPanel1.SuspendLayout()
        Me.Panel1.SuspendLayout()
        Me.ToolBar1.SuspendLayout()
        Me.Panel2.SuspendLayout()
        Me.SuspendLayout()
        '
        'C1Holder1
        '
        Me.C1Holder1.Commands.Add(Me.Move1)
        Me.C1Holder1.Commands.Add(Me.Move2)
        Me.C1Holder1.Commands.Add(Me.Move3)
        Me.C1Holder1.Commands.Add(Me.Move4)
        Me.C1Holder1.Commands.Add(Me.Move5)
        Me.C1Holder1.Commands.Add(Me.Control1)
        Me.C1Holder1.Commands.Add(Me.Control2)
        Me.C1Holder1.Commands.Add(Me.Control4)
        Me.C1Holder1.Commands.Add(Me.Control3)
        Me.C1Holder1.Owner = Me
        Me.C1Holder1.VisualStyle = C1.Win.C1Command.VisualStyle.Office2010Blue
        '
        'Move1
        '
        Me.Move1.Image = CType(resources.GetObject("Move1.Image"), System.Drawing.Image)
        Me.Move1.Name = "Move1"
        Me.Move1.Shortcut = System.Windows.Forms.Shortcut.F2
        Me.Move1.ShortcutText = ""
        Me.Move1.Text = "最前"
        Me.Move1.ToolTipText = "移到最前记录"
        '
        'Move2
        '
        Me.Move2.Image = CType(resources.GetObject("Move2.Image"), System.Drawing.Image)
        Me.Move2.Name = "Move2"
        Me.Move2.Shortcut = System.Windows.Forms.Shortcut.F3
        Me.Move2.ShortcutText = ""
        Me.Move2.Text = "上移"
        Me.Move2.ToolTipText = "上移记录"
        '
        'Move3
        '
        Me.Move3.Image = CType(resources.GetObject("Move3.Image"), System.Drawing.Image)
        Me.Move3.Name = "Move3"
        Me.Move3.Shortcut = System.Windows.Forms.Shortcut.F4
        Me.Move3.ShortcutText = ""
        Me.Move3.Text = "下移"
        Me.Move3.ToolTipText = "下移记录"
        '
        'Move4
        '
        Me.Move4.Image = CType(resources.GetObject("Move4.Image"), System.Drawing.Image)
        Me.Move4.Name = "Move4"
        Me.Move4.Shortcut = System.Windows.Forms.Shortcut.F5
        Me.Move4.ShortcutText = ""
        Me.Move4.Text = "最后"
        Me.Move4.ToolTipText = "移至最后记录"
        '
        'Move5
        '
        Me.Move5.Image = CType(resources.GetObject("Move5.Image"), System.Drawing.Image)
        Me.Move5.Name = "Move5"
        Me.Move5.Shortcut = System.Windows.Forms.Shortcut.F6
        Me.Move5.ShortcutText = ""
        Me.Move5.Text = "新增"
        '
        'Control1
        '
        Me.Control1.Control = Me.T_Label1
        Me.Control1.HotFrame = C1.Win.C1Command.HotFrameEnum.None
        Me.Control1.Name = "Control1"
        Me.Control1.ShortcutText = ""
        Me.Control1.ShowShortcut = False
        Me.Control1.ShowTextAsToolTip = False
        '
        'T_Label1
        '
        Me.T_Label1.AutoSize = True
        Me.T_Label1.BackColor = System.Drawing.Color.Transparent
        Me.T_Label1.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.T_Label1.ForeColor = System.Drawing.SystemColors.ControlText
        Me.T_Label1.Location = New System.Drawing.Point(11, 11)
        Me.T_Label1.Name = "T_Label1"
        Me.T_Label1.Size = New System.Drawing.Size(0, 0)
        Me.T_Label1.TabIndex = 36
        Me.T_Label1.Tag = Nothing
        Me.T_Label1.TextAlign = System.Drawing.ContentAlignment.BottomLeft
        Me.T_Label1.TextDetached = True
        '
        'Control2
        '
        Me.Control2.Control = Me.T_Label2
        Me.Control2.HotFrame = C1.Win.C1Command.HotFrameEnum.None
        Me.Control2.Name = "Control2"
        Me.Control2.ShortcutText = ""
        Me.Control2.ShowShortcut = False
        Me.Control2.ShowTextAsToolTip = False
        '
        'T_Label2
        '
        Me.T_Label2.AutoSize = True
        Me.T_Label2.BackColor = System.Drawing.Color.Transparent
        Me.T_Label2.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.T_Label2.ForeColor = System.Drawing.SystemColors.ControlText
        Me.T_Label2.Location = New System.Drawing.Point(81, 11)
        Me.T_Label2.Name = "T_Label2"
        Me.T_Label2.Size = New System.Drawing.Size(0, 0)
        Me.T_Label2.TabIndex = 37
        Me.T_Label2.Tag = Nothing
        Me.T_Label2.TextAlign = System.Drawing.ContentAlignment.BottomCenter
        Me.T_Label2.TextDetached = True
        Me.T_Label2.TrimStart = True
        '
        'Control4
        '
        Me.Control4.Name = "Control4"
        Me.Control4.ShortcutText = ""
        Me.Control4.Text = "New Command"
        '
        'Control3
        '
        Me.Control3.Control = Me.T_Label3
        Me.Control3.HotFrame = C1.Win.C1Command.HotFrameEnum.None
        Me.Control3.Name = "Control3"
        Me.Control3.ShortcutText = ""
        Me.Control3.ShowShortcut = False
        Me.Control3.ShowTextAsToolTip = False
        '
        'T_Label3
        '
        Me.T_Label3.BackColor = System.Drawing.Color.Transparent
        Me.T_Label3.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.T_Label3.ForeColor = System.Drawing.SystemColors.ControlText
        Me.T_Label3.Location = New System.Drawing.Point(200, 3)
        Me.T_Label3.Name = "T_Label3"
        Me.T_Label3.Size = New System.Drawing.Size(35, 15)
        Me.T_Label3.TabIndex = 35
        Me.T_Label3.Tag = Nothing
        Me.T_Label3.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.T_Label3.TextDetached = True
        '
        'TableLayoutPanel1
        '
        Me.TableLayoutPanel1.ColumnCount = 4
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 60.0!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 160.0!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 60.0!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 175.0!))
        Me.TableLayoutPanel1.Controls.Add(Me.MemoTextBox1, 0, 8)
        Me.TableLayoutPanel1.Controls.Add(Me.Panel1, 0, 10)
        Me.TableLayoutPanel1.Controls.Add(Me.T_Line1, 0, 2)
        Me.TableLayoutPanel1.Controls.Add(Me.JcTextBox, 2, 3)
        Me.TableLayoutPanel1.Controls.Add(Me.NameTextBox, 0, 3)
        Me.TableLayoutPanel1.Controls.Add(Me.CodeTextBox, 0, 1)
        Me.TableLayoutPanel1.Controls.Add(Me.AgeNumericEdit1, 2, 4)
        Me.TableLayoutPanel1.Controls.Add(Me.Panel2, 0, 5)
        Me.TableLayoutPanel1.Controls.Add(Me.ks_Combo, 0, 6)
        Me.TableLayoutPanel1.Controls.Add(Me.DoctorDtComobo1, 2, 6)
        Me.TableLayoutPanel1.Controls.Add(Me.CheckBoxMust, 3, 7)
        Me.TableLayoutPanel1.Controls.Add(Me.SexDtComobo1, 0, 4)
        Me.TableLayoutPanel1.Controls.Add(Me.CheckBoxMulti, 1, 7)
        Me.TableLayoutPanel1.Controls.Add(Me.MblbSingleComobo1, 2, 1)
        Me.TableLayoutPanel1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.TableLayoutPanel1.Location = New System.Drawing.Point(0, 0)
        Me.TableLayoutPanel1.Name = "TableLayoutPanel1"
        Me.TableLayoutPanel1.RowCount = 11
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 4.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 27.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 4.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 27.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 27.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 27.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 27.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 27.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 27.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 35.0!))
        Me.TableLayoutPanel1.Size = New System.Drawing.Size(455, 309)
        Me.TableLayoutPanel1.TabIndex = 131
        '
        'MemoTextBox1
        '
        Me.MemoTextBox1.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.MemoTextBox1.Captain = "备    注"
        Me.MemoTextBox1.CaptainBackColor = System.Drawing.Color.Transparent
        Me.MemoTextBox1.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.MemoTextBox1.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.MemoTextBox1.CaptainWidth = 60.0!
        Me.TableLayoutPanel1.SetColumnSpan(Me.MemoTextBox1, 4)
        Me.MemoTextBox1.ContentForeColor = System.Drawing.Color.Black
        Me.MemoTextBox1.DisabledForeColor = System.Drawing.Color.FromArgb(CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer))
        Me.MemoTextBox1.Location = New System.Drawing.Point(3, 173)
        Me.MemoTextBox1.Multiline = True
        Me.MemoTextBox1.Name = "MemoTextBox1"
        Me.MemoTextBox1.PasswordChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.MemoTextBox1.ReadOnly = False
        Me.TableLayoutPanel1.SetRowSpan(Me.MemoTextBox1, 2)
        Me.MemoTextBox1.ScrollBars = System.Windows.Forms.ScrollBars.Vertical
        Me.MemoTextBox1.SelectionStart = 0
        Me.MemoTextBox1.SelectStart = 0
        Me.MemoTextBox1.Size = New System.Drawing.Size(449, 98)
        Me.MemoTextBox1.TabIndex = 6
        Me.MemoTextBox1.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.MemoTextBox1.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Top
        '
        'Panel1
        '
        Me.TableLayoutPanel1.SetColumnSpan(Me.Panel1, 4)
        Me.Panel1.Controls.Add(Me.MyButton2)
        Me.Panel1.Controls.Add(Me.MyButton1)
        Me.Panel1.Controls.Add(Me.T_Line3)
        Me.Panel1.Controls.Add(Me.ToolBar1)
        Me.Panel1.Dock = System.Windows.Forms.DockStyle.Bottom
        Me.Panel1.Location = New System.Drawing.Point(0, 279)
        Me.Panel1.Margin = New System.Windows.Forms.Padding(0)
        Me.Panel1.Name = "Panel1"
        Me.Panel1.Size = New System.Drawing.Size(455, 30)
        Me.Panel1.TabIndex = 7
        '
        'MyButton2
        '
        Me.MyButton2.ButtonImageSize = CustomControl.MyButton.imageSize.small
        Me.MyButton2.DialogResult = System.Windows.Forms.DialogResult.None
        Me.MyButton2.Location = New System.Drawing.Point(348, 2)
        Me.MyButton2.Name = "MyButton2"
        Me.MyButton2.Size = New System.Drawing.Size(60, 25)
        Me.MyButton2.TabIndex = 1
        Me.MyButton2.Tag = "取消"
        Me.MyButton2.Text = "取消"
        '
        'MyButton1
        '
        Me.MyButton1.ButtonImageSize = CustomControl.MyButton.imageSize.small
        Me.MyButton1.DialogResult = System.Windows.Forms.DialogResult.None
        Me.MyButton1.Location = New System.Drawing.Point(273, 2)
        Me.MyButton1.Name = "MyButton1"
        Me.MyButton1.Size = New System.Drawing.Size(60, 25)
        Me.MyButton1.TabIndex = 0
        Me.MyButton1.Tag = "保存"
        Me.MyButton1.Text = "保存"
        '
        'T_Line3
        '
        Me.T_Line3.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D
        Me.T_Line3.Dock = System.Windows.Forms.DockStyle.Top
        Me.T_Line3.Location = New System.Drawing.Point(0, 0)
        Me.T_Line3.Name = "T_Line3"
        Me.T_Line3.Size = New System.Drawing.Size(455, 2)
        Me.T_Line3.TabIndex = 101
        Me.T_Line3.Text = "Label2"
        '
        'ToolBar1
        '
        Me.ToolBar1.AccessibleName = "Tool Bar"
        Me.ToolBar1.BackColor = System.Drawing.Color.Transparent
        Me.ToolBar1.CommandHolder = Me.C1Holder1
        Me.ToolBar1.CommandLinks.AddRange(New C1.Win.C1Command.C1CommandLink() {Me.Link6, Me.Link1, Me.Link2, Me.Link7, Me.Link3, Me.Link4, Me.Link5, Me.Link8})
        Me.ToolBar1.Controls.Add(Me.T_Label1)
        Me.ToolBar1.Controls.Add(Me.T_Label3)
        Me.ToolBar1.Controls.Add(Me.T_Label2)
        Me.ToolBar1.Location = New System.Drawing.Point(1, 3)
        Me.ToolBar1.MinButtonSize = 22
        Me.ToolBar1.Movable = False
        Me.ToolBar1.Name = "ToolBar1"
        Me.ToolBar1.Size = New System.Drawing.Size(236, 22)
        Me.ToolBar1.Text = "C1ToolBar2"
        Me.ToolBar1.VisualStyle = C1.Win.C1Command.VisualStyle.Custom
        Me.ToolBar1.VisualStyleBase = C1.Win.C1Command.VisualStyle.Classic
        '
        'Link6
        '
        Me.Link6.Command = Me.Control1
        '
        'Link1
        '
        Me.Link1.Command = Me.Move1
        Me.Link1.Delimiter = True
        Me.Link1.SortOrder = 1
        '
        'Link2
        '
        Me.Link2.Command = Me.Move2
        Me.Link2.SortOrder = 2
        '
        'Link7
        '
        Me.Link7.Command = Me.Control2
        Me.Link7.SortOrder = 3
        '
        'Link3
        '
        Me.Link3.Command = Me.Move3
        Me.Link3.SortOrder = 4
        '
        'Link4
        '
        Me.Link4.Command = Me.Move4
        Me.Link4.SortOrder = 5
        '
        'Link5
        '
        Me.Link5.ButtonLook = CType((C1.Win.C1Command.ButtonLookFlags.Text Or C1.Win.C1Command.ButtonLookFlags.Image), C1.Win.C1Command.ButtonLookFlags)
        Me.Link5.Command = Me.Move5
        Me.Link5.Delimiter = True
        Me.Link5.SortOrder = 6
        Me.Link5.Text = "新增"
        Me.Link5.ToolTipText = "新增记录"
        '
        'Link8
        '
        Me.Link8.Command = Me.Control3
        Me.Link8.Delimiter = True
        Me.Link8.SortOrder = 7
        Me.Link8.Text = "New Command"
        '
        'T_Line1
        '
        Me.T_Line1.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.T_Line1.BackColor = System.Drawing.SystemColors.Control
        Me.T_Line1.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D
        Me.TableLayoutPanel1.SetColumnSpan(Me.T_Line1, 4)
        Me.T_Line1.Location = New System.Drawing.Point(3, 32)
        Me.T_Line1.Name = "T_Line1"
        Me.T_Line1.Size = New System.Drawing.Size(449, 2)
        Me.T_Line1.TabIndex = 1
        Me.T_Line1.Text = "Label1"
        '
        'JcTextBox
        '
        Me.JcTextBox.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.JcTextBox.Captain = "模板简称"
        Me.JcTextBox.CaptainBackColor = System.Drawing.Color.Transparent
        Me.JcTextBox.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.JcTextBox.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.JcTextBox.CaptainWidth = 60.0!
        Me.TableLayoutPanel1.SetColumnSpan(Me.JcTextBox, 2)
        Me.JcTextBox.ContentForeColor = System.Drawing.Color.Black
        Me.JcTextBox.DisabledForeColor = System.Drawing.Color.FromArgb(CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer))
        Me.JcTextBox.Location = New System.Drawing.Point(223, 38)
        Me.JcTextBox.Multiline = False
        Me.JcTextBox.Name = "JcTextBox"
        Me.JcTextBox.PasswordChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.JcTextBox.ReadOnly = False
        Me.JcTextBox.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.JcTextBox.SelectionStart = 0
        Me.JcTextBox.SelectStart = 0
        Me.JcTextBox.Size = New System.Drawing.Size(229, 20)
        Me.JcTextBox.TabIndex = 140
        Me.JcTextBox.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.JcTextBox.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle
        '
        'NameTextBox
        '
        Me.NameTextBox.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.NameTextBox.Captain = "模板名称"
        Me.NameTextBox.CaptainBackColor = System.Drawing.Color.Transparent
        Me.NameTextBox.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.NameTextBox.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.NameTextBox.CaptainWidth = 60.0!
        Me.TableLayoutPanel1.SetColumnSpan(Me.NameTextBox, 2)
        Me.NameTextBox.ContentForeColor = System.Drawing.Color.Black
        Me.NameTextBox.DisabledForeColor = System.Drawing.Color.FromArgb(CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer))
        Me.NameTextBox.Location = New System.Drawing.Point(3, 38)
        Me.NameTextBox.Multiline = False
        Me.NameTextBox.Name = "NameTextBox"
        Me.NameTextBox.PasswordChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.NameTextBox.ReadOnly = False
        Me.NameTextBox.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.NameTextBox.SelectionStart = 0
        Me.NameTextBox.SelectStart = 0
        Me.NameTextBox.Size = New System.Drawing.Size(214, 21)
        Me.NameTextBox.TabIndex = 1
        Me.NameTextBox.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.NameTextBox.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle
        '
        'CodeTextBox
        '
        Me.CodeTextBox.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.CodeTextBox.Captain = "模板编码"
        Me.CodeTextBox.CaptainBackColor = System.Drawing.Color.Transparent
        Me.CodeTextBox.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.CodeTextBox.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.CodeTextBox.CaptainWidth = 60.0!
        Me.TableLayoutPanel1.SetColumnSpan(Me.CodeTextBox, 2)
        Me.CodeTextBox.ContentForeColor = System.Drawing.Color.Black
        Me.CodeTextBox.DisabledForeColor = System.Drawing.Color.FromArgb(CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer))
        Me.CodeTextBox.Location = New System.Drawing.Point(3, 7)
        Me.CodeTextBox.Multiline = False
        Me.CodeTextBox.Name = "CodeTextBox"
        Me.CodeTextBox.PasswordChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.CodeTextBox.ReadOnly = False
        Me.CodeTextBox.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.CodeTextBox.SelectionStart = 0
        Me.CodeTextBox.SelectStart = 0
        Me.CodeTextBox.Size = New System.Drawing.Size(214, 20)
        Me.CodeTextBox.TabIndex = 100
        Me.CodeTextBox.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.CodeTextBox.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle
        '
        'AgeNumericEdit1
        '
        Me.AgeNumericEdit1.Captain = "年龄限制"
        Me.AgeNumericEdit1.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.AgeNumericEdit1.CaptainWidth = 60.0!
        Me.TableLayoutPanel1.SetColumnSpan(Me.AgeNumericEdit1, 2)
        Me.AgeNumericEdit1.Location = New System.Drawing.Point(223, 65)
        Me.AgeNumericEdit1.MaximumSize = New System.Drawing.Size(100000, 80)
        Me.AgeNumericEdit1.MinimumSize = New System.Drawing.Size(0, 20)
        Me.AgeNumericEdit1.Name = "AgeNumericEdit1"
        Me.AgeNumericEdit1.NumFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.AgeNumericEdit1.ReadOnly = False
        Me.AgeNumericEdit1.Size = New System.Drawing.Size(229, 20)
        Me.AgeNumericEdit1.TabIndex = 3
        Me.AgeNumericEdit1.ValueIsDbNull = False
        '
        'Panel2
        '
        Me.TableLayoutPanel1.SetColumnSpan(Me.Panel2, 4)
        Me.Panel2.Controls.Add(Me.RadioButton专科)
        Me.Panel2.Controls.Add(Me.RadioButton标准)
        Me.Panel2.Location = New System.Drawing.Point(3, 92)
        Me.Panel2.Name = "Panel2"
        Me.Panel2.Size = New System.Drawing.Size(449, 21)
        Me.Panel2.TabIndex = 8
        '
        'RadioButton专科
        '
        Me.RadioButton专科.AutoSize = True
        Me.RadioButton专科.Location = New System.Drawing.Point(280, 2)
        Me.RadioButton专科.Name = "RadioButton专科"
        Me.RadioButton专科.Size = New System.Drawing.Size(71, 16)
        Me.RadioButton专科.TabIndex = 202
        Me.RadioButton专科.TabStop = True
        Me.RadioButton专科.Text = "专科模板"
        Me.RadioButton专科.UseVisualStyleBackColor = True
        '
        'RadioButton标准
        '
        Me.RadioButton标准.AutoSize = True
        Me.RadioButton标准.Location = New System.Drawing.Point(61, 2)
        Me.RadioButton标准.Name = "RadioButton标准"
        Me.RadioButton标准.Size = New System.Drawing.Size(71, 16)
        Me.RadioButton标准.TabIndex = 201
        Me.RadioButton标准.TabStop = True
        Me.RadioButton标准.Text = "标准模板"
        Me.RadioButton标准.UseVisualStyleBackColor = True
        '
        'ks_Combo
        '
        Me.ks_Combo.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.ks_Combo.Captain = "所属科室"
        Me.ks_Combo.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.ks_Combo.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.ks_Combo.CaptainWidth = 60.0!
        Me.TableLayoutPanel1.SetColumnSpan(Me.ks_Combo, 2)
        Me.ks_Combo.DataSource = Nothing
        Me.ks_Combo.ItemHeight = 16
        Me.ks_Combo.ItemTextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.ks_Combo.Location = New System.Drawing.Point(3, 119)
        Me.ks_Combo.MaximumSize = New System.Drawing.Size(10000, 20)
        Me.ks_Combo.MinimumSize = New System.Drawing.Size(0, 20)
        Me.ks_Combo.Name = "ks_Combo"
        Me.ks_Combo.ReadOnly = False
        Me.ks_Combo.Size = New System.Drawing.Size(214, 20)
        Me.ks_Combo.TabIndex = 4
        Me.ks_Combo.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        '
        'DoctorDtComobo1
        '
        Me.DoctorDtComobo1.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.DoctorDtComobo1.Captain = "所属医生"
        Me.DoctorDtComobo1.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.DoctorDtComobo1.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.DoctorDtComobo1.CaptainWidth = 60.0!
        Me.TableLayoutPanel1.SetColumnSpan(Me.DoctorDtComobo1, 2)
        Me.DoctorDtComobo1.DataSource = Nothing
        Me.DoctorDtComobo1.ItemHeight = 16
        Me.DoctorDtComobo1.ItemTextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.DoctorDtComobo1.Location = New System.Drawing.Point(223, 119)
        Me.DoctorDtComobo1.MaximumSize = New System.Drawing.Size(10000, 20)
        Me.DoctorDtComobo1.MinimumSize = New System.Drawing.Size(0, 20)
        Me.DoctorDtComobo1.Name = "DoctorDtComobo1"
        Me.DoctorDtComobo1.ReadOnly = False
        Me.DoctorDtComobo1.Size = New System.Drawing.Size(229, 20)
        Me.DoctorDtComobo1.TabIndex = 5
        Me.DoctorDtComobo1.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        '
        'CheckBoxMust
        '
        Me.CheckBoxMust.AutoSize = True
        Me.CheckBoxMust.Location = New System.Drawing.Point(283, 146)
        Me.CheckBoxMust.Name = "CheckBoxMust"
        Me.CheckBoxMust.Size = New System.Drawing.Size(72, 16)
        Me.CheckBoxMust.TabIndex = 10
        Me.CheckBoxMust.Text = "是否必须"
        Me.CheckBoxMust.UseVisualStyleBackColor = True
        '
        'SexDtComobo1
        '
        Me.SexDtComobo1.Captain = "适应性别"
        Me.SexDtComobo1.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.SexDtComobo1.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.SexDtComobo1.CaptainWidth = 60.0!
        Me.TableLayoutPanel1.SetColumnSpan(Me.SexDtComobo1, 2)
        Me.SexDtComobo1.ComboStyle = C1.Win.C1List.ComboStyleEnum.DropdownList
        Me.SexDtComobo1.ItemHeight = 16
        Me.SexDtComobo1.ItemTextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.SexDtComobo1.Location = New System.Drawing.Point(3, 65)
        Me.SexDtComobo1.MaximumSize = New System.Drawing.Size(10000, 20)
        Me.SexDtComobo1.MinimumSize = New System.Drawing.Size(0, 20)
        Me.SexDtComobo1.Name = "SexDtComobo1"
        Me.SexDtComobo1.ReadOnly = False
        Me.SexDtComobo1.Size = New System.Drawing.Size(214, 20)
        Me.SexDtComobo1.TabIndex = 2
        Me.SexDtComobo1.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        '
        'CheckBoxMulti
        '
        Me.CheckBoxMulti.AutoSize = True
        Me.CheckBoxMulti.Location = New System.Drawing.Point(63, 146)
        Me.CheckBoxMulti.Name = "CheckBoxMulti"
        Me.CheckBoxMulti.Size = New System.Drawing.Size(72, 16)
        Me.CheckBoxMulti.TabIndex = 9
        Me.CheckBoxMulti.Text = "是否多份"
        Me.CheckBoxMulti.UseVisualStyleBackColor = True
        '
        'MblbSingleComobo1
        '
        Me.MblbSingleComobo1.Captain = "模板类别"
        Me.MblbSingleComobo1.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.MblbSingleComobo1.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.MblbSingleComobo1.CaptainWidth = 60.0!
        Me.TableLayoutPanel1.SetColumnSpan(Me.MblbSingleComobo1, 2)
        Me.MblbSingleComobo1.DataSource = Nothing
        Me.MblbSingleComobo1.ItemHeight = 16
        Me.MblbSingleComobo1.ItemTextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.MblbSingleComobo1.Location = New System.Drawing.Point(223, 7)
        Me.MblbSingleComobo1.MaximumSize = New System.Drawing.Size(10000, 20)
        Me.MblbSingleComobo1.MinimumSize = New System.Drawing.Size(0, 20)
        Me.MblbSingleComobo1.Name = "MblbSingleComobo1"
        Me.MblbSingleComobo1.ReadOnly = False
        Me.MblbSingleComobo1.Size = New System.Drawing.Size(229, 20)
        Me.MblbSingleComobo1.TabIndex = 0
        Me.MblbSingleComobo1.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        '
        'EmrMb3
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 12.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(455, 309)
        Me.Controls.Add(Me.TableLayoutPanel1)
        Me.Margin = New System.Windows.Forms.Padding(5)
        Me.MinimizeBox = False
        Me.Name = "EmrMb3"
        Me.Text = "模板信息"
        CType(Me.C1Holder1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.T_Label1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.T_Label2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.T_Label3, System.ComponentModel.ISupportInitialize).EndInit()
        Me.TableLayoutPanel1.ResumeLayout(False)
        Me.TableLayoutPanel1.PerformLayout()
        Me.Panel1.ResumeLayout(False)
        Me.ToolBar1.ResumeLayout(False)
        Me.ToolBar1.PerformLayout()
        Me.Panel2.ResumeLayout(False)
        Me.Panel2.PerformLayout()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents C1Holder1 As C1.Win.C1Command.C1CommandHolder
    Friend WithEvents Move1 As C1.Win.C1Command.C1Command
    Friend WithEvents Move2 As C1.Win.C1Command.C1Command
    Friend WithEvents Move3 As C1.Win.C1Command.C1Command
    Friend WithEvents Move4 As C1.Win.C1Command.C1Command
    Friend WithEvents Move5 As C1.Win.C1Command.C1Command
    Friend WithEvents Control1 As C1.Win.C1Command.C1CommandControl
    Friend WithEvents Control2 As C1.Win.C1Command.C1CommandControl
    Friend WithEvents ToolTip1 As System.Windows.Forms.ToolTip
    Friend WithEvents Control4 As C1.Win.C1Command.C1Command
    Friend WithEvents TableLayoutPanel1 As System.Windows.Forms.TableLayoutPanel
    Friend WithEvents T_Line1 As System.Windows.Forms.Label
    Friend WithEvents JcTextBox As CustomControl.MyTextBox
    Friend WithEvents NameTextBox As CustomControl.MyTextBox
    Friend WithEvents CodeTextBox As CustomControl.MyTextBox
    Friend WithEvents ks_Combo As CustomControl.MyDtComobo
    Friend WithEvents AgeNumericEdit1 As CustomControl.MyNumericEdit
    Friend WithEvents DoctorDtComobo1 As CustomControl.MyDtComobo
    Friend WithEvents CheckBoxMust As System.Windows.Forms.CheckBox
    Friend WithEvents MemoTextBox1 As CustomControl.MyTextBox
    Friend WithEvents Panel2 As System.Windows.Forms.Panel
    Friend WithEvents RadioButton专科 As System.Windows.Forms.RadioButton
    Friend WithEvents RadioButton标准 As System.Windows.Forms.RadioButton
    Friend WithEvents SexDtComobo1 As CustomControl.MySingleComobo
    Friend WithEvents CheckBoxMulti As System.Windows.Forms.CheckBox
    Friend WithEvents T_Label1 As C1.Win.C1Input.C1Label
    Friend WithEvents T_Label2 As C1.Win.C1Input.C1Label
    Friend WithEvents Control3 As C1.Win.C1Command.C1CommandControl
    Friend WithEvents T_Label3 As C1.Win.C1Input.C1Label
    Friend WithEvents Panel1 As System.Windows.Forms.Panel
    Friend WithEvents MyButton2 As CustomControl.MyButton
    Friend WithEvents MyButton1 As CustomControl.MyButton
    Friend WithEvents T_Line3 As System.Windows.Forms.Label
    Friend WithEvents ToolBar1 As C1.Win.C1Command.C1ToolBar
    Friend WithEvents Link6 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents Link1 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents Link2 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents Link7 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents Link3 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents Link4 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents Link5 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents Link8 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents MblbSingleComobo1 As CustomControl.MyDtComobo
End Class
