﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class Hsz_Syk
    Inherits HisControl.BaseChild

    'Form 重写 Dispose，以清理组件列表。
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Windows 窗体设计器所必需的
    Private components As System.ComponentModel.IContainer

    '注意: 以下过程是 Windows 窗体设计器所必需的
    '可以使用 Windows 窗体设计器修改它。
    '不要使用代码编辑器修改它。
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(Hsz_Syk))
        Me.C1TextBox10 = New C1.Win.C1Input.C1TextBox()
        Me.C1TextBox9 = New C1.Win.C1Input.C1TextBox()
        Me.C1TextBox8 = New C1.Win.C1Input.C1TextBox()
        Me.C1TextBox7 = New C1.Win.C1Input.C1TextBox()
        Me.C1TextBox11 = New C1.Win.C1Input.C1TextBox()
        Me.C1TextBox13 = New C1.Win.C1Input.C1TextBox()
        Me.C1TextBox14 = New C1.Win.C1Input.C1TextBox()
        Me.C1TextBox15 = New C1.Win.C1Input.C1TextBox()
        Me.C1TextBox16 = New C1.Win.C1Input.C1TextBox()
        Me.T_Textbox = New C1.Win.C1Input.C1TextBox()
        Me.Panel1 = New System.Windows.Forms.Panel()
        Me.TableLayoutPanel1 = New System.Windows.Forms.TableLayoutPanel()
        Me.FzComobo = New CustomControl.MySingleComobo()
        Me.YPComobo = New CustomControl.MyDtComobo()
        Me.MemoTextBox = New CustomControl.MyTextBox()
        Me.KySlNumericEdit = New CustomControl.MyNumericEdit()
        Me.PbSlNumericEdit = New CustomControl.MyNumericEdit()
        Me.OKButton = New CustomControl.MyButton()
        Me.DelButton = New CustomControl.MyButton()
        Me.DyButton = New CustomControl.MyButton()
        Me.C1TextBox1 = New C1.Win.C1Input.C1TextBox()
        Me.C1TextBox2 = New C1.Win.C1Input.C1TextBox()
        Me.C1TextBox3 = New C1.Win.C1Input.C1TextBox()
        Me.C1TextBox4 = New C1.Win.C1Input.C1TextBox()
        Me.C1TextBox5 = New C1.Win.C1Input.C1TextBox()
        Me.C1TextBox6 = New C1.Win.C1Input.C1TextBox()
        Me.MyGrid1 = New CustomControl.MyGrid()
        CType(Me.C1TextBox10,System.ComponentModel.ISupportInitialize).BeginInit
        CType(Me.C1TextBox9,System.ComponentModel.ISupportInitialize).BeginInit
        CType(Me.C1TextBox8,System.ComponentModel.ISupportInitialize).BeginInit
        CType(Me.C1TextBox7,System.ComponentModel.ISupportInitialize).BeginInit
        CType(Me.C1TextBox11,System.ComponentModel.ISupportInitialize).BeginInit
        CType(Me.C1TextBox13,System.ComponentModel.ISupportInitialize).BeginInit
        CType(Me.C1TextBox14,System.ComponentModel.ISupportInitialize).BeginInit
        CType(Me.C1TextBox15,System.ComponentModel.ISupportInitialize).BeginInit
        CType(Me.C1TextBox16,System.ComponentModel.ISupportInitialize).BeginInit
        CType(Me.T_Textbox,System.ComponentModel.ISupportInitialize).BeginInit
        Me.Panel1.SuspendLayout
        Me.TableLayoutPanel1.SuspendLayout
        CType(Me.C1TextBox1,System.ComponentModel.ISupportInitialize).BeginInit
        CType(Me.C1TextBox2,System.ComponentModel.ISupportInitialize).BeginInit
        CType(Me.C1TextBox3,System.ComponentModel.ISupportInitialize).BeginInit
        CType(Me.C1TextBox4,System.ComponentModel.ISupportInitialize).BeginInit
        CType(Me.C1TextBox5,System.ComponentModel.ISupportInitialize).BeginInit
        CType(Me.C1TextBox6,System.ComponentModel.ISupportInitialize).BeginInit
        Me.SuspendLayout
        '
        'C1TextBox10
        '
        Me.C1TextBox10.BackColor = System.Drawing.SystemColors.Info
        Me.C1TextBox10.BorderColor = System.Drawing.SystemColors.WindowFrame
        Me.C1TextBox10.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.C1TextBox10.Location = New System.Drawing.Point(564, 10)
        Me.C1TextBox10.Name = "C1TextBox10"
        Me.C1TextBox10.ReadOnly = true
        Me.C1TextBox10.Size = New System.Drawing.Size(208, 14)
        Me.C1TextBox10.TabIndex = 69
        Me.C1TextBox10.Tag = Nothing
        Me.C1TextBox10.TextDetached = true
        Me.C1TextBox10.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
        Me.C1TextBox10.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'C1TextBox9
        '
        Me.C1TextBox9.BackColor = System.Drawing.SystemColors.Info
        Me.C1TextBox9.BorderColor = System.Drawing.SystemColors.WindowFrame
        Me.C1TextBox9.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.C1TextBox9.Location = New System.Drawing.Point(69, 34)
        Me.C1TextBox9.Name = "C1TextBox9"
        Me.C1TextBox9.ReadOnly = true
        Me.C1TextBox9.Size = New System.Drawing.Size(123, 14)
        Me.C1TextBox9.TabIndex = 71
        Me.C1TextBox9.Tag = Nothing
        Me.C1TextBox9.TextDetached = true
        Me.C1TextBox9.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
        Me.C1TextBox9.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'C1TextBox8
        '
        Me.C1TextBox8.BackColor = System.Drawing.SystemColors.Info
        Me.C1TextBox8.BorderColor = System.Drawing.SystemColors.WindowFrame
        Me.C1TextBox8.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.C1TextBox8.Location = New System.Drawing.Point(276, 34)
        Me.C1TextBox8.Name = "C1TextBox8"
        Me.C1TextBox8.ReadOnly = true
        Me.C1TextBox8.Size = New System.Drawing.Size(208, 14)
        Me.C1TextBox8.TabIndex = 73
        Me.C1TextBox8.Tag = Nothing
        Me.C1TextBox8.TextDetached = true
        Me.C1TextBox8.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
        Me.C1TextBox8.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'C1TextBox7
        '
        Me.C1TextBox7.BackColor = System.Drawing.SystemColors.Info
        Me.C1TextBox7.BorderColor = System.Drawing.SystemColors.WindowFrame
        Me.C1TextBox7.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.C1TextBox7.Location = New System.Drawing.Point(564, 35)
        Me.C1TextBox7.Name = "C1TextBox7"
        Me.C1TextBox7.ReadOnly = true
        Me.C1TextBox7.Size = New System.Drawing.Size(208, 14)
        Me.C1TextBox7.TabIndex = 75
        Me.C1TextBox7.Tag = Nothing
        Me.C1TextBox7.TextDetached = true
        Me.C1TextBox7.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
        Me.C1TextBox7.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'C1TextBox11
        '
        Me.C1TextBox11.BackColor = System.Drawing.SystemColors.Info
        Me.C1TextBox11.BorderColor = System.Drawing.SystemColors.WindowFrame
        Me.C1TextBox11.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.C1TextBox11.Location = New System.Drawing.Point(276, 9)
        Me.C1TextBox11.Name = "C1TextBox11"
        Me.C1TextBox11.ReadOnly = true
        Me.C1TextBox11.Size = New System.Drawing.Size(208, 14)
        Me.C1TextBox11.TabIndex = 81
        Me.C1TextBox11.Tag = Nothing
        Me.C1TextBox11.TextDetached = true
        Me.C1TextBox11.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
        Me.C1TextBox11.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'C1TextBox13
        '
        Me.C1TextBox13.BackColor = System.Drawing.SystemColors.Info
        Me.C1TextBox13.BorderColor = System.Drawing.SystemColors.WindowFrame
        Me.C1TextBox13.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.C1TextBox13.Location = New System.Drawing.Point(69, 59)
        Me.C1TextBox13.Name = "C1TextBox13"
        Me.C1TextBox13.ReadOnly = true
        Me.C1TextBox13.Size = New System.Drawing.Size(123, 14)
        Me.C1TextBox13.TabIndex = 82
        Me.C1TextBox13.Tag = Nothing
        Me.C1TextBox13.TextDetached = true
        Me.C1TextBox13.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
        Me.C1TextBox13.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'C1TextBox14
        '
        Me.C1TextBox14.BackColor = System.Drawing.SystemColors.Info
        Me.C1TextBox14.BorderColor = System.Drawing.SystemColors.WindowFrame
        Me.C1TextBox14.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.C1TextBox14.Location = New System.Drawing.Point(276, 59)
        Me.C1TextBox14.Name = "C1TextBox14"
        Me.C1TextBox14.ReadOnly = true
        Me.C1TextBox14.Size = New System.Drawing.Size(80, 14)
        Me.C1TextBox14.TabIndex = 83
        Me.C1TextBox14.Tag = Nothing
        Me.C1TextBox14.TextDetached = true
        Me.C1TextBox14.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
        Me.C1TextBox14.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'C1TextBox15
        '
        Me.C1TextBox15.BackColor = System.Drawing.SystemColors.Info
        Me.C1TextBox15.BorderColor = System.Drawing.SystemColors.WindowFrame
        Me.C1TextBox15.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.C1TextBox15.Location = New System.Drawing.Point(419, 59)
        Me.C1TextBox15.Name = "C1TextBox15"
        Me.C1TextBox15.ReadOnly = true
        Me.C1TextBox15.Size = New System.Drawing.Size(65, 14)
        Me.C1TextBox15.TabIndex = 84
        Me.C1TextBox15.Tag = Nothing
        Me.C1TextBox15.TextDetached = true
        Me.C1TextBox15.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
        Me.C1TextBox15.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'C1TextBox16
        '
        Me.C1TextBox16.BackColor = System.Drawing.SystemColors.Info
        Me.C1TextBox16.BorderColor = System.Drawing.SystemColors.WindowFrame
        Me.C1TextBox16.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.C1TextBox16.Location = New System.Drawing.Point(564, 59)
        Me.C1TextBox16.Name = "C1TextBox16"
        Me.C1TextBox16.ReadOnly = true
        Me.C1TextBox16.Size = New System.Drawing.Size(80, 14)
        Me.C1TextBox16.TabIndex = 85
        Me.C1TextBox16.Tag = Nothing
        Me.C1TextBox16.TextDetached = true
        Me.C1TextBox16.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
        Me.C1TextBox16.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'T_Textbox
        '
        Me.T_Textbox.AutoSize = false
        Me.T_Textbox.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.T_Textbox.Location = New System.Drawing.Point(715, 59)
        Me.T_Textbox.Name = "T_Textbox"
        Me.T_Textbox.Size = New System.Drawing.Size(57, 14)
        Me.T_Textbox.TabIndex = 39
        Me.T_Textbox.TabStop = false
        Me.T_Textbox.Tag = Nothing
        Me.T_Textbox.TextAlign = System.Windows.Forms.HorizontalAlignment.Right
        Me.T_Textbox.TextDetached = true
        Me.T_Textbox.TrimStart = true
        Me.T_Textbox.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
        '
        'Panel1
        '
        Me.Panel1.Controls.Add(Me.TableLayoutPanel1)
        Me.Panel1.Dock = System.Windows.Forms.DockStyle.Top
        Me.Panel1.Location = New System.Drawing.Point(0, 0)
        Me.Panel1.Name = "Panel1"
        Me.Panel1.Size = New System.Drawing.Size(834, 67)
        Me.Panel1.TabIndex = 38
        '
        'TableLayoutPanel1
        '
        Me.TableLayoutPanel1.ColumnCount = 7
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 160!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 260!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 80!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 80!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 80!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 80!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 94!))
        Me.TableLayoutPanel1.Controls.Add(Me.FzComobo, 0, 1)
        Me.TableLayoutPanel1.Controls.Add(Me.YPComobo, 1, 1)
        Me.TableLayoutPanel1.Controls.Add(Me.MemoTextBox, 0, 2)
        Me.TableLayoutPanel1.Controls.Add(Me.KySlNumericEdit, 2, 1)
        Me.TableLayoutPanel1.Controls.Add(Me.PbSlNumericEdit, 4, 1)
        Me.TableLayoutPanel1.Controls.Add(Me.OKButton, 2, 2)
        Me.TableLayoutPanel1.Controls.Add(Me.DelButton, 3, 2)
        Me.TableLayoutPanel1.Controls.Add(Me.DyButton, 4, 2)
        Me.TableLayoutPanel1.Dock = System.Windows.Forms.DockStyle.Top
        Me.TableLayoutPanel1.Location = New System.Drawing.Point(0, 0)
        Me.TableLayoutPanel1.Name = "TableLayoutPanel1"
        Me.TableLayoutPanel1.RowCount = 4
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 5!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 29!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 29!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 5!))
        Me.TableLayoutPanel1.Size = New System.Drawing.Size(834, 67)
        Me.TableLayoutPanel1.TabIndex = 0
        '
        'FzComobo
        '
        Me.FzComobo.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.FzComobo.Captain = "分组名称"
        Me.FzComobo.CaptainFont = New System.Drawing.Font("宋体", 10.5!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.FzComobo.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.FzComobo.CaptainWidth = 69!
        Me.FzComobo.ComboStyle = C1.Win.C1List.ComboStyleEnum.DropdownList
        Me.FzComobo.ItemHeight = 18
        Me.FzComobo.ItemTextFont = New System.Drawing.Font("宋体", 10.5!)
        Me.FzComobo.Location = New System.Drawing.Point(3, 8)
        Me.FzComobo.MaximumSize = New System.Drawing.Size(10000, 20)
        Me.FzComobo.MinimumSize = New System.Drawing.Size(0, 23)
        Me.FzComobo.Name = "FzComobo"
        Me.FzComobo.ReadOnly = false
        Me.FzComobo.Size = New System.Drawing.Size(154, 23)
        Me.FzComobo.TabIndex = 0
        Me.FzComobo.TextFont = New System.Drawing.Font("宋体", 10.5!)
        '
        'YPComobo
        '
        Me.YPComobo.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.YPComobo.Bookmark = -1
        Me.YPComobo.Captain = "药品名称"
        Me.YPComobo.CaptainFont = New System.Drawing.Font("宋体", 10.5!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.YPComobo.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.YPComobo.CaptainWidth = 60!
        Me.YPComobo.ColumnCaptionHeight = 20
        Me.YPComobo.DataSource = Nothing
        Me.YPComobo.DataView = Nothing
        Me.YPComobo.ItemHeight = 18
        Me.YPComobo.ItemTextFont = New System.Drawing.Font("宋体", 10.5!)
        Me.YPComobo.Location = New System.Drawing.Point(163, 8)
        Me.YPComobo.MaximumSize = New System.Drawing.Size(10000, 20)
        Me.YPComobo.MinimumSize = New System.Drawing.Size(0, 23)
        Me.YPComobo.Name = "YPComobo"
        Me.YPComobo.ReadOnly = false
        Me.YPComobo.Row = 0
        Me.YPComobo.Size = New System.Drawing.Size(254, 23)
        Me.YPComobo.TabIndex = 1
        Me.YPComobo.TextFont = New System.Drawing.Font("宋体", 10.5!)
        '
        'MemoTextBox
        '
        Me.MemoTextBox.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.MemoTextBox.Captain = "备    注"
        Me.MemoTextBox.CaptainBackColor = System.Drawing.Color.Transparent
        Me.MemoTextBox.CaptainFont = New System.Drawing.Font("宋体", 10.5!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.MemoTextBox.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.MemoTextBox.CaptainWidth = 60!
        Me.TableLayoutPanel1.SetColumnSpan(Me.MemoTextBox, 2)
        Me.MemoTextBox.ContentForeColor = System.Drawing.Color.Black
        Me.MemoTextBox.DisabledForeColor = System.Drawing.Color.FromArgb(CType(CType(183,Byte),Integer), CType(CType(183,Byte),Integer), CType(CType(183,Byte),Integer))
        Me.MemoTextBox.EditMask = Nothing
        Me.MemoTextBox.Location = New System.Drawing.Point(3, 37)
        Me.MemoTextBox.Multiline = false
        Me.MemoTextBox.Name = "MemoTextBox"
        Me.MemoTextBox.PasswordChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.MemoTextBox.ReadOnly = false
        Me.MemoTextBox.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.MemoTextBox.SelectionStart = 0
        Me.MemoTextBox.SelectStart = 0
        Me.MemoTextBox.Size = New System.Drawing.Size(414, 23)
        Me.MemoTextBox.TabIndex = 4
        Me.MemoTextBox.TextAlign = System.Windows.Forms.HorizontalAlignment.Left
        Me.MemoTextBox.TextFont = New System.Drawing.Font("宋体", 10.5!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.MemoTextBox.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
        Me.MemoTextBox.Watermark = Nothing
        '
        'KySlNumericEdit
        '
        Me.KySlNumericEdit.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.KySlNumericEdit.Captain = "可用数量"
        Me.KySlNumericEdit.CaptainFont = New System.Drawing.Font("宋体", 10.5!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.KySlNumericEdit.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.KySlNumericEdit.CaptainWidth = 60!
        Me.TableLayoutPanel1.SetColumnSpan(Me.KySlNumericEdit, 2)
        Me.KySlNumericEdit.Location = New System.Drawing.Point(423, 8)
        Me.KySlNumericEdit.MaximumSize = New System.Drawing.Size(100000, 20)
        Me.KySlNumericEdit.MinimumSize = New System.Drawing.Size(0, 23)
        Me.KySlNumericEdit.Name = "KySlNumericEdit"
        Me.KySlNumericEdit.NumericInputKeys = CType(((((C1.Win.C1Input.NumericInputKeyFlags.F9 Or C1.Win.C1Input.NumericInputKeyFlags.Minus)  _
            Or C1.Win.C1Input.NumericInputKeyFlags.Plus)  _
            Or C1.Win.C1Input.NumericInputKeyFlags.[Decimal])  _
            Or C1.Win.C1Input.NumericInputKeyFlags.X),C1.Win.C1Input.NumericInputKeyFlags)
        Me.KySlNumericEdit.NumFont = New System.Drawing.Font("宋体", 10.5!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.KySlNumericEdit.ReadOnly = false
        Me.KySlNumericEdit.Size = New System.Drawing.Size(154, 23)
        Me.KySlNumericEdit.TabIndex = 2
        Me.KySlNumericEdit.ValueIsDbNull = false
        '
        'PbSlNumericEdit
        '
        Me.PbSlNumericEdit.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.PbSlNumericEdit.Captain = "配比数量"
        Me.PbSlNumericEdit.CaptainFont = New System.Drawing.Font("宋体", 10.5!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.PbSlNumericEdit.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.PbSlNumericEdit.CaptainWidth = 60!
        Me.TableLayoutPanel1.SetColumnSpan(Me.PbSlNumericEdit, 2)
        Me.PbSlNumericEdit.Location = New System.Drawing.Point(583, 8)
        Me.PbSlNumericEdit.MaximumSize = New System.Drawing.Size(100000, 20)
        Me.PbSlNumericEdit.MinimumSize = New System.Drawing.Size(0, 23)
        Me.PbSlNumericEdit.Name = "PbSlNumericEdit"
        Me.PbSlNumericEdit.NumericInputKeys = CType(((((C1.Win.C1Input.NumericInputKeyFlags.F9 Or C1.Win.C1Input.NumericInputKeyFlags.Minus)  _
            Or C1.Win.C1Input.NumericInputKeyFlags.Plus)  _
            Or C1.Win.C1Input.NumericInputKeyFlags.[Decimal])  _
            Or C1.Win.C1Input.NumericInputKeyFlags.X),C1.Win.C1Input.NumericInputKeyFlags)
        Me.PbSlNumericEdit.NumFont = New System.Drawing.Font("宋体", 10.5!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.PbSlNumericEdit.ReadOnly = false
        Me.PbSlNumericEdit.Size = New System.Drawing.Size(154, 23)
        Me.PbSlNumericEdit.TabIndex = 3
        Me.PbSlNumericEdit.ValueIsDbNull = false
        '
        'OKButton
        '
        Me.OKButton.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom),System.Windows.Forms.AnchorStyles)
        Me.OKButton.ButtonImageSize = CustomControl.MyButton.imageSize.small
        Me.OKButton.DialogResult = System.Windows.Forms.DialogResult.None
        Me.OKButton.Font = New System.Drawing.Font("宋体", 10.5!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.OKButton.Location = New System.Drawing.Point(430, 35)
        Me.OKButton.Margin = New System.Windows.Forms.Padding(1)
        Me.OKButton.Name = "OKButton"
        Me.OKButton.Size = New System.Drawing.Size(60, 27)
        Me.OKButton.TabIndex = 5
        Me.OKButton.Tag = "保存"
        Me.OKButton.Text = "保存"
        '
        'DelButton
        '
        Me.DelButton.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom),System.Windows.Forms.AnchorStyles)
        Me.DelButton.ButtonImageSize = CustomControl.MyButton.imageSize.small
        Me.DelButton.DialogResult = System.Windows.Forms.DialogResult.None
        Me.DelButton.Font = New System.Drawing.Font("宋体", 10.5!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.DelButton.Location = New System.Drawing.Point(510, 35)
        Me.DelButton.Margin = New System.Windows.Forms.Padding(1)
        Me.DelButton.Name = "DelButton"
        Me.DelButton.Size = New System.Drawing.Size(60, 27)
        Me.DelButton.TabIndex = 8
        Me.DelButton.Tag = "删除"
        Me.DelButton.Text = "删除"
        '
        'DyButton
        '
        Me.DyButton.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom),System.Windows.Forms.AnchorStyles)
        Me.DyButton.ButtonImageSize = CustomControl.MyButton.imageSize.small
        Me.DyButton.DialogResult = System.Windows.Forms.DialogResult.None
        Me.DyButton.Font = New System.Drawing.Font("宋体", 10.5!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.DyButton.Location = New System.Drawing.Point(590, 35)
        Me.DyButton.Margin = New System.Windows.Forms.Padding(1)
        Me.DyButton.Name = "DyButton"
        Me.DyButton.Size = New System.Drawing.Size(60, 27)
        Me.DyButton.TabIndex = 7
        Me.DyButton.Tag = "打印"
        Me.DyButton.Text = "打印"
        '
        'C1TextBox1
        '
        Me.C1TextBox1.BackColor = System.Drawing.SystemColors.Window
        Me.C1TextBox1.BorderColor = System.Drawing.SystemColors.WindowFrame
        Me.C1TextBox1.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.C1TextBox1.Location = New System.Drawing.Point(69, 9)
        Me.C1TextBox1.Name = "C1TextBox1"
        Me.C1TextBox1.Size = New System.Drawing.Size(123, 14)
        Me.C1TextBox1.TabIndex = 0
        Me.C1TextBox1.Tag = Nothing
        Me.C1TextBox1.TextDetached = true
        Me.C1TextBox1.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
        Me.C1TextBox1.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'C1TextBox2
        '
        Me.C1TextBox2.BackColor = System.Drawing.SystemColors.Info
        Me.C1TextBox2.BorderColor = System.Drawing.SystemColors.WindowFrame
        Me.C1TextBox2.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.C1TextBox2.Location = New System.Drawing.Point(276, 9)
        Me.C1TextBox2.Name = "C1TextBox2"
        Me.C1TextBox2.ReadOnly = true
        Me.C1TextBox2.Size = New System.Drawing.Size(123, 14)
        Me.C1TextBox2.TabIndex = 67
        Me.C1TextBox2.Tag = Nothing
        Me.C1TextBox2.TextDetached = true
        Me.C1TextBox2.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
        Me.C1TextBox2.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'C1TextBox3
        '
        Me.C1TextBox3.BackColor = System.Drawing.SystemColors.Info
        Me.C1TextBox3.BorderColor = System.Drawing.SystemColors.WindowFrame
        Me.C1TextBox3.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.C1TextBox3.Location = New System.Drawing.Point(476, 9)
        Me.C1TextBox3.Name = "C1TextBox3"
        Me.C1TextBox3.ReadOnly = true
        Me.C1TextBox3.Size = New System.Drawing.Size(123, 14)
        Me.C1TextBox3.TabIndex = 69
        Me.C1TextBox3.Tag = Nothing
        Me.C1TextBox3.TextDetached = true
        Me.C1TextBox3.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
        Me.C1TextBox3.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'C1TextBox4
        '
        Me.C1TextBox4.BackColor = System.Drawing.SystemColors.Info
        Me.C1TextBox4.BorderColor = System.Drawing.SystemColors.WindowFrame
        Me.C1TextBox4.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.C1TextBox4.Location = New System.Drawing.Point(69, 34)
        Me.C1TextBox4.Name = "C1TextBox4"
        Me.C1TextBox4.ReadOnly = true
        Me.C1TextBox4.Size = New System.Drawing.Size(123, 14)
        Me.C1TextBox4.TabIndex = 71
        Me.C1TextBox4.Tag = Nothing
        Me.C1TextBox4.TextDetached = true
        Me.C1TextBox4.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
        Me.C1TextBox4.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'C1TextBox5
        '
        Me.C1TextBox5.BackColor = System.Drawing.SystemColors.Info
        Me.C1TextBox5.BorderColor = System.Drawing.SystemColors.WindowFrame
        Me.C1TextBox5.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.C1TextBox5.Location = New System.Drawing.Point(276, 34)
        Me.C1TextBox5.Name = "C1TextBox5"
        Me.C1TextBox5.ReadOnly = true
        Me.C1TextBox5.Size = New System.Drawing.Size(123, 14)
        Me.C1TextBox5.TabIndex = 73
        Me.C1TextBox5.Tag = Nothing
        Me.C1TextBox5.TextDetached = true
        Me.C1TextBox5.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
        Me.C1TextBox5.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'C1TextBox6
        '
        Me.C1TextBox6.BackColor = System.Drawing.SystemColors.Info
        Me.C1TextBox6.BorderColor = System.Drawing.SystemColors.WindowFrame
        Me.C1TextBox6.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.C1TextBox6.Location = New System.Drawing.Point(476, 34)
        Me.C1TextBox6.Name = "C1TextBox6"
        Me.C1TextBox6.ReadOnly = true
        Me.C1TextBox6.Size = New System.Drawing.Size(123, 14)
        Me.C1TextBox6.TabIndex = 75
        Me.C1TextBox6.Tag = Nothing
        Me.C1TextBox6.TextDetached = true
        Me.C1TextBox6.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
        Me.C1TextBox6.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'MyGrid1
        '
        Me.MyGrid1.AllowColMove = true
        Me.MyGrid1.AllowFilter = true
        Me.MyGrid1.CanCustomCol = false
        Me.MyGrid1.Caption = ""
        Me.MyGrid1.ChildGrid = Nothing
        Me.MyGrid1.Col = 0
        Me.MyGrid1.ColumnFooters = false
        Me.MyGrid1.ColumnHeaders = true
        Me.MyGrid1.DataMember = ""
        Me.MyGrid1.DataSource = Nothing
        Me.MyGrid1.DirectionAfterEnter = C1.Win.C1TrueDBGrid.DirectionAfterEnterEnum.MoveRight
        Me.MyGrid1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.MyGrid1.FetchRowStyles = false
        Me.MyGrid1.FilterBar = false
        Me.MyGrid1.GroupByAreaVisible = true
        Me.MyGrid1.Location = New System.Drawing.Point(0, 67)
        Me.MyGrid1.Margin = New System.Windows.Forms.Padding(0)
        Me.MyGrid1.MarqueeStyle = C1.Win.C1TrueDBGrid.MarqueeEnum.HighlightRow
        Me.MyGrid1.Name = "MyGrid1"
        Me.MyGrid1.Size = New System.Drawing.Size(834, 469)
        Me.MyGrid1.TabAction = C1.Win.C1TrueDBGrid.TabActionEnum.ControlNavigation
        Me.MyGrid1.TabIndex = 39
        Me.MyGrid1.Xmlpath = Nothing
        '
        'Hsz_Syk
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6!, 12!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(834, 536)
        Me.Controls.Add(Me.MyGrid1)
        Me.Controls.Add(Me.Panel1)
        Me.Icon = CType(resources.GetObject("$this.Icon"),System.Drawing.Icon)
        Me.Name = "Hsz_Syk"
        Me.Text = "处置药品分组"
        CType(Me.C1TextBox10,System.ComponentModel.ISupportInitialize).EndInit
        CType(Me.C1TextBox9,System.ComponentModel.ISupportInitialize).EndInit
        CType(Me.C1TextBox8,System.ComponentModel.ISupportInitialize).EndInit
        CType(Me.C1TextBox7,System.ComponentModel.ISupportInitialize).EndInit
        CType(Me.C1TextBox11,System.ComponentModel.ISupportInitialize).EndInit
        CType(Me.C1TextBox13,System.ComponentModel.ISupportInitialize).EndInit
        CType(Me.C1TextBox14,System.ComponentModel.ISupportInitialize).EndInit
        CType(Me.C1TextBox15,System.ComponentModel.ISupportInitialize).EndInit
        CType(Me.C1TextBox16,System.ComponentModel.ISupportInitialize).EndInit
        CType(Me.T_Textbox,System.ComponentModel.ISupportInitialize).EndInit
        Me.Panel1.ResumeLayout(false)
        Me.TableLayoutPanel1.ResumeLayout(false)
        CType(Me.C1TextBox1,System.ComponentModel.ISupportInitialize).EndInit
        CType(Me.C1TextBox2,System.ComponentModel.ISupportInitialize).EndInit
        CType(Me.C1TextBox3,System.ComponentModel.ISupportInitialize).EndInit
        CType(Me.C1TextBox4,System.ComponentModel.ISupportInitialize).EndInit
        CType(Me.C1TextBox5,System.ComponentModel.ISupportInitialize).EndInit
        CType(Me.C1TextBox6,System.ComponentModel.ISupportInitialize).EndInit
        Me.ResumeLayout(false)

End Sub
    Friend WithEvents C1TextBox10 As C1.Win.C1Input.C1TextBox
    Friend WithEvents C1TextBox9 As C1.Win.C1Input.C1TextBox
    Friend WithEvents C1TextBox8 As C1.Win.C1Input.C1TextBox
    Friend WithEvents C1TextBox7 As C1.Win.C1Input.C1TextBox
    Friend WithEvents C1TextBox11 As C1.Win.C1Input.C1TextBox
    Friend WithEvents C1TextBox13 As C1.Win.C1Input.C1TextBox
    Friend WithEvents C1TextBox14 As C1.Win.C1Input.C1TextBox
    Friend WithEvents C1TextBox15 As C1.Win.C1Input.C1TextBox
    Friend WithEvents C1TextBox16 As C1.Win.C1Input.C1TextBox
    Friend WithEvents T_Textbox As C1.Win.C1Input.C1TextBox
    Friend WithEvents Panel1 As System.Windows.Forms.Panel
    Friend WithEvents C1TextBox1 As C1.Win.C1Input.C1TextBox
    Friend WithEvents C1TextBox2 As C1.Win.C1Input.C1TextBox
    Friend WithEvents C1TextBox3 As C1.Win.C1Input.C1TextBox
    Friend WithEvents C1TextBox4 As C1.Win.C1Input.C1TextBox
    Friend WithEvents C1TextBox5 As C1.Win.C1Input.C1TextBox
    Friend WithEvents C1TextBox6 As C1.Win.C1Input.C1TextBox
    Friend WithEvents TableLayoutPanel1 As System.Windows.Forms.TableLayoutPanel
    Friend WithEvents FzComobo As CustomControl.MySingleComobo
    Friend WithEvents YPComobo As CustomControl.MyDtComobo
    Friend WithEvents KySlNumericEdit As CustomControl.MyNumericEdit
    Friend WithEvents PbSlNumericEdit As CustomControl.MyNumericEdit
    Friend WithEvents MemoTextBox As CustomControl.MyTextBox
    Friend WithEvents OKButton As CustomControl.MyButton
    Friend WithEvents MyGrid1 As CustomControl.MyGrid
    Friend WithEvents DyButton As CustomControl.MyButton
    Friend WithEvents DelButton As CustomControl.MyButton
End Class
