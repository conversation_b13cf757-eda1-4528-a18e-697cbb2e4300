﻿Imports BaseClass
Imports System.Windows.Forms
Imports System.Drawing
Imports CustomControl
Imports Microsoft.VisualBasic.Strings
Imports C1.Win.C1Command    '待定

Public Class LIS_TestXm2

#Region "变量定义"
    Dim My_Table As New DataTable
    Dim MyView As DataView
    Dim My_Row As DataRow                                           '当 前 行
    Dim My_Cm As CurrencyManager                                    '同步指针
    Dim V_Insert, vResult As Boolean                                         '是否新增人员
    Dim Spell As New Chs2Spell

    Dim vTestXm, vDevCode As String
    Dim V_TestXm_Code As String

    Dim BllLIS_DictDev As New BLLOld.B_LIS_DictDev
    Dim BllLIS_Element2 As New BLLOld.B_LIS_Element2
    Dim ModelLIS_TestXm As New ModelOld.M_LIS_TestXm
    Dim BllLIS_TestXm As New BLLOld.B_LIS_TestXm
    Dim BllLIS_TestItem As New BLLOld.B_LIS_TestItem
#End Region

#Region "传参"
    Dim Rinsert As Boolean
    Dim Rrow As DataRow
    Dim RZbtb As DataTable
    Dim Rrc As C_RowChange
#End Region

    Public Sub New(ByVal tinsert As Boolean, ByRef trow As DataRow, ByRef tZbtb As DataTable, ByRef trc As C_RowChange)
        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()
        Rinsert = tinsert
        Rrow = trow
        RZbtb = tZbtb
        Rrc = trc

        ' 在 InitializeComponent() 调用之后添加任何初始化。
    End Sub

    Private Sub Zd_Ry4_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        If Rrc IsNot Nothing Then
            AddHandler Rrc.RowChanged, AddressOf Data_Show
        End If
        Call Form_Init()
        If Rinsert = True Then
            V_TestXm_Code = BllLIS_TestXm.MaxCode()
            Call Data_Clear()
        Else
            V_TestXm_Code = Trim(Rrow.Item("TestXm_Code") & "")
            Call Data_Show(Rrow)
        End If
        Call Init_Data()
        AddHandler m_Rc.GridMoveEvent, AddressOf GridMove
    End Sub

    Private Sub Zd_Ry4_FormClosed(ByVal sender As Object, ByVal e As System.Windows.Forms.FormClosedEventArgs) Handles Me.FormClosed
        If Rrc IsNot Nothing Then
            RemoveHandler Rrc.RowChanged, AddressOf Data_Show
        End If
        If m_Rc IsNot Nothing Then
            RemoveHandler m_Rc.GridMoveEvent, AddressOf GridMove
        End If
    End Sub

#Region "窗体__事件"

    Private Sub Form_Init()
        CodeTextBox.Enabled = False
        JcMyTextBox3.Enabled = False
        With DevMyDtComobo1
            .DataView = BllLIS_DictDev.GetList("").Tables(0).DefaultView
            .Init_Colum("Dev_Name", "仪器名称", 120, "左")
            .Init_Colum("Dev_Jc", "仪器简称", 0, "左")
            .Init_Colum("Dev_Code", "仪器编码", 0, "中")
            .DisplayMember = "Dev_Name"
            .ValueMember = "Dev_Code"
            .RowFilterNotTextNull = "Dev_Jc"
            .RowFilterTextNull = ""
            .DroupDownWidth = .Width - .CaptainWidth
            .MaxDropDownItems = 15
            .SelectedValue = -1
        End With

        With TestSampleMyDtComobo2
            .DataView = BllLIS_Element2.GetList("Elementlb_Code='003'").Tables(0).DefaultView
            .Init_Colum("Element_Name", "检验标本名称", 120, "左")
            .Init_Colum("Element_Jc", "检验标本简称", 0, "左")
            .Init_Colum("Element_Code", "检验标本编码", 0, "中")
            .DisplayMember = "Element_Name"
            .ValueMember = "Element_Code"
            .RowFilterNotTextNull = "Element_Jc"
            .RowFilterTextNull = ""
            .DroupDownWidth = .Width - .CaptainWidth
            .MaxDropDownItems = 15
            .SelectedValue = -1
        End With
        '按扭初始化
        Button1.Top = 2
        Button2.Location = New Point(Button1.Left + Button1.Width + 2, Button1.Top)

        'TestXm_Code, TestItem_Code, Item_Name, Item_Jc, Item_Dw, MaleMin, MaleMax, FemaleMin, FemaleMax, isDefault, ItemGroup, ItemOrder, Memo
        'MyGrid1初始化
        With MyGrid1
            .Clear()
            .Init_Column("小项编码", "TestItem_Code", 0, "中", "", False)
            .Init_Column("检验项目编码", "TestXm_Code", 0, "中", "", False)
            .Init_Column("小项名称", "Item_Name", "200", "中", "", False)
            .Init_Column("小项简称", "Item_Jc", "150 ", "中", "Combobox", False)
            .Init_Column("小项单位", "Item_Dw", "80", "中", "", False)
            .Init_Column("男性下限", "MaleMin", "100", "中", "", False)
            .Init_Column("男性上限", "MaleMax", "100", "中", "", False)
            .Init_Column("女性下限", "FemaleMin", "100 ", "中", "", False)
            .Init_Column("女性上限", "FemaleMax", "100 ", "中", "", False)
            .Init_Column("是否默认", "isDefault", "80", "中", "", False)
            .Init_Column("项目组合名", "ItemGroup", "140", "中", "", False)
            .Init_Column("顺序", "ItemOrder", "40", "中", "", False)
            .Init_Column("备注", "Memo", "0", "中", "", False)
            .Splits(0).DisplayColumns("isDefault").FetchStyle = True
            .MultiSelect = C1.Win.C1TrueDBGrid.MultiSelectEnum.Simple

        End With

    End Sub

    Private Sub Init_Data()

        '下面为必须
        My_Table = BllLIS_TestItem.GetList(" TestXm_Code = '" & V_TestXm_Code & "'").Tables(0)  '待定

        MyView = My_Table.DefaultView
        MyView.Sort = "ItemOrder"
        My_Table.PrimaryKey = New DataColumn() {My_Table.Columns("TestItem_Code")}
        My_Cm = CType(BindingContext(My_Table, ""), CurrencyManager)
        MyGrid1.DataTable = My_Table
        'MyGrid1.Select()
    End Sub

    Private Sub Data_Clear()
        Rinsert = True
        Move5.Enabled = False                                   '新增记录
        CodeTextBox.Text = BllLIS_TestXm.MaxCode()
        NameMyTextBox2.Text = ""
        JcMyTextBox3.Text = ""
        DevMyDtComobo1.SelectedIndex = -1
        TestSampleMyDtComobo2.SelectedIndex = -1
        ChannelMyTextBox4.Text = ""
        AccuracyMyNumericEdit1.Value = 2
        AddNumMyNumericEdit2.Value = 0
        ConversionNumMyNumericEdit3.Value = 0
        MemoTextBox5.Text = ""
        Call Show_Label()
    End Sub

    Private Sub Data_Show(ByVal tmp_Row As DataRow)
        Rinsert = False
        Move5.Enabled = True
        Rrow = tmp_Row                                   '更新Rrow
        With tmp_Row
            CodeTextBox.Text = .Item("TestXm_Code") & ""
            V_TestXm_Code = CodeTextBox.Text
            NameMyTextBox2.Text = .Item("TestXm_Name") & ""
            JcMyTextBox3.Text = .Item("TestXm_Jc") & ""
            DevMyDtComobo1.SelectedValue = .Item("Dev_Code") & ""
            TestSampleMyDtComobo2.Text = .Item("TestSample") & ""
            ChannelMyTextBox4.Text = .Item("ChannelCode") & ""
            AccuracyMyNumericEdit1.Value = .Item("Accuracy")
            AddNumMyNumericEdit2.Value = .Item("AddNum")
            ConversionNumMyNumericEdit3.Value = .Item("ConversionNum")
            MemoTextBox5.Text = .Item("Memo") & ""

            vTestXm = .Item("TestXm_Name") & ""
            vDevCode = .Item("Dev_code") & ""
        End With
        Call Show_Label()
    End Sub

    Private Sub Show_Label()
        If Rinsert = True Then
            T_Label2.Text = "新增"
        Else
            T_Label2.Text = IIf(RZbtb.Rows.Count() = 0, "0", CStr((RZbtb.Rows.IndexOf(Rrow)) + 1))

        End If
        T_Label3.Text = "∑=" + RZbtb.Rows.Count.ToString
        Me.NameMyTextBox2.Select()
    End Sub

#End Region

#Region "动作__函数"

    Private Sub GridMove(ByVal s As String)
        With MyGrid1
            If MyGrid1.RowCount = 0 Then Exit Sub
            Select Case s
                Case "最前"
                    .MoveFirst()
                Case "上移"
                    .MovePrevious()
                Case "下移"
                    .MoveNext()
                Case "最后"
                    .MoveLast()

            End Select
        End With
    End Sub


#End Region

#Region "控件__动作"
    Private Sub NameMyTextBox2_Validated(ByVal sender As Object, ByVal e As System.EventArgs) Handles NameMyTextBox2.Validated
        JcMyTextBox3.Text = Spell.GetPy(NameMyTextBox2.Text & "")
    End Sub

    Private Sub Button_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Button1.Click, Button2.Click
        Select Case sender.tag
            Case "保存"
                If Trim(NameMyTextBox2.Text & "") = "" Then
                    MsgBox("请填写检验项目名称！", MsgBoxStyle.Critical + MsgBoxStyle.OkOnly, "提示:")
                    NameMyTextBox2.Select()
                    Exit Sub
                End If
                If DevMyDtComobo1.SelectedValue & "" = "" Then
                    MsgBox("仪器必须选择！", MsgBoxStyle.Critical + MsgBoxStyle.OkOnly, "提示:")
                    DevMyDtComobo1.Select()
                    Exit Sub
                End If
                vTestXm = NameMyTextBox2.Text

                If Rinsert = True Then                    '增加记录
                    If BllLIS_TestXm.GetRecordCount("TestXm_Name='" & vTestXm & "'") > 0 Then
                        MsgBox("检验项目名称已存在！", MsgBoxStyle.Critical + MsgBoxStyle.OkOnly, "提示:")
                        NameMyTextBox2.Select()
                        Exit Sub
                    End If
                    Call Data_Add()
                    Call Data_Clear()
                Else                    '编辑记录
                    If BllLIS_TestXm.GetRecordCount("TestXm_Name='" & vTestXm & "' and TestXm_Code<>'" & V_TestXm_Code & "'") > 0 Then
                        MsgBox("检验项目名称已存在！", MsgBoxStyle.Critical + MsgBoxStyle.OkOnly, "提示:")
                        NameMyTextBox2.Select()
                        Exit Sub
                    End If
                    Call Data_Edit()
                End If
            Case "取消"
                Me.Close()
        End Select
    End Sub

    Private Sub Comm_Click(ByVal sender As Object, ByVal e As C1.Win.C1Command.ClickEventArgs) Handles Comm1.Click, Comm2.Click, Comm3.Click,
        Comm4.Click, Comm5.Click


        Select Case sender.text
            Case "增加", "保存"
                vResult = False
                If Trim(NameMyTextBox2.Text & "") = "" Then
                    MsgBox("请填写检验项目名称！", MsgBoxStyle.Critical + MsgBoxStyle.OkOnly, "提示:")
                    NameMyTextBox2.Select()
                    Exit Sub
                End If
                If DevMyDtComobo1.SelectedValue & "" = "" Then
                    MsgBox("仪器必须选择！", MsgBoxStyle.Critical + MsgBoxStyle.OkOnly, "提示:")
                    DevMyDtComobo1.Select()
                    Exit Sub
                End If
                vTestXm = NameMyTextBox2.Text

                If Rinsert = True Then                    '增加记录
                    If BllLIS_TestXm.GetRecordCount("TestXm_Name='" & vTestXm & "'") > 0 Then
                        MsgBox("检验项目名称已存在！", MsgBoxStyle.Critical + MsgBoxStyle.OkOnly, "提示:")
                        NameMyTextBox2.Select()
                        Exit Sub
                    End If
                    Call Data_Add()

                    Rinsert = False
                Else                    '编辑记录
                    If BllLIS_TestXm.GetRecordCount("TestXm_Name='" & vTestXm & "' and TestXm_Code<>'" & V_TestXm_Code & "'") > 0 Then
                        MsgBox("检验项目名称已存在！", MsgBoxStyle.Critical + MsgBoxStyle.OkOnly, "提示:")
                        NameMyTextBox2.Select()
                        Exit Sub
                    End If
                    Call Data_Edit()
                End If
                vResult = True
                If sender.Text = "增加" Then
                    Call P_ShowData("增加")
                End If

            Case "删除"
                Beep()
                If MyGrid1.Splits(0).Rows.Count = 0 Then Exit Sub
                Call P_Del_Data()
            Case "刷新"
                '初始化
                Call Init_Data()
                MyGrid1.Select()
                MyGrid1.MoveFirst()
            Case "上移"
                Dim m1, m2 As ModelOld.M_LIS_TestItem
                Dim s As String
                Dim intR As Int16 = MyGrid1.Row
                If intR <= 0 Then
                    MsgBox("无法继续上移！", MsgBoxStyle.Critical + MsgBoxStyle.OkOnly, "提示:")
                    Exit Sub
                End If
                m1 = BllLIS_TestItem.GetModel(MyGrid1.Columns("TestItem_Code").CellValue(intR))
                m2 = BllLIS_TestItem.GetModel(MyGrid1.Columns("TestItem_Code").CellValue(intR - 1))
                If BllLIS_TestItem.ExchangeOrder(m1, m2) = True Then
                    ExchangeRow(My_Table.Rows.Find(MyGrid1.Columns("TestItem_Code").CellValue(intR)),
                                                    My_Table.Rows.Find(MyGrid1.Columns("TestItem_Code").CellValue(intR - 1)))
                End If
            Case "下移"
                Dim m1, m2 As ModelOld.M_LIS_TestItem
                Dim s As String
                Dim intR As Int16 = MyGrid1.Row
                If intR >= MyGrid1.RowCount - 1 Then
                    MsgBox("无法继续下移！", MsgBoxStyle.Critical + MsgBoxStyle.OkOnly, "提示:")
                    Exit Sub
                End If
                m1 = BllLIS_TestItem.GetModel(MyGrid1.Columns("TestItem_Code").CellValue(intR))
                m2 = BllLIS_TestItem.GetModel(MyGrid1.Columns("TestItem_Code").CellValue(intR + 1))
                If BllLIS_TestItem.ExchangeOrder(m1, m2) = True Then
                    ExchangeRow(My_Table.Rows.Find(MyGrid1.Columns("TestItem_Code").CellValue(intR)),
                                My_Table.Rows.Find(MyGrid1.Columns("TestItem_Code").CellValue(intR + 1)))
                End If

        End Select

    End Sub

    Private Sub ExchangeRow(ByVal r1 As DataRow, ByVal r2 As DataRow)
        Dim obj As Object
        obj = r1.Item("ItemOrder")
        r1.Item("ItemOrder") = r2.Item("ItemOrder")
        r2.Item("ItemOrder") = obj
    End Sub

    Private Sub Move_Click(ByVal sender As Object, ByVal e As C1.Win.C1Command.ClickEventArgs) Handles Move1.Click, Move2.Click, Move3.Click, Move4.Click, Move5.Click
        If sender.text = "新增" Then                            '增加状态
            Call Data_Clear()
            My_Table.Clear() '待定
        Else
            Rrc.GridMove(sender.text)
            Init_Data()
        End If
    End Sub

    Private Sub MyGrid1_FetchCellStyle(sender As Object, e As C1.Win.C1TrueDBGrid.FetchCellStyleEventArgs) Handles MyGrid1.FetchCellStyle
        If e.Column.Name = "是否默认" Then
            If MyGrid1.Columns("是否默认").CellValue(e.Row) = True Then
                e.CellStyle.ForegroundImage = ZtHis.Lis.My.Resources.否
            Else
                e.CellStyle.ForegroundImage = ZtHis.Lis.My.Resources.是
            End If
            e.CellStyle.ForeGroundPicturePosition = C1.Win.C1TrueDBGrid.ForeGroundPicturePositionEnum.PictureOnly
        End If
    End Sub

    Private Sub MyGrid1_MouseUp(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles MyGrid1.MouseUp
        If e.Button = Windows.Forms.MouseButtons.Right Then Call Comm_Click(Button1, Nothing) : If vResult = True Then Call P_ShowData("DBGrid")
    End Sub

    Private Sub MyGrid1_KeyDown(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles MyGrid1.KeyDown
        Select Case e.KeyCode
            Case Keys.Return
                Call Comm_Click(Button1, Nothing)
                If vResult = True Then Call P_ShowData("DBGrid")
            Case Keys.Insert
                Call Comm_Click(Button1, Nothing)
                If vResult = True Then Call P_ShowData("增加")
            Case Keys.Delete
                Call Comm_Click(Comm2, Nothing)
        End Select
    End Sub



    Dim m_Rc As New BaseClass.C_RowChange
    Private Sub MyGrid1_RowColChange(ByVal sender As System.Object, ByVal e As C1.Win.C1TrueDBGrid.RowColChangeEventArgs) Handles MyGrid1.RowColChange
        If (MyGrid1.Row + 1) > MyGrid1.RowCount Then
        Else
            My_Row = My_Cm.List(MyGrid1.Row).Row
            m_Rc.ChangeRow(My_Row)
        End If
    End Sub

    Private Sub ZhongWen_TextBox_GotFocus(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles NameMyTextBox2.GotFocus, MemoTextBox5.GotFocus
        InputLanguage.CurrentInputLanguage = HisVar.HisVar.InputCode
    End Sub
    Private Sub DevMyDtComobo1_TextBox_GotFocus(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles DevMyDtComobo1.GotFocus, TestSampleMyDtComobo2.GotFocus
        InputLanguage.CurrentInputLanguage = InputLanguage.InstalledInputLanguages(0)
    End Sub
#End Region

#Region "数据__操作"
    '待定
    Private Sub Data_Add()
        'TestXm_Code, TestXm_Name, TestXm_Jc, lis_testxm.Dev_Code, Dev_Name, TestSample,
        'ChannelCode, Accuracy, AddNum, ConversionNum, TestXmRpt, lis_testxm.Memo
        Dim My_NewRow As DataRow = RZbtb.NewRow
        With My_NewRow
            .Item("TestXm_Code") = Trim(CodeTextBox.Text & "")
            .Item("TestXm_Name") = NameMyTextBox2.Text
            .Item("TestXm_Jc") = JcMyTextBox3.Text
            .Item("Dev_code") = DevMyDtComobo1.SelectedValue
            .Item("Dev_Name") = Trim(DevMyDtComobo1.Text & "")
            .Item("TestSample") = Trim(TestSampleMyDtComobo2.Text & "")
            .Item("ChannelCode") = ChannelMyTextBox4.Text
            .Item("Accuracy") = AccuracyMyNumericEdit1.Value
            .Item("AddNum") = AddNumMyNumericEdit2.Value
            .Item("TestXmRpt") = Nothing
            .Item("ConversionNum") = ConversionNumMyNumericEdit3.Value
            .Item("Memo") = MemoTextBox5.Text
        End With
        V_TestXm_Code = Trim(CodeTextBox.Text & "")
        '数据保存
        Try
            RZbtb.Rows.Add(My_NewRow)

        Catch ex As Exception
            Beep()
            MsgBox(ex.Message, MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
            Exit Sub
        Finally
        End Try

        '数据更新
        Try
            With ModelLIS_TestXm
                .TestXm_Code = My_NewRow.Item("TestXm_Code")
                .TestXm_Name = My_NewRow.Item("TestXm_Name")
                .TestXm_Jc = My_NewRow.Item("TestXm_Jc")
                .Dev_Code = My_NewRow.Item("Dev_code")
                .TestSample = My_NewRow.Item("TestSample")
                .ChannelCode = My_NewRow.Item("ChannelCode")
                .Accuracy = My_NewRow.Item("Accuracy")
                .AddNum = My_NewRow.Item("AddNum")
                .ConversionNum = My_NewRow.Item("ConversionNum")
                .Memo = My_NewRow.Item("Memo")
            End With
            BllLIS_TestXm.Add(ModelLIS_TestXm)
            My_NewRow.AcceptChanges()
            RZbtb.AcceptChanges()
            If RZbtb.Rows.Count = 1 Then
                Rrow = My_NewRow
            End If
            Rrc.GridMove("最后")
        Catch ex As Exception
            Beep()
            MsgBox(ex.Message, MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
            DevMyDtComobo1.Select()
            Exit Sub
        Finally
        End Try

        '数据清空

        'Call Data_Clear()
    End Sub

    Private Sub Data_Edit()
        Dim My_Row As DataRow = Rrow
        Try
            With My_Row
                .BeginEdit()
                .Item("TestXm_Code") = Trim(CodeTextBox.Text & "")
                .Item("TestXm_Name") = NameMyTextBox2.Text
                .Item("TestXm_Jc") = JcMyTextBox3.Text
                .Item("Dev_code") = DevMyDtComobo1.SelectedValue
                .Item("Dev_Name") = Trim(DevMyDtComobo1.Text & "")
                .Item("TestSample") = Trim(TestSampleMyDtComobo2.Text & "")
                .Item("ChannelCode") = ChannelMyTextBox4.Text
                .Item("Accuracy") = AccuracyMyNumericEdit1.Value
                .Item("AddNum") = AddNumMyNumericEdit2.Value
                .Item("TestXmRpt") = Nothing
                .Item("ConversionNum") = ConversionNumMyNumericEdit3.Value
                .Item("Memo") = MemoTextBox5.Text
                vTestXm = .Item("TestXm_Name") & ""
                vDevCode = .Item("Dev_code") & ""
                .EndEdit()
            End With
            V_TestXm_Code = Trim(CodeTextBox.Text & "")
        Catch ex As Exception
            Beep()
            MsgBox(ex.Message, MsgBoxStyle.Critical, "提示:")
            My_Row.CancelEdit()
            Exit Sub
        Finally

        End Try

        '数据更新
        Try
            With ModelLIS_TestXm
                .TestXm_Code = My_Row.Item("TestXm_Code")
                .TestXm_Name = My_Row.Item("TestXm_Name")
                .TestXm_Jc = My_Row.Item("TestXm_Jc")
                .Dev_Code = My_Row.Item("Dev_code")
                .TestSample = My_Row.Item("TestSample")
                .ChannelCode = My_Row.Item("ChannelCode")
                .Accuracy = My_Row.Item("Accuracy")
                .AddNum = My_Row.Item("AddNum")
                .ConversionNum = My_Row.Item("ConversionNum")
                .Memo = My_Row.Item("Memo")
            End With
            BllLIS_TestXm.Update(ModelLIS_TestXm)
            My_Row.AcceptChanges()
            RZbtb.AcceptChanges()
        Catch ex As Exception
            Beep()
            MsgBox(ex.Message, MsgBoxStyle.Critical + MsgBoxStyle.OkOnly, "提示:")
        Finally
            Me.NameMyTextBox2.Select()
        End Try
    End Sub

    Private Sub P_Del_Data()
        Beep()
        My_Row = My_Cm.List(MyGrid1.Row).Row
        If My_Row.Item("isDefault") = True Then
            MsgBox("无法删除" + Me.MyGrid1.Columns("Item_Name").Value + " ", MsgBoxStyle.Information + MsgBoxStyle.OkOnly, "提示:")
            Exit Sub
        Else
            Dim Code As String
            Try
                If MsgBox("是否删除：小项名称 = " + Me.MyGrid1.Columns("Item_Name").Value + " ", MsgBoxStyle.Information + MsgBoxStyle.OkCancel, "提示:") = MsgBoxResult.Cancel Then Exit Sub
                Code = My_Row.Item("TestItem_Code")
                BllLIS_TestItem.Delete(Code)
                MyGrid1.Delete()
                My_Row.AcceptChanges()
                T_Label.Text = "∑=" + MyGrid1.Splits(0).Rows.Count.ToString
            Catch ex As Exception
                Beep()
                MsgBox("错误:" + ex.Message, MsgBoxStyle.Critical + MsgBoxStyle.OkOnly, "提示:")
            Finally
                MyGrid1.Select()
            End Try
        End If
    End Sub

#End Region

#Region "自定义函数"

    Private Sub P_ShowData(ByVal V_Lb As String)
        If V_Lb = "增加" Then
            V_Insert = True
        Else
            If MyGrid1.RowCount > 0 Then V_Insert = False Else V_Insert = True
        End If

        If V_Insert = False Then
            My_Row = My_Cm.List(MyGrid1.Row).Row
        End If

        Dim vform As Form = New LIS_TestItem(V_Insert, My_Row, My_Table, m_Rc, V_TestXm_Code)
        vform.Owner = Me
        vform.StartPosition = FormStartPosition.CenterScreen
        vform.ShowDialog()
        MyGrid1.Select()
    End Sub

#End Region


End Class
