﻿<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<StiSerializer version="1.02" type="Net" application="StiReport">
  <Dictionary Ref="1" type="Dictionary" isKey="true">
    <BusinessObjects isList="true" count="0" />
    <Databases isList="true" count="0" />
    <DataSources isList="true" count="2">
      <内蒙古通辽门诊票据 Ref="2" type="DataTableSource" isKey="true">
        <Alias>内蒙古通辽门诊票据</Alias>
        <Columns isList="true" count="2">
          <value>Mz_Money,System.Decimal</value>
          <value>Mz_lb,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Name>内蒙古通辽门诊票据</Name>
        <NameInSource>内蒙古通辽门诊票据</NameInSource>
      </内蒙古通辽门诊票据>
      <明细表 Ref="3" type="DataTableSource" isKey="true">
        <Alias>明细表</Alias>
        <Columns isList="true" count="5">
          <value>V_Order,System.String</value>
          <value>Cf_Sl,System.Decimal</value>
          <value>Cf_Money,System.Decimal</value>
          <value>Cf_Lb,System.String</value>
          <value>Bl_Code,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Name>明细表</Name>
        <NameInSource>明细表</NameInSource>
      </明细表>
    </DataSources>
    <Relations isList="true" count="0" />
    <Report isRef="0" />
    <Variables isList="true" count="15">
      <value>,医院名称,医院名称,System.String,,False,False</value>
      <value>,姓名,姓名,System.String,,False,False</value>
      <value>,科别,科别,System.String,,False,False</value>
      <value>,打印时间,打印时间,System.String,,False,False</value>
      <value>,大写,大写,System.String,,False,False</value>
      <value>,住院编码,住院编码,System.String,,False,False</value>
      <value>,经手人,经手人,System.String,,False,False</value>
      <value>,医生,医生,System.String,,False,False</value>
      <value>,住院费用,住院费用,System.String,,False,False</value>
      <value>,住院押金,住院押金,System.String,,False,False</value>
      <value>,退找金额,退找金额,System.String,,False,False</value>
      <value>,入院日期,入院日期,System.String,,False,False</value>
      <value>,出院日期,出院日期,System.String,,False,False</value>
      <value>,住院天数,住院天数,System.String,,False,False</value>
      <value>,病例编码,病例编码,System.String,,False,False</value>
    </Variables>
  </Dictionary>
  <EngineVersion>EngineV2</EngineVersion>
  <GlobalizationStrings isList="true" count="0" />
  <MetaTags isList="true" count="0" />
  <Pages isList="true" count="1">
    <Page1 Ref="4" type="Page" isKey="true">
      <Border>None;Black;0;Solid;False;4;Black</Border>
      <Brush>Transparent</Brush>
      <Components isList="true" count="3">
        <ReportTitleBand1 Ref="5" type="ReportTitleBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,0.4,17,3.45</ClientRectangle>
          <Components isList="true" count="22">
            <Text1 Ref="6" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,17,0.9</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>楷体_GB2312,15.75,Underline</Font>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text1</Name>
              <Page isRef="4" />
              <Parent isRef="5" />
              <Text>通 辽 市 医 疗 单 位 住 院 医 疗 收 费 票 据</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text1>
            <Text2 Ref="7" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0.9,1,1.6,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text2</Name>
              <Page isRef="4" />
              <Parent isRef="5" />
              <Text>收费医院:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text2>
            <Text3 Ref="8" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>11.4,1,0.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>黑体,15,Bold,Point,False,134</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text3</Name>
              <Page isRef="4" />
              <Parent isRef="5" />
              <Text>№</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text3>
            <Text5 Ref="9" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0.9,2.2,1.6,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>f0edda5a8f9749aba5b1eb4a6a827cde</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text5</Name>
              <Page isRef="4" />
              <Parent isRef="5" />
              <Text>患者姓名:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text5>
            <Text35 Ref="10" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>2.5,1,6.2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>858177b73b064fa3abf6743d4ac44d6c</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text35</Name>
              <Page isRef="4" />
              <Parent isRef="5" />
              <Text>{医院名称}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text35>
            <Text36 Ref="11" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>2.5,2.2,2.9,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>c1b2048370484437949a390ac496199f</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text36</Name>
              <Page isRef="4" />
              <Parent isRef="5" />
              <Text>{姓名}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text36>
            <Text38 Ref="12" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>12.1,1,3.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>黑体,15,Bold,Point,False,134</Font>
              <Guid>116013a7a5994fa497e9b7aa4d4a0499</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text38</Name>
              <Page isRef="4" />
              <Parent isRef="5" />
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text38>
            <Text80 Ref="13" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>13.1,2.2,3.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>984d2b720ace4a519467d236874cc13f</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text80</Name>
              <Page isRef="4" />
              <Parent isRef="5" />
              <Text>{打印时间}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="14" type="CustomFormat" isKey="true">
                <StringFormat>yyyy-MM-dd HH:mm:ss</StringFormat>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text80>
            <Text6 Ref="15" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>11.1,2.2,1.6,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>4a4d04ee7ff84d5bae45d62379812144</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text6</Name>
              <Page isRef="4" />
              <Parent isRef="5" />
              <Text>结算时间:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text6>
            <Text9 Ref="16" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>5.4,1.6,1.6,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>8d21e13e89f0430d91247c098f35ddda</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text9</Name>
              <Page isRef="4" />
              <Parent isRef="5" />
              <Text>住院科室:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text9>
            <Text15 Ref="17" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>7,1.6,4,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>54e869e2bc1749c9bc79a5ca638f8e49</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text15</Name>
              <Page isRef="4" />
              <Parent isRef="5" />
              <Text>{科别}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text15>
            <Text16 Ref="18" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>5.4,2.2,1.6,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>f7761dee512040ab878607649e9614e2</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text16</Name>
              <Page isRef="4" />
              <Parent isRef="5" />
              <Text>住院号:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text16>
            <Text18 Ref="19" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>7,2.2,4,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>7f24c5200a524bbfa1472aa2a4f59b69</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text18</Name>
              <Page isRef="4" />
              <Parent isRef="5" />
              <Text>{病例编码}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text18>
            <Text19 Ref="20" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>11.1,1.6,2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>a9bf4919418c44399a67beee6d85ea9c</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text19</Name>
              <Page isRef="4" />
              <Parent isRef="5" />
              <Text>预交款合计:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text19>
            <Text20 Ref="21" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>13.1,1.6,3.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>ef6c15546afc40e39cfdf254b8c12b8c</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text20</Name>
              <Page isRef="4" />
              <Parent isRef="5" />
              <Text>{住院押金}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text20>
            <Text4 Ref="22" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,2.8,1.5,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>5cc3a6bdb2644c8f9c571481a76a60a2</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text4</Name>
              <Page isRef="4" />
              <Parent isRef="5" />
              <Text>入院:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text4>
            <Text13 Ref="23" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>1.5,2.8,4,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>582ac7b2c54e4de2af369949fbd24860</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text13</Name>
              <Page isRef="4" />
              <Parent isRef="5" />
              <Text>{入院日期}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text13>
            <Text17 Ref="24" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>12.5,2.8,1.6,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>a8303cbdff3a4d6388b4a48ce1ead23e</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text17</Name>
              <Page isRef="4" />
              <Parent isRef="5" />
              <Text>{住院天数}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="25" type="CustomFormat" isKey="true">
                <StringFormat>yyyy-MM-dd HH:mm:ss</StringFormat>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text17>
            <Text23 Ref="26" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>10.5,2.8,2,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>529497000a4a4c17b77dff802bf7cca2</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text23</Name>
              <Page isRef="4" />
              <Parent isRef="5" />
              <Text>共住院:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text23>
            <Text24 Ref="27" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>5.5,2.8,1,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>95bf1700b5a8437f9f34c1f15b2aa05e</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text24</Name>
              <Page isRef="4" />
              <Parent isRef="5" />
              <Text>出院:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text24>
            <Text25 Ref="28" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>6.5,2.8,4,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>d067a032c337466db7d237a91412072f</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text25</Name>
              <Page isRef="4" />
              <Parent isRef="5" />
              <Text>{出院日期}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text25>
            <Text26 Ref="29" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>14,2.8,3,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>7966daf8ac32410ba4d64ab64d52780a</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text26</Name>
              <Page isRef="4" />
              <Parent isRef="5" />
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text26>
          </Components>
          <Conditions isList="true" count="0" />
          <Name>ReportTitleBand1</Name>
          <Page isRef="4" />
          <Parent isRef="4" />
        </ReportTitleBand1>
        <DataBand1 Ref="30" type="DataBand" isKey="true">
          <Brush>Transparent</Brush>
          <BusinessObjectGuid isNull="true" />
          <ClientRectangle>0,4.65,17,0.5</ClientRectangle>
          <Columns>5</Columns>
          <Components isList="true" count="2">
            <Text8 Ref="31" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>1.6,0,1.6,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>18f75520972a4579adb956d01683cdd4</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text8</Name>
              <NullValue>     </NullValue>
              <Page isRef="4" />
              <Parent isRef="30" />
              <Text>{IIF(明细表.Cf_Money==0,null,明细表.Cf_Money)}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="32" type="CustomFormat" isKey="true">
                <StringFormat>#0.00</StringFormat>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text8>
            <Text7 Ref="33" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,1.6,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>7e87434b6fdc4fb2905bbf24b85eb429</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text7</Name>
              <Page isRef="4" />
              <Parent isRef="30" />
              <Text>{明细表.Cf_Lb}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="34" type="CustomFormat" isKey="true">
                <StringFormat>#0.00</StringFormat>
              </TextFormat>
              <Type>DataColumn</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text7>
          </Components>
          <Conditions isList="true" count="0" />
          <DataRelationName isNull="true" />
          <DataSourceName>明细表</DataSourceName>
          <Filters isList="true" count="0" />
          <Name>DataBand1</Name>
          <Page isRef="4" />
          <Parent isRef="4" />
          <Sort isList="true" count="0" />
        </DataBand1>
        <ReportSummaryBand1 Ref="35" type="ReportSummaryBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,5.95,17,1</ClientRectangle>
          <Components isList="true" count="10">
            <Text10 Ref="36" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>9,0,2,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>6a222a1f19a3474790fec7eefacdb9fc</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text10</Name>
              <Page isRef="4" />
              <Parent isRef="35" />
              <Text>合计(小写)</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text10>
            <Text12 Ref="37" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0.5,2.5,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>afc7f53ca172430d9ac45e5292b24533</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text12</Name>
              <Page isRef="4" />
              <Parent isRef="35" />
              <Text>经办人:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text12>
            <Text14 Ref="38" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>9,0.5,2.4,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>c2eec0ebd56941ff988510699a872851</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text14</Name>
              <Page isRef="4" />
              <Parent isRef="35" />
              <Text>机制号:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text14>
            <Text33 Ref="39" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,2.5,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>7fd46407a8db4c948c08b6f7a061da9a</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text33</Name>
              <Page isRef="4" />
              <Parent isRef="35" />
              <Text>合计(大写)</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text33>
            <Text45 Ref="40" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>2.5,0,6.5,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>25f9c6a646ae4ace97474d345e538d17</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text45</Name>
              <Page isRef="4" />
              <Parent isRef="35" />
              <Text>{大写}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text45>
            <Text81 Ref="41" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>11.4,0.5,5.4,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>17e039b6553a4be8be60a1b6e497eb9e</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text81</Name>
              <Page isRef="4" />
              <Parent isRef="35" />
              <Text>{住院编码}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text81>
            <Text82 Ref="42" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>2.5,0.5,6.5,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>5ff1452f8459461d937744eb934c04de</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text82</Name>
              <Page isRef="4" />
              <Parent isRef="35" />
              <Text>{经手人}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text82>
            <Text11 Ref="43" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>11,0,2.4,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>b6ea85f5701843c5bee6fca5a1c1cccf</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text11</Name>
              <Page isRef="4" />
              <Parent isRef="35" />
              <Text>{住院费用}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="44" type="CustomFormat" isKey="true">
                <StringFormat>#0.00</StringFormat>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text11>
            <Text21 Ref="45" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>13.4,0,1.2,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>148b1967bdfc400a83ae89792e2ccb8c</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text21</Name>
              <Page isRef="4" />
              <Parent isRef="35" />
              <Text>退/补款</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text21>
            <Text22 Ref="46" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>14.6,0,2.3,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>555093b90a32408f95c3a7b86e851de1</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text22</Name>
              <Page isRef="4" />
              <Parent isRef="35" />
              <Text>{退找金额}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="47" type="CustomFormat" isKey="true">
                <StringFormat>#0.00</StringFormat>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text22>
          </Components>
          <Conditions isList="true" count="0" />
          <Name>ReportSummaryBand1</Name>
          <Page isRef="4" />
          <Parent isRef="4" />
        </ReportSummaryBand1>
      </Components>
      <Conditions isList="true" count="0" />
      <Guid>006e6db275c0490a978068679f5ede10</Guid>
      <LargeHeight>True</LargeHeight>
      <Margins>1.55,1.55,0,0</Margins>
      <Name>Page1</Name>
      <PageHeight>9.3</PageHeight>
      <PageWidth>20.1</PageWidth>
      <Report isRef="0" />
      <Watermark Ref="48" type="Stimulsoft.Report.Components.StiWatermark" isKey="true">
        <Font>Arial,100</Font>
        <TextBrush>[50:0:0:0]</TextBrush>
      </Watermark>
    </Page1>
  </Pages>
  <PrinterSettings Ref="49" type="Stimulsoft.Report.Print.StiPrinterSettings" isKey="true" />
  <ReferencedAssemblies isList="true" count="8">
    <value>System.Dll</value>
    <value>System.Drawing.Dll</value>
    <value>System.Windows.Forms.Dll</value>
    <value>System.Data.Dll</value>
    <value>System.Xml.Dll</value>
    <value>Stimulsoft.Controls.Dll</value>
    <value>Stimulsoft.Base.Dll</value>
    <value>Stimulsoft.Report.Dll</value>
  </ReferencedAssemblies>
  <ReportAlias>内蒙古通辽住院票据</ReportAlias>
  <ReportChanged>7/2/2012 9:19:18 AM</ReportChanged>
  <ReportCreated>2/16/2012 3:42:02 PM</ReportCreated>
  <ReportFile>D:\正在进行时\唐山\his2010\His2010\Rpt\内蒙古通辽住院票据.mrt</ReportFile>
  <ReportGuid>27a5806f4fec4d2e92d1a4fecd3dfa03</ReportGuid>
  <ReportName>内蒙古通辽住院票据</ReportName>
  <ReportUnit>Centimeters</ReportUnit>
  <ReportVersion>2011.2.1026</ReportVersion>
  <Script>using System;
using System.Drawing;
using System.Windows.Forms;
using System.Data;
using Stimulsoft.Controls;
using Stimulsoft.Base.Drawing;
using Stimulsoft.Report;
using Stimulsoft.Report.Dialogs;
using Stimulsoft.Report.Components;

namespace Reports
{
    public class Report : Stimulsoft.Report.StiReport
    {
        public Report()        {
            this.InitializeComponent();
        }

        #region StiReport Designer generated code - do not modify
        #endregion StiReport Designer generated code - do not modify
    }
}
</Script>
  <ScriptLanguage>CSharp</ScriptLanguage>
  <Styles isList="true" count="0" />
</StiSerializer>