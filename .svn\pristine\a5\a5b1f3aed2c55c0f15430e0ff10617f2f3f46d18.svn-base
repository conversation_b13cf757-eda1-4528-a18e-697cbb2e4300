﻿/**  版本信息模板在安装目录下，可自行修改。
* D_Zd_Qx1.cs
*
* 功 能： N/A
* 类 名： D_Zd_Qx1
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2016-08-15 09:56:32   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
namespace DAL
{
	/// <summary>
	/// 数据访问类:D_Zd_Qx1
	/// </summary>
	public partial class D_Zd_Qx1
	{
		public D_Zd_Qx1()
		{}
		#region  BasicMethod

		/// <summary>
		/// 是否存在该记录
		/// </summary>
		public bool Exists(string Glz_Code)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) from Zd_Qx1");
			strSql.Append(" where Glz_Code=@Glz_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Glz_Code", SqlDbType.Char,7)			};
			parameters[0].Value = Glz_Code;

			return HisVar.HisVar.Sqldal.Exists(strSql.ToString(),parameters);
		}


		/// <summary>
		/// 增加一条数据
		/// </summary>
		public bool Add(ModelOld.M_Zd_Qx1 model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("insert into Zd_Qx1(");
			strSql.Append("Yy_Code,Glz_Code,Glz_Name,Glz_Jc,Glz_Memo)");
			strSql.Append(" values (");
			strSql.Append("@Yy_Code,@Glz_Code,@Glz_Name,@Glz_Jc,@Glz_Memo)");
			SqlParameter[] parameters = {
					new SqlParameter("@Yy_Code", SqlDbType.Char,4),
					new SqlParameter("@Glz_Code", SqlDbType.Char,7),
					new SqlParameter("@Glz_Name", SqlDbType.VarChar,50),
					new SqlParameter("@Glz_Jc", SqlDbType.VarChar,50),
					new SqlParameter("@Glz_Memo", SqlDbType.VarChar,50)};
			parameters[0].Value = model.Yy_Code;
			parameters[1].Value = model.Glz_Code;
			parameters[2].Value = model.Glz_Name;
			parameters[3].Value = model.Glz_Jc;
			parameters[4].Value = model.Glz_Memo;

			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 更新一条数据
		/// </summary>
		public bool Update(ModelOld.M_Zd_Qx1 model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("update Zd_Qx1 set ");
			strSql.Append("Yy_Code=@Yy_Code,");
			strSql.Append("Glz_Name=@Glz_Name,");
			strSql.Append("Glz_Jc=@Glz_Jc,");
			strSql.Append("Glz_Memo=@Glz_Memo");
			strSql.Append(" where Glz_Code=@Glz_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Yy_Code", SqlDbType.Char,4),
					new SqlParameter("@Glz_Name", SqlDbType.VarChar,50),
					new SqlParameter("@Glz_Jc", SqlDbType.VarChar,50),
					new SqlParameter("@Glz_Memo", SqlDbType.VarChar,50),
					new SqlParameter("@Glz_Code", SqlDbType.Char,7)};
			parameters[0].Value = model.Yy_Code;
			parameters[1].Value = model.Glz_Name;
			parameters[2].Value = model.Glz_Jc;
			parameters[3].Value = model.Glz_Memo;
			parameters[4].Value = model.Glz_Code;

			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool Delete(string Glz_Code)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from Zd_Qx1 ");
			strSql.Append(" where Glz_Code=@Glz_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Glz_Code", SqlDbType.Char,7)			};
			parameters[0].Value = Glz_Code;

			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 批量删除数据
		/// </summary>
		public bool DeleteList(string Glz_Codelist )
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from Zd_Qx1 ");
			strSql.Append(" where Glz_Code in ("+Glz_Codelist + ")  ");
			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString());
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public ModelOld.M_Zd_Qx1 GetModel(string Glz_Code)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select  top 1 Yy_Code,Glz_Code,Glz_Name,Glz_Jc,Glz_Memo from Zd_Qx1 ");
			strSql.Append(" where Glz_Code=@Glz_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Glz_Code", SqlDbType.Char,7)			};
			parameters[0].Value = Glz_Code;

			ModelOld.M_Zd_Qx1 model=new ModelOld.M_Zd_Qx1();
			DataSet ds=HisVar.HisVar.Sqldal.Query(strSql.ToString(),parameters);
			if(ds.Tables[0].Rows.Count>0)
			{
				return DataRowToModel(ds.Tables[0].Rows[0]);
			}
			else
			{
				return null;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public ModelOld.M_Zd_Qx1 DataRowToModel(DataRow row)
		{
			ModelOld.M_Zd_Qx1 model=new ModelOld.M_Zd_Qx1();
			if (row != null)
			{
				if(row["Yy_Code"]!=null)
				{
					model.Yy_Code=row["Yy_Code"].ToString();
				}
				if(row["Glz_Code"]!=null)
				{
					model.Glz_Code=row["Glz_Code"].ToString();
				}
				if(row["Glz_Name"]!=null)
				{
					model.Glz_Name=row["Glz_Name"].ToString();
				}
				if(row["Glz_Jc"]!=null)
				{
					model.Glz_Jc=row["Glz_Jc"].ToString();
				}
				if(row["Glz_Memo"]!=null)
				{
					model.Glz_Memo=row["Glz_Memo"].ToString();
				}
			}
			return model;
		}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetList(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select Yy_Code,Glz_Code,Glz_Name,Glz_Jc,Glz_Memo ");
			strSql.Append(" FROM Zd_Qx1 ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			return HisVar.HisVar.Sqldal.Query(strSql.ToString());
		}

		/// <summary>
		/// 获得前几行数据
		/// </summary>
		public DataSet GetList(int Top,string strWhere,string filedOrder)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select ");
			if(Top>0)
			{
				strSql.Append(" top "+Top.ToString());
			}
			strSql.Append(" Yy_Code,Glz_Code,Glz_Name,Glz_Jc,Glz_Memo ");
			strSql.Append(" FROM Zd_Qx1 ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			strSql.Append(" order by " + filedOrder);
			return HisVar.HisVar.Sqldal.Query(strSql.ToString());
		}

		/// <summary>
		/// 获取记录总数
		/// </summary>
		public int GetRecordCount(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) FROM Zd_Qx1 ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			object obj = HisVar.HisVar.Sqldal.GetSingle(strSql.ToString());
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("SELECT * FROM ( ");
			strSql.Append(" SELECT ROW_NUMBER() OVER (");
			if (!string.IsNullOrEmpty(orderby.Trim()))
			{
				strSql.Append("order by T." + orderby );
			}
			else
			{
				strSql.Append("order by T.Glz_Code desc");
			}
			strSql.Append(")AS Row, T.*  from Zd_Qx1 T ");
			if (!string.IsNullOrEmpty(strWhere.Trim()))
			{
				strSql.Append(" WHERE " + strWhere);
			}
			strSql.Append(" ) TT");
			strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
			return HisVar.HisVar.Sqldal.Query(strSql.ToString());
		}

		/*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "Zd_Qx1";
			parameters[1].Value = "Glz_Code";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return HisVar.HisVar.Sqldal.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

		#endregion  BasicMethod
		#region  ExtensionMethod

		#endregion  ExtensionMethod
	}
}

