﻿/**  版本信息模板在安装目录下，可自行修改。
* M_Materials_Other_Out2.cs
*
* 功 能： N/A
* 类 名： M_Materials_Other_Out2
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2016/12/8 15:57:17   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
namespace ModelOld
{
	/// <summary>
	/// 物资其他出库从表
	/// </summary>
	[Serializable]
	public partial class M_Materials_Other_Out2
	{
		public M_Materials_Other_Out2()
		{}
		#region Model
		private string _m_otherout_code;
		private string _materials_code;
		private string _materialsstock_code;
		private string _m_otherout_detail_code;
		private string _materialslot;
		private DateTime? _materialsexpirydate;
		private decimal? _m_otherout_num;
		private decimal? _m_otherout_writeoffno;
		private decimal? _m_otherout_realno;
		private decimal? _m_otherout_price;
		private decimal? _m_otherout_money;
		private decimal? _m_otherout_realmoney;
		private string _m_otheroutdetail_memo;
		/// <summary>
		/// yyMMdd+5位流水号
		/// </summary>
		public string M_OtherOut_Code
		{
			set{ _m_otherout_code=value;}
			get{return _m_otherout_code;}
		}
		/// <summary>
		/// 物资编码
		/// </summary>
		public string Materials_Code
		{
			set{ _materials_code=value;}
			get{return _materials_code;}
		}
		/// <summary>
		/// 物资编码+6位流水号
		/// </summary>
		public string MaterialsStock_Code
		{
			set{ _materialsstock_code=value;}
			get{return _materialsstock_code;}
		}
		/// <summary>
		/// 出库明细编码
		/// </summary>
		public string M_OtherOut_Detail_Code
		{
			set{ _m_otherout_detail_code=value;}
			get{return _m_otherout_detail_code;}
		}
		/// <summary>
		/// 物资批号
		/// </summary>
		public string MaterialsLot
		{
			set{ _materialslot=value;}
			get{return _materialslot;}
		}
		/// <summary>
		/// 物资有效期
		/// </summary>
		public DateTime? MaterialsExpiryDate
		{
			set{ _materialsexpirydate=value;}
			get{return _materialsexpirydate;}
		}
		/// <summary>
		/// 出库数量
		/// </summary>
		public decimal? M_OtherOut_Num
		{
			set{ _m_otherout_num=value;}
			get{return _m_otherout_num;}
		}
		/// <summary>
		/// 
		/// </summary>
		public decimal? M_OtherOut_WriteoffNo
		{
			set{ _m_otherout_writeoffno=value;}
			get{return _m_otherout_writeoffno;}
		}
		/// <summary>
		/// 
		/// </summary>
		public decimal? M_OtherOut_RealNo
		{
			set{ _m_otherout_realno=value;}
			get{return _m_otherout_realno;}
		}
		/// <summary>
		/// 出库单价
		/// </summary>
		public decimal? M_OtherOut_Price
		{
			set{ _m_otherout_price=value;}
			get{return _m_otherout_price;}
		}
		/// <summary>
		/// 出库金额
		/// </summary>
		public decimal? M_OtherOut_Money
		{
			set{ _m_otherout_money=value;}
			get{return _m_otherout_money;}
		}
		/// <summary>
		/// 采购金额
		/// </summary>
		public decimal? M_OtherOut_RealMoney
		{
			set{ _m_otherout_realmoney=value;}
			get{return _m_otherout_realmoney;}
		}
		/// <summary>
		/// 备注
		/// </summary>
		public string M_OtherOutDetail_Memo
		{
			set{ _m_otheroutdetail_memo=value;}
			get{return _m_otheroutdetail_memo;}
		}
		#endregion Model

	}
}

