﻿Imports System.Data.SqlClient
Imports Stimulsoft.Report

Public Class Cx_YkYf_Crk
    Dim My_Cm As CurrencyManager             '同步指针
    Dim My_View As New DataView
    Dim My_Dataset As New DataSet
    Dim YfCode As String
    Dim YfName As String
    Dim FormLb As String

    Public Sub New(ByVal m_YfCode As String, ByVal m_YfName As String, ByVal m_FormLb As String)

        ' 此调用是设计器所必需的。
        InitializeComponent()
        YfCode = m_YfCode
        YfName = m_YfName
        FormLb = m_FormLb
        ' 在 InitializeComponent() 调用之后添加任何初始化。

    End Sub

    Private Sub Cx_YkCgRk_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        Call FormInit()
        Call Init_Tree()
    End Sub

    Private Sub FormInit()
        TextBox1.Text = ""
        TextBox1.Select()
        BaseFunc.BaseFunc.Init_Datetimepicker(DateTimePicker1)
        BaseFunc.BaseFunc.Init_Datetimepicker(DateTimePicker2)
        DateTimePicker1.Value = Format(Now, "yyyy-MM-01 00:00:00")
        DateTimePicker2.Value = Format(Now, "yyyy-MM-dd 23:59:59")
        Dim My_Combo1 As New BaseClass.C_Combo1(Me.C1Combo2)
        With My_Combo1
            .Init_TDBCombo()
            .AddItem("票据汇总")
            .AddItem("药品汇总")
            If FormLb = "药库采购入库查询" Or FormLb = "药房采购入库查询" Or FormLb = "药库退供应商查询" Or FormLb = "药房退供应商查询" Then
                .AddItem("供应商汇总")
            ElseIf FormLb = "药库科室支领查询" Or FormLb = "药房科室支领查询" Then
                .AddItem("支领科室汇总")
            ElseIf FormLb = "药库批发查询" Or FormLb = "药房批发查询" Then
                .AddItem("批发客户汇总")
            ElseIf FormLb = "药库调拨药房查询" Then
                .AddItem("药房汇总")
            End If
            .AddItem("精简汇总")
            .SelectedIndex(0)
        End With
        With C1Combo2
            .BorderStyle = BorderStyle.Fixed3D
            .Height = 22
            .Width = 124
            .DropDownWidth = 123
        End With
        Dim My_Grid As New BaseClass.C_Grid(Me.C1TrueDBGrid1)
        With My_Grid
            .Init_Grid()
            .AllAddNew(False)
            .AllDelete(False)
            .Init_Column("时间", "Crk_Date", 110, "中", "yyyy-MM-dd HH:mm")
            .Init_Column("出入库单位", "Kh_Name", 120, "左", "")
            .Init_Column("药品类别", "Dl_Name", 60, "左", "")
            .Init_Column("药品名称", "Yp_Name", 135, "左", "")
            .Init_Column("规格", "Mx_Gg", 80, "左", "")
            .Init_Column("产地", "Mx_Cd", 75, "左", "")
            .Init_Column("批号", "Yp_ph", 75, "左", "")
            .Init_Column("有效期", "Yp_Yxq", 70, "中", "yyyy-MM-dd")
            .Init_Column("单位", "Dw", 45, "中", "")
            .Init_Column("数量", "Sl", 45, "右", "##0.####")
            .Init_Column("采购价", "Mx_Cgj", 50, "右", "#####0.0000##")
            .Init_Column("采购金额", "Cg_Money", 70, "右", "#####0.0000##")
            .Init_Column("批发价", "Mx_Pfj", 50, "右", "#####0.0000##")
            .Init_Column("批发金额", "Pf_Money", 70, "右", "#####0.00##")
            .Init_Column("销售价", "Mx_Xsj", 50, "右", "#####0.0000##")
            .Init_Column("销售金额", "Xs_Money", 65, "右", "#####0.0000##")
            If FormLb = "药库退供应商查询" Or FormLb = "药房退供应商查询" Then
                .Init_Column("退库单价", "Tk_Dj", 55, "右", "#####0.0000##")
                .Init_Column("退库金额", "Tk_Money", 80, "右", "#####0.0000##")
            End If
            .P_Dock(BaseClass.C_Grid.Dock.Fill)
        End With
        C1TrueDBGrid1.Splits(0).HScrollBar.Style = C1.Win.C1TrueDBGrid.ScrollBarStyleEnum.Automatic
        C1TrueDBGrid1.VScrollBar.Style = C1.Win.C1TrueDBGrid.ScrollBarStyleEnum.Always
        Label4.Text = "记录条数 ∑=" + C1TrueDBGrid1.Splits(0).Rows.Count.ToString
        C1TrueDBGrid1.ColumnFooters = True

    End Sub

    Private Sub Init_Tree()

        With Me.TreeView1
            .Nodes.Clear()
            .FullRowSelect = True
            .HideSelection = False
            .HotTracking = True
            .Scrollable = True
            .ShowRootLines = False
            .Sorted = False
        End With


        '根节点
        Dim My_Reader As SqlDataReader

        Dim My_Root As New TreeNode
        With My_Root
            .Tag = "00"
            .Text = "药品类别"
            .ImageIndex = 0
        End With

        TreeView1.Nodes.Clear()
        TreeView1.Nodes.Add(My_Root)

        '一级数据
        My_Reader = HisVar.HisVar.Sqldal.ExecuteReader("Select Dl_Code,Dl_Name from Zd_Ml_YP1")
        While My_Reader.Read()
            Dim My_Node As New TreeNode
            With My_Node
                .Tag = My_Reader.Item(0).ToString
                .Text = My_Reader.Item(1).ToString
                .ImageIndex = 1
                .SelectedImageIndex = 2
            End With
            TreeView1.SelectedNode = My_Root
            TreeView1.SelectedNode.Nodes.Add(My_Node)

        End While
        TreeView1.SelectedNode.Checked = True
        My_Reader.Close()
        My_Cn.Close()


        Dim My_Root1 As New TreeNode
        With My_Root1
            .Tag = "11"
            If FormLb = "药库采购入库查询" Or FormLb = "药库退供应商查询" Or FormLb = "药房采购入库查询" Or FormLb = "药房退供应商查询" Then
                .Text = "供应商名称"
            ElseIf FormLb = "药库科室支领查询" Or FormLb = "药房科室支领查询" Then
                .Text = "科室名称"
            ElseIf FormLb = "药库批发查询" Or FormLb = "药房批发查询" Then
                .Text = "客户名称"
            ElseIf FormLb = "药库调拨药房查询" Then
                .Text = "药房名称"
            ElseIf FormLb = "药房退药库查询" Then
                .Text = "药库名称"
            End If

            .ImageIndex = 0
        End With

        TreeView1.Nodes.Add(My_Root1)


        If FormLb = "药库采购入库查询" Or FormLb = "药库退供应商查询" Or FormLb = "药房采购入库查询" Or FormLb = "药房退供应商查询" Then
            My_Reader = HisVar.HisVar.Sqldal.ExecuteReader("Select Kh_Code,Kh_Name from Zd_Kh_Rk ")
        ElseIf FormLb = "药库科室支领查询" Or FormLb = "药房科室支领查询" Then
            My_Reader = HisVar.HisVar.Sqldal.ExecuteReader("Select Ks_Code,Ks_Name from Zd_Yyks ")
        ElseIf FormLb = "药库批发查询" Or FormLb = "药房批发查询" Then
            My_Reader = HisVar.HisVar.Sqldal.ExecuteReader("Select Kh_Code,Kh_Name from Zd_Kh_Xs  ")
        ElseIf FormLb = "药库调拨药房查询" Then
            My_Reader = HisVar.HisVar.Sqldal.ExecuteReader("Select Yf_Code,Yf_Name from Zd_YyYf  ")
        ElseIf FormLb = "药房退药库查询" Then
            My_Reader = HisVar.HisVar.Sqldal.ExecuteReader("Select '01' as Yk_Code,'药库' as Yk_Name  ")
        End If


        While My_Reader.Read()
            Dim My_Node As New TreeNode
            With My_Node
                .Tag = My_Reader.Item(0).ToString
                .Text = My_Reader.Item(1).ToString
                .ImageIndex = 1
                .SelectedImageIndex = 2
            End With
            TreeView1.SelectedNode = My_Root1
            TreeView1.SelectedNode.Nodes.Add(My_Node)
        End While
        TreeView1.SelectedNode.Checked = True
        My_Reader.Close()
        My_Cn.Close()
        TreeView1.ExpandAll()
        TreeView1.SelectedNode = TreeView1.Nodes.Item(0)


    End Sub

    Private Sub TreeView1_AfterCheck(ByVal sender As Object, ByVal e As System.Windows.Forms.TreeViewEventArgs) Handles TreeView1.AfterCheck
        Dim N_Count As Integer

        For N_Count = 0 To e.Node.Nodes.Count - 1
            If e.Node.Checked = True Then

                TreeView1.SelectedNode = e.Node
                TreeView1.SelectedNode.Nodes.Item(N_Count).Checked = True
            Else
                TreeView1.SelectedNode = e.Node
                TreeView1.SelectedNode.Nodes.Item(N_Count).Checked = False
            End If
        Next

        'End If

    End Sub

    Private Sub TreeView1_BeforeCollapse(ByVal sender As Object, ByVal e As System.Windows.Forms.TreeViewCancelEventArgs) Handles TreeView1.BeforeCollapse
        If e.Node.Tag = TreeView1.Nodes.Item(0).Tag Or e.Node.Tag = TreeView1.Nodes.Item(1).Tag Then e.Cancel = True
    End Sub

    Private Sub TextBox1_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles TextBox1.TextChanged

        Dim V_Str As String = ""
        If Trim(TextBox1.Text & "") = "" Then
            V_Str = ""
        Else
            V_Str = "Yp_Name Like '*" & Trim(TextBox1.Text) & "*' or Kh_Name Like '*" & Trim(TextBox1.Text) & "*'"
        End If
        My_View.RowFilter = V_Str
        Label4.Text = "∑=" + C1TrueDBGrid1.Splits(0).Rows.Count.ToString
        Call F_Sum()
    End Sub

    Public Overrides Sub F_Sum()
        Dim Sum1 As Double = 0
        Dim Sum2 As Double = 0
        Dim Sum3 As Double = 0
        Dim Sum4 As Double = 0
        If C1TrueDBGrid1.RowCount <> 0 Then
            Sum1 = IIf(My_View.Table.Compute("Sum(Cg_Money)", My_View.RowFilter) Is DBNull.Value, 0, My_View.Table.Compute("Sum(Cg_Money)", My_View.RowFilter))
            Sum2 = IIf(My_View.Table.Compute("Sum(Pf_Money)", My_View.RowFilter) Is DBNull.Value, 0, My_View.Table.Compute("Sum(Pf_Money)", My_View.RowFilter))
            Sum3 = IIf(My_View.Table.Compute("Sum(Xs_Money)", My_View.RowFilter) Is DBNull.Value, 0, My_View.Table.Compute("Sum(Xs_Money)", My_View.RowFilter))
            If FormLb = "药库退供应商查询" Or FormLb = "药房退供应商查询" Then
                Sum4 = IIf(My_View.Table.Compute("Sum(Tk_Money)", My_View.RowFilter) Is DBNull.Value, 0, My_View.Table.Compute("Sum(Tk_Money)", My_View.RowFilter))
            End If



        End If
        With C1TrueDBGrid1
            .ColumnFooters = True
            .Columns("Cg_Money").FooterText = Format(Sum1, "###0.0000")
            .Columns("Pf_Money").FooterText = Format(Sum2, "###0.0000")
            .Columns("Xs_Money").FooterText = Format(Sum3, "###0.0000")
            If FormLb = "药库退供应商查询" Or FormLb = "药房退供应商查询" Then
                .Columns("Tk_Money").FooterText = Format(Sum4, "###0.0000")
            End If

        End With
    End Sub

    Private Sub C1Button3_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles C1Button3.Click
        TextBox1.Text = ""
        Dim V_Dl As String = ""
        Dim V_Kh As String = ""
        Dim Str_Where As String = ""
        Dim I As Integer

        TreeView1.SelectedNode = TreeView1.Nodes.Item(0)

        For I = 0 To TreeView1.SelectedNode.Nodes.Count - 1
            If TreeView1.SelectedNode.Nodes.Item(I).Checked = True Then
                V_Dl = V_Dl & ",'" & TreeView1.SelectedNode.Nodes.Item(I).Tag & "'"
            End If
        Next

        V_Dl = "(" & Mid(V_Dl, 2) & ")"


        TreeView1.SelectedNode = TreeView1.Nodes.Item(1)

        For I = 0 To TreeView1.SelectedNode.Nodes.Count - 1
            If TreeView1.SelectedNode.Nodes.Item(I).Checked = True Then
                V_Kh = V_Kh & ",'" & TreeView1.SelectedNode.Nodes.Item(I).Tag & "'"
            End If
        Next

        V_Kh = "(" & Mid(V_Kh, 2) & ")"

        If V_Dl = "()" Then
            MsgBox("请选择要查询的药品类别，要查询所有请全部勾选！", MsgBoxStyle.Information, "提示")
            Exit Sub

        End If

        If V_Kh = "()" Then
            MsgBox("请选择要查询的" & TreeView1.Nodes.Item(1).Text & "，要查询所有请全部勾选！", MsgBoxStyle.Information, "提示")
            Exit Sub

        End If

        If FormLb = "药库采购入库查询" Then
            Str_Where = " SELECT Rk_Date as Crk_Date,Yk_Rk1.Rk_Code as Crk_Code,V_YpKc.Xx_code,Kh_Name,Jsr_Name,Dl_Name,Yp_Name,Yp_Jc,Mx_Gg,Mx_Cd,Jx_Name,Rk_Sl as Sl,Rk_Dj as Mx_Cgj,Yk_Rk2.Rk_Money as Cg_Money,isnull(Rk_Xsj,0)Mx_Xsj,Rk_Sl*Isnull(Rk_Xsj,0) AS Xs_Money,isnull(Rk_Pfj,0)Mx_Pfj,Rk_Sl*Isnull(Rk_Pfj,0) AS Pf_Money,0 as Tk_Dj,0 as Tk_Money,Yp_ph,Yp_Yxq,IsJb,Dl_Code,Mx_CgDw as Dw " & _
      "FROM V_YpKc,Yk_Rk1,Yk_Rk2,Zd_YyJsr " & _
      "Where  Yk_Rk1.Rk_Code = Yk_Rk2.Rk_Code And V_YpKc.Xx_code = Yk_Rk2.Xx_code And Yk_Rk1.Jsr_Code=Zd_YyJsr.Jsr_Code  " & _
      "And Yk_Rk1.Rk_Date>='" & Format(DateTimePicker1.Value, "yyyy-MM-dd HH:mm:ss") & "' And Yk_Rk1.Rk_Date<='" & Format(DateTimePicker2.Value, "yyyy-MM-dd HH:mm:ss") & "' and Rk_Ok='已完成' " & _
      "And Kh_Code in " & V_Kh & " And Dl_Code in " & V_Dl & " Order by Yk_Rk1.Rk_Code"
        ElseIf FormLb = "药库科室支领查询" Then
            Str_Where = " SELECT Ck_Date as Crk_Date,Yk_Ks1.Ck_Code as Crk_Code,V_YpKc.Xx_Code,Ks_Name as Kh_Name,Jsr_Name,Dl_Name,Yp_Name ,Yp_Jc,Mx_Gg,Mx_Cd,Jx_Name,Ck_Sl as Sl,Ck_Cgj as Mx_Cgj,Ck_Sl*Isnull(Ck_Cgj,0) AS Cg_Money,isnull(Ck_Xsj,0)Mx_Xsj,Ck_Sl*Isnull(Ck_Xsj,0) AS Xs_Money,isnull(Ck_Pfj,0)Mx_Pfj,Ck_Sl*Isnull(Ck_Pfj,0) AS Pf_Money,0 as Tk_Dj,0 as Tk_Money,Yp_ph,Yp_Yxq,IsJb,Dl_Code,Mx_CgDw as Dw  FROM V_YpKc,Yk_Ks1,Yk_Ks2,Zd_YyJsr,Zd_YyKs Where Yk_Ks1.Ks_Code=Zd_YyKs.Ks_Code  And Yk_Ks1.Ck_Code = Yk_Ks2.Ck_Code And V_YpKc.Xx_Code = Yk_Ks2.Xx_Code And Yk_Ks1.Jsr_Code=Zd_YyJsr.Jsr_Code  And Dl_Code in " & V_Dl & " And Yk_Ks1.Ck_Date>='" & Format(DateTimePicker1.Value, "yyyy-MM-dd HH:mm:ss") & "' And Yk_Ks1.Ck_Date<='" & Format(DateTimePicker2.Value, "yyyy-MM-dd HH:mm:ss") & "' And Zd_YyKs.Ks_Code in " & V_Kh & " and Ck_Ok='已完成' Order by Yk_Ks1.Ck_Code"
        ElseIf FormLb = "药库批发查询" Then
            Str_Where = " SELECT Ck_Date as Crk_Date,Yk_Pf1.Ck_Code as Crk_Code,V_YpKc.Xx_code,Kh_Name as Kh_Name,Jsr_Name,Dl_Name,Yp_Name ,Yp_Jc,Mx_Gg,Mx_Cd,Jx_Name,Ck_Sl as Sl,isnull(Yk_Cgj,0)Mx_Cgj,Ck_Sl*Isnull(Yk_Cgj,0) AS Cg_Money,isnull(Yk_Xsj,0)Mx_Xsj,Ck_Sl*Isnull(Yk_Xsj,0) AS Xs_Money,Ck_Dj as Mx_Pfj,Yk_Pf2.Ck_Money Pf_Money,0 as Tk_Dj,0 as Tk_Money,Yp_ph,Yp_Yxq,IsJb,Dl_Code,Mx_CgDw as Dw  FROM V_YpKc,Yk_Pf1,Yk_Pf2,Zd_YyJsr Where  Yk_Pf1.Ck_Code = Yk_Pf2.Ck_Code And V_YpKc.Xx_code = Yk_Pf2.Xx_code And Yk_Pf1.Jsr_Code=Zd_YyJsr.Jsr_Code  And Dl_Code in " & V_Dl & " And Yk_Pf1.Ck_Date>='" & Format(DateTimePicker1.Value, "yyyy-MM-dd HH:mm:ss") & "' And Yk_Pf1.Ck_Date<='" & Format(DateTimePicker2.Value, "yyyy-MM-dd HH:mm:ss") & "' And Kh_Code in " & V_Kh & " and Ck_Ok='已完成'  Order by Yk_Pf1.Ck_Code"
        ElseIf FormLb = "药库调拨药房查询" Then
            Str_Where = " SELECT Ck_Date as Crk_Date,Yk_Yf1.Ck_Code as Crk_Code,V_YpKc.Xx_Code,Yf_Name as Kh_Name,Jsr_Name,Dl_Name,Yp_Name ,Yp_Jc,Mx_Gg,Mx_Cd,Jx_Name,Ck_Sl as Sl,isnull(Ck_Cgj,0)Mx_Cgj,Ck_Sl*Isnull(Ck_Cgj,0) AS Cg_Money,Ck_Xsj as Mx_Xsj,Ck_Sl*Isnull(Ck_Xsj,0) AS Xs_Money,isnull(Ck_Pfj,0)Mx_Pfj,Ck_Sl*Isnull(Ck_Pfj,0) AS Pf_Money,0 as Tk_Dj,0 as Tk_Money,Yp_ph,Yp_Yxq ,IsJb,Dl_Code,Mx_CgDw as Dw  FROM V_YpKc,Yk_Yf1,Yk_Yf2,Zd_YyJsr,Zd_YyYf Where Yk_Yf1.Yf_Code=Zd_YyYf.Yf_Code And Yk_Yf1.Ck_Code = Yk_Yf2.Ck_Code And V_YpKc.Xx_Code = Yk_Yf2.Xx_Code And Yk_Yf1.Jsr_Code=Zd_YyJsr.Jsr_Code and Ck_Qr=1  And Dl_Code in " & V_Dl & " And Yk_Yf1.Ck_Date>='" & Format(DateTimePicker1.Value, "yyyy-MM-dd HH:mm:ss") & "' And Yk_Yf1.Ck_Date<='" & Format(DateTimePicker2.Value, "yyyy-MM-dd HH:mm:ss") & "'  And Zd_YyYf.Yf_Code in " & V_Kh & " Order by Yk_Yf1.Ck_Code"
        ElseIf FormLb = "药库退供应商查询" Then
            Str_Where = "SELECT Tk_Date as Crk_Date,Yk_TkPf1.Tk_Code as Crk_Code,V_YpKc.Xx_Code,Kh_Name as Kh_Name,Jsr_Name,Dl_Name,Yp_Name,Yp_Jc,Mx_Gg,Mx_Cd,Jx_Name,Tk_Sl as Sl,isnull(Yk_Cgj,0)Mx_Cgj,Tk_Sl*Isnull(Yk_Cgj,0) AS Cg_Money,isnull(Yk_Xsj,0)Mx_Xsj,Tk_Sl*Isnull(Yk_Xsj,0) AS Xs_Money,isnull(Yk_Pfj,0)Mx_Pfj,Tk_Sl*Isnull(Yk_Pfj,0) AS Pf_Money,Tk_Dj,Yk_TkPf2.Tk_Money,Yp_ph,Yp_Yxq,IsJb,Dl_Code,Mx_CgDw as Dw  FROM V_YpKc,Yk_TkPf1,Yk_TkPf2,Zd_YyJsr " & _
      "Where  Yk_TkPf1.Tk_Code = Yk_TkPf2.Tk_Code And V_YpKc.Xx_Code = Yk_TkPf2.Xx_Code And Yk_TkPf1.Jsr_Code=Zd_YyJsr.Jsr_Code  " & _
      "And Yk_TkPf1.Tk_Date>='" & Format(DateTimePicker1.Value, "yyyy-MM-dd HH:mm:ss") & "' And Yk_TkPf1.Tk_Date<='" & Format(DateTimePicker2.Value, "yyyy-MM-dd HH:mm:ss") & "' and Ck_Ok='已完成' And Kh_Code in " & V_Kh & " And Dl_Code in " & V_Dl & "  Order by Yk_TkPf1.Tk_Code"
        ElseIf FormLb = "药房采购入库查询" Then
            Str_Where = "SELECT Rk_Date as Crk_Date,Yf_Rk1.Rk_Code as Crk_Code,V_YpKc.Xx_code,Kh_Name as Kh_Name,Jsr_Name,Dl_Name,Yp_Name ,Yp_Jc,Mx_Gg,Mx_Cd,Jx_Name,Rk_Sl as Sl,Rk_Dj as Mx_Cgj,Rk_Sl*Rk_Dj AS Cg_Money,isnull(Rk_Xsj,0)Mx_Xsj,Rk_Sl*Isnull(Rk_Xsj,0) AS Xs_Money,isnull(Rk_Pfj,0)Mx_Pfj,Rk_Sl*Isnull(Rk_Pfj,0) AS Pf_Money,0 as Tk_Dj,0 as Tk_Money,Yp_ph,Yp_Yxq,IsJb,Dl_Code,Mx_XsDw as Dw  FROM V_YpKc,Yf_Rk1,Yf_Rk2,Zd_YyJsr Where  Yf_Rk1.Rk_Code = Yf_Rk2.Rk_Code And V_YpKc.Xx_code = Yf_Rk2.Xx_code And Yf_Rk1.Jsr_Code=Zd_YyJsr.Jsr_Code  And Yf_Rk1.Rk_Date>='" & Format(DateTimePicker1.Value, "yyyy-MM-dd HH:mm:ss") & "' And Yf_Rk1.Rk_Date<='" & Format(DateTimePicker2.Value, "yyyy-MM-dd HH:mm:ss") & "'  And Kh_Code in " & V_Kh & "And Dl_Code in " & V_Dl & "   and Rk_Ok='已完成' and Yf_Rk1.Yf_Code='" & YfCode & "' Order by Yf_Rk1.Rk_Code"
        ElseIf FormLb = "药房科室支领查询" Then
            Str_Where = " SELECT Ck_Date as Crk_Date,Yf_Ks1.Ck_Code as Crk_Code,V_YpKc.Xx_Code,Ks_Name as Kh_Name,Jsr_Name,Dl_Name,Yp_Name,Yp_Jc,Mx_Gg,Mx_Cd,Jx_Name,Ck_Sl as Sl,Ck_Cgj as Mx_Cgj,Ck_Sl*Isnull(Ck_Cgj,0) AS Cg_Money,Ck_Xsj as Mx_Xsj,Ck_Sl*Isnull(Ck_Xsj,0) AS Xs_Money,isnull(Ck_Pfj,0)Mx_Pfj,Ck_Sl*Isnull(Ck_Pfj,0) AS Pf_Money,0 as Tk_Dj,0 as Tk_Money,Yp_ph,Yp_Yxq,IsJb,Dl_Code,Mx_XsDw as Dw  FROM V_YpKc,Yf_Ks1,Yf_Ks2,Zd_YyJsr,Zd_YyKs Where Yf_Ks1.Ks_Code=Zd_YyKs.Ks_Code  And Yf_Ks1.Ck_Code = Yf_Ks2.Ck_Code And V_YpKc.Xx_Code = Yf_Ks2.Xx_Code And Yf_Ks1.Jsr_Code=Zd_YyJsr.Jsr_Code  And Yf_Ks1.Ck_Date>='" & Format(DateTimePicker1.Value, "yyyy-MM-dd HH:mm:ss") & "' And Yf_Ks1.Ck_Date<='" & Format(DateTimePicker2.Value, "yyyy-MM-dd HH:mm:ss") & "'  And Zd_YyKs.Ks_Code in " & V_Kh & "And Dl_Code in " & V_Dl & "   and Ck_Ok='已完成' and Yf_Ks1.Yf_Code='" & YfCode & "' Order by Yf_Ks1.Ck_Code"
        ElseIf FormLb = "药房批发查询" Then
            Str_Where = " SELECT Ck_Date as Crk_Date,Yf_Pf1.Ck_Code as Crk_Code,V_YpKc.Xx_code,Kh_Name as Kh_Name,Jsr_Name,Dl_Name,Yp_Name,Yp_Jc,Mx_Gg,Mx_Cd,Jx_Name,Ck_Sl as Sl,isnull(Yk_Cgj,0)/Mx_CfBl AS Mx_Cgj,Ck_Sl*Isnull(Yk_Cgj,0)/Mx_CfBl AS Cg_Money,isnull(Yk_Xsj,0)/Mx_CfBl AS Mx_Xsj,Ck_Sl*Isnull(Yk_Xsj,0)/Mx_CfBl AS Xs_Money,Ck_Dj as Mx_Pfj,Ck_Sl*Ck_Dj AS Pf_Money,0 as Tk_Dj,0 as Tk_Money,Yp_ph,Yp_Yxq,IsJb,Dl_Code,Mx_XsDw as Dw  FROM V_YpKc,Yf_Pf1,Yf_Pf2,Zd_YyJsr Where  Yf_Pf1.Ck_Code = Yf_Pf2.Ck_Code And V_YpKc.Xx_code = Yf_Pf2.Xx_code  And Yf_Pf1.Jsr_Code=Zd_YyJsr.Jsr_Code  And Dl_Code in " & V_Dl & " And Yf_Pf1.Ck_Date>='" & Format(DateTimePicker1.Value, "yyyy-MM-dd HH:mm:ss") & "' And Yf_Pf1.Ck_Date<='" & Format(DateTimePicker2.Value, "yyyy-MM-dd HH:mm:ss") & "'  And Kh_Code in " & V_Kh & " and Ck_Ok='已完成'" & " And Yf_Pf1.Yf_Code='" & YfCode & "' Order by Yf_Pf1.Ck_Code"
        ElseIf FormLb = "药房退供应商查询" Then
            Str_Where = "SELECT Tk_Date as Crk_Date,Yf_TkPf1.Tk_Code as Crk_Code,V_YpKc.Xx_Code,Kh_Name as Kh_Name,Jsr_Name,Dl_Name,Yp_Name,Yp_Jc,Mx_Gg,Mx_Cd,Jx_Name,Tk_Sl as Sl,isnull(Yk_Cgj/Mx_Cfbl,0)Mx_Cgj,Tk_Sl*Isnull(Yk_Cgj/Mx_Cfbl,0) AS Cg_Money,Yf_Lsj1 as Mx_Xsj,Tk_Sl*Yf_Lsj1 AS Xs_Money,isnull(Yk_Pfj/Mx_Cfbl,0)Mx_Pfj,Tk_Sl*Isnull(Yk_Pfj/Mx_Cfbl,0) AS Pf_Money,Tk_Dj,Yf_TkPf2.Tk_Money,Yp_ph,Yp_Yxq,IsJb,Dl_Code,Mx_XsDw  as Dw " & _
      "FROM V_YpKc,Yf_TkPf1,Yf_TkPf2,Zd_YyJsr Where  Yf_TkPf1.Tk_Code = Yf_TkPf2.Tk_Code And V_YpKc.Xx_Code = Yf_TkPf2.Xx_Code And Yf_TkPf1.Jsr_Code=Zd_YyJsr.Jsr_Code And Yf_TkPf1.Yf_Code='" & YfCode & "' " & _
      "And Yf_TkPf1.Tk_Date>='" & Format(DateTimePicker1.Value, "yyyy-MM-dd HH:mm:ss") & "' And Yf_TkPf1.Tk_Date<='" & Format(DateTimePicker2.Value, "yyyy-MM-dd HH:mm:ss") & "' and Ck_Ok='已完成' And Kh_Code in " & V_Kh & " And Dl_Code in " & V_Dl & "  Order by Yf_TkPf1.Tk_Code"
        ElseIf FormLb = "药房退药库查询" Then
            Str_Where = "SELECT Tk_Date as Crk_Date,Yf_Tk1.Tk_Code as Crk_Code,V_Ypkc.Xx_Code,'药库' as Kh_Name,Jsr_Name,Dl_Name,Yp_Name,Yp_Jc,Mx_Gg,Mx_Cd,Jx_Name,Tk_Sl as Sl,Yk_Cgj/Mx_Cfbl as Mx_Cgj,Tk_Sl*Yk_Cgj/Mx_Cfbl AS Cg_Money,Tk_Dj as Mx_Xsj,Yf_Tk2.Tk_Money AS Xs_Money, Yk_Pfj/Mx_Cfbl as Mx_Pfj,Tk_Sl*Yk_Pfj/Mx_Cfbl AS Pf_Money,0 as Tk_Dj,0 as Tk_Money, Yp_ph,Yp_Yxq,IsJb,Dl_Code,Mx_XsDw as Dw " & _
      "FROM V_Ypkc,Yf_Tk1,Yf_Tk2,Zd_YyJsr where  Yf_Tk1.Tk_Code = Yf_Tk2.Tk_Code and V_Ypkc.Xx_Code = Yf_Tk2.Xx_Code and  Yf_Tk1.Jsr_Code=Zd_YyJsr.Jsr_Code and Tk_Qr=1 And Yf_Tk1.Tk_Date>='" & Format(DateTimePicker1.Value, "yyyy-MM-dd HH:mm:ss") & "' And Yf_Tk1.Tk_Date<='" & Format(DateTimePicker2.Value, "yyyy-MM-dd HH:mm:ss") & "' And Dl_Code in " & V_Dl & "  and Yf_Tk1.Yf_Code='" & YfCode & "' Order by Yf_Tk1.Tk_Code"

        End If


        HisVar.HisVar.Sqldal.QueryDt(My_Dataset, Str_Where, "药库药房出入库查询", True)

        With C1TrueDBGrid1
            My_Cm = CType(BindingContext(My_Dataset, "药库药房出入库查询"), CurrencyManager)
            .SetDataBinding(My_Dataset, "药库药房出入库查询", True)
            My_View = My_Cm.List
        End With
        Label4.Text = "记录条数 ∑=" + C1TrueDBGrid1.Splits(0).Rows.Count.ToString
        Call F_Sum()
        If My_Dataset.Tables("药库药房出入库查询").Rows.Count = 0 Then MsgBox("没有符合条件的记录", MsgBoxStyle.Information, "提示") : Exit Sub


    End Sub

    Private Sub C1Button1_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles C1Button1.Click
        If C1TrueDBGrid1.RowCount = 0 Then Exit Sub
        Dim StiRpt As New StiReport
        If FormLb = "药库退供应商查询" Or FormLb = "药房退供应商查询" Then
            Select Case C1Combo2.Text
                Case "票据汇总"
                    StiRpt.Load(".\Rpt\药库药房退供应商查询票据汇总.mrt")
                Case "药品汇总"
                    StiRpt.Load(".\Rpt\药库药房退供应商查询药品汇总.mrt")
                Case "供应商汇总"
                    StiRpt.Load(".\Rpt\药库药房退供应商查询客户汇总.mrt")
                Case "精简汇总"
                    StiRpt.Load(".\Rpt\药库药房退供应商查询清单简化.mrt")
            End Select
        Else
            Select Case C1Combo2.Text
                Case "票据汇总"
                    StiRpt.Load(".\Rpt\药库药房出入库查询票据汇总.mrt")
                Case "药品汇总"
                    StiRpt.Load(".\Rpt\药库药房出入库查询药品汇总.mrt")
                Case "供应商汇总", "支领科室汇总", "批发客户汇总", "药房汇总"
                    StiRpt.Load(".\Rpt\药库药房出入库查询客户汇总.mrt")
                Case "精简汇总"
                    StiRpt.Load(".\Rpt\药库药房出入库查询清单简化.mrt")
            End Select
        End If
       
        StiRpt.ReportName = FormLb & C1Combo2.Text
        StiRpt.RegData(My_View)
        StiRpt.Compile()
        StiRpt("标题") = FormLb & C1Combo2.Text
        StiRpt("打印日期") = "打印时间:" & Format(Now, "yyyy-MM-dd HH:mm")
        StiRpt("查询时间") = "查询时间:" & Format(DateTimePicker1.Value, "yyyy-MM-dd HH:mm:ss") & " 至 " & Format(DateTimePicker2.Value, "yyyy-MM-dd HH:mm:ss")
        StiRpt("制表人") = "制表人:" & HisVar.HisVar.JsrName

        ' StiRpt.Design()
        StiRpt.Show()


    End Sub

#Region "输入法设置"
    '中文
    Private Sub C1TextBox1_Enter(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles TextBox1.GotFocus
        InputLanguage.CurrentInputLanguage = HisVar.HisVar.InputCode
    End Sub

#End Region

  
End Class