﻿Imports System.Data.SqlClient
Imports System.Windows.Forms
Imports System.Drawing

Public Class MaterialsMove

#Region "para"
    Dim My_View As New DataView
    Public My_Table As New DataTable
    Public My_Cm As CurrencyManager
    Public My_Row As DataRow

    Dim BllMtClass As New BLLOld.B_Materials_Class_Dict
    Dim BllMt As New BLLOld.B_Materials_Dict
    Dim ModelMt As New ModelOld.M_Materials_Dict
#End Region

#Region "trans_para"
    Dim RMyGrid As CustomControl.MyGrid
#End Region

    Public Sub New(ByRef tMyGrid As CustomControl.MyGrid)
        InitializeComponent()
        RMyGrid = tMyGrid
    End Sub

    Private Sub MaterialsMove_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        Call Form_Init()
        Call Data_Init()
    End Sub

#Region "kj_dz"
    Private Sub Button1_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Button1.Click, Button2.Click
        Select Case sender.tag
            Case "确定"
                UpdateData()
                Me.Close()
            Case "取消"
                Me.Close()
        End Select
    End Sub

    Private Sub C1TrueDBGrid1_RowColChange_1(ByVal sender As System.Object, ByVal e As C1.Win.C1TrueDBGrid.RowColChangeEventArgs) Handles MyGrid1.RowColChange
        If (MyGrid1.Row + 1) > MyGrid1.RowCount Then
        Else
            My_Row = My_Cm.List(MyGrid1.Row).Row
        End If
    End Sub
#End Region

#Region "zdy_func"
    Private Sub Form_Init()
        Button1.Top = 1
        Button2.Location = New Point(Button1.Left + Button1.Width + 2, Button1.Top)
        Grid_Init()
    End Sub

    Private Sub Grid_Init()
        With MyGrid1
            .Clear()
            .Dock = System.Windows.Forms.DockStyle.Fill
            .Init_Column("类别编码", "Class_Code", 100, "中", "", False)
            .Init_Column("类别名称", "Class_Name", 100, "中", "", False)
            .Init_Column("父类编码", "Class_Father", 100, "中", "", False)
            .AllowSort = False
        End With
    End Sub

    Private Sub Data_Init()
        My_Table = BllMtClass.GetList("HaveChild=0").Tables(0)
        My_Table.PrimaryKey = New DataColumn() {My_Table.Columns("Class_Code")}
        With Me.MyGrid1
            My_Cm = CType(BindingContext(My_Table, ""), CurrencyManager)
            .DataTable = My_Table
        End With
        My_View = My_Cm.List
        MyGrid1.Select()
    End Sub

    Private Sub UpdateData()
        For I = 0 To RMyGrid.SelectedRows.Count - 1
            RMyGrid.Bookmark = RMyGrid.SelectedRows(I)
            ModelMt = BllMt.GetModel(RMyGrid.Columns("Materials_Code").Value)
            ModelMt.Class_Code = My_Row("Class_Code")
            BllMt.Update(ModelMt)
        Next
    End Sub
#End Region

End Class