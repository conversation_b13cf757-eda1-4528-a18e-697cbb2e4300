﻿<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<StiSerializer version="1.02" type="Net" application="StiReport">
  <Dictionary Ref="1" type="Dictionary" isKey="true">
    <BusinessObjects isList="true" count="0" />
    <Databases isList="true" count="0" />
    <DataSources isList="true" count="5">
      <西药 Ref="2" type="DataTableSource" isKey="true">
        <Alias>西药</Alias>
        <Columns isList="true" count="11">
          <value>Mx_Code,System.String</value>
          <value>Jx_Name,System.String</value>
          <value>Mz_id,System.Int32</value>
          <value>Mx_Gg,System.String</value>
          <value>Mz_Sl,System.Decimal</value>
          <value>Mx_XsDw,System.String</value>
          <value>Yp_Yfyl,System.String</value>
          <value>Mz_Money,System.Decimal</value>
          <value>IsJb,System.String</value>
          <value>Yp_Name,System.String</value>
          <value>Special,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Name>西药</Name>
        <NameInSource>西药</NameInSource>
      </西药>
      <中成药 Ref="3" type="DataTableSource" isKey="true">
        <Alias>中成药</Alias>
        <Columns isList="true" count="11">
          <value>Mx_Code,System.String</value>
          <value>Jx_Name,System.String</value>
          <value>Mz_id,System.Int32</value>
          <value>Mx_Gg,System.String</value>
          <value>Mz_Sl,System.Decimal</value>
          <value>Mx_XsDw,System.String</value>
          <value>Yp_Yfyl,System.String</value>
          <value>Mz_Money,System.Decimal</value>
          <value>IsJb,System.String</value>
          <value>Yp_Name,System.String</value>
          <value>Special,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Name>中成药</Name>
        <NameInSource>中成药</NameInSource>
      </中成药>
      <中药处方 Ref="4" type="DataTableSource" isKey="true">
        <Alias>中药处方</Alias>
        <Columns isList="true" count="11">
          <value>Mx_Code,System.String</value>
          <value>Jx_Name,System.String</value>
          <value>Mz_id,System.Int32</value>
          <value>Mx_Gg,System.String</value>
          <value>Mz_Sl,System.Decimal</value>
          <value>Mx_XsDw,System.String</value>
          <value>Yp_Yfyl,System.String</value>
          <value>Mz_Money,System.Decimal</value>
          <value>IsJb,System.String</value>
          <value>Yp_Name,System.String</value>
          <value>Special,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Name>中药处方</Name>
        <NameInSource>中药处方</NameInSource>
      </中药处方>
      <卫材处方 Ref="5" type="DataTableSource" isKey="true">
        <Alias>卫材处方</Alias>
        <Columns isList="true" count="11">
          <value>Mx_Code,System.String</value>
          <value>Jx_Name,System.String</value>
          <value>Mz_id,System.Int32</value>
          <value>Mx_Gg,System.String</value>
          <value>Mz_Sl,System.Decimal</value>
          <value>Mx_XsDw,System.String</value>
          <value>Yp_Yfyl,System.String</value>
          <value>Mz_Money,System.Decimal</value>
          <value>IsJb,System.String</value>
          <value>Yp_Name,System.String</value>
          <value>Special,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Name>卫材处方</Name>
        <NameInSource>卫材处方</NameInSource>
      </卫材处方>
      <诊疗处方 Ref="6" type="DataTableSource" isKey="true">
        <Alias>诊疗处方</Alias>
        <Columns isList="true" count="10">
          <value>Mx_Code,System.String</value>
          <value>Mz_id,System.Int32</value>
          <value>Mx_Gg,System.String</value>
          <value>Mz_Sl,System.Decimal</value>
          <value>Mx_XsDw,System.String</value>
          <value>Yp_Yfyl,System.String</value>
          <value>Mz_Money,System.Decimal</value>
          <value>Yp_Name,System.String</value>
          <value>Jx_Name,System.String</value>
          <value>IsJb,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Name>诊疗处方</Name>
        <NameInSource>诊疗处方</NameInSource>
      </诊疗处方>
    </DataSources>
    <Relations isList="true" count="0" />
    <Report isRef="0" />
    <Variables isList="true" count="1">
      <value>,门诊时间,门诊时间,System.String,,False,False</value>
    </Variables>
  </Dictionary>
  <EngineVersion>EngineV2</EngineVersion>
  <GlobalizationStrings isList="true" count="0" />
  <MetaTags isList="true" count="0" />
  <Pages isList="true" count="5">
    <Page1 Ref="7" type="Page" isKey="true">
      <Border>None;Black;2;Solid;False;4;Black</Border>
      <Brush>Transparent</Brush>
      <Components isList="true" count="5">
        <PageHeader1 Ref="8" type="PageHeaderBand" isKey="true">
          <Brush>[255:255:255]</Brush>
          <CanGrow>False</CanGrow>
          <ClientRectangle>0,0.4,10.4,0</ClientRectangle>
          <Components isList="true" count="0" />
          <Conditions isList="true" count="0" />
          <Linked>True</Linked>
          <Name>PageHeader1</Name>
          <Page isRef="7" />
          <Parent isRef="7" />
        </PageHeader1>
        <PageFooter1 Ref="9" type="PageFooterBand" isKey="true">
          <Brush>[255:255:255]</Brush>
          <CanGrow>False</CanGrow>
          <ClientRectangle>0,27.7,10.4,0</ClientRectangle>
          <Components isList="true" count="0" />
          <Conditions isList="true" count="0" />
          <Linked>True</Linked>
          <Name>PageFooter1</Name>
          <Page isRef="7" />
          <Parent isRef="7" />
        </PageFooter1>
        <GroupHeader1 Ref="10" type="GroupHeaderBand" isKey="true">
          <Brush>[255:255:255]</Brush>
          <ClientRectangle>0,1.2,10.4,6</ClientRectangle>
          <Components isList="true" count="3">
            <TextBox2 Ref="11" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>7.5,5.19,2.81,0.81</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9</Font>
              <HorAlignment>Center</HorAlignment>
              <Linked>True</Linked>
              <Margins>0,0,0,0</Margins>
              <Name>TextBox2</Name>
              <Page isRef="7" />
              <Parent isRef="10" />
              <Text>（         ）</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <VertAlignment>Center</VertAlignment>
            </TextBox2>
            <TextBox3 Ref="12" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>6.37,5.19,1.12,0.81</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <HorAlignment>Right</HorAlignment>
              <Linked>True</Linked>
              <Margins>0,0,0,0</Margins>
              <Name>TextBox3</Name>
              <Page isRef="7" />
              <Parent isRef="10" />
              <Text>试 敏</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <VertAlignment>Center</VertAlignment>
            </TextBox3>
            <Label1 Ref="13" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>0.19,5.19,6.19,0.79</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Microsoft Sans Serif,10.5</Font>
              <HorAlignment>Center</HorAlignment>
              <Linked>True</Linked>
              <Margins>0,0,0,0</Margins>
              <Name>Label1</Name>
              <Page isRef="7" />
              <Parent isRef="10" />
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <VertAlignment>Center</VertAlignment>
            </Label1>
          </Components>
          <Conditions isList="true" count="0" />
          <Linked>True</Linked>
          <Name>GroupHeader1</Name>
          <Page isRef="7" />
          <Parent isRef="7" />
        </GroupHeader1>
        <Detail1 Ref="14" type="DataBand" isKey="true">
          <Brush>[255:255:255]</Brush>
          <BusinessObjectGuid isNull="true" />
          <ClientRectangle>0,8,10.4,2.4</ClientRectangle>
          <Components isList="true" count="8">
            <TextBox20 Ref="15" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>6.81,1.25,2.54,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>Arial,8</Font>
              <Linked>True</Linked>
              <Margins>0,0,0,0</Margins>
              <Name>TextBox20</Name>
              <Page isRef="7" />
              <Parent isRef="14" />
              <Text>{西药.Mz_Money}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
            </TextBox20>
            <TextBox1 Ref="16" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>0.16,0,3.65,1.27</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Microsoft Sans Serif,9</Font>
              <Linked>True</Linked>
              <Margins>0,0,0,0</Margins>
              <Name>TextBox1</Name>
              <Page isRef="7" />
              <Parent isRef="14" />
              <Text>{西药.Yp_Name}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </TextBox1>
            <TextBox11 Ref="17" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>8.57,0,0.95,1.27</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Microsoft Sans Serif,10</Font>
              <HideZeros>True</HideZeros>
              <HorAlignment>Right</HorAlignment>
              <Linked>True</Linked>
              <Margins>0,0,0,0</Margins>
              <Name>TextBox11</Name>
              <Page isRef="7" />
              <Parent isRef="14" />
              <Text>{西药.Mz_Sl}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="18" type="CustomFormat" isKey="true">
                <StringFormat>0.####</StringFormat>
              </TextFormat>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </TextBox11>
            <TextBox12 Ref="19" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>0.16,1.27,10.16,1.11</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Microsoft Sans Serif,10</Font>
              <HorAlignment>Right</HorAlignment>
              <Linked>True</Linked>
              <Margins>0,0,0,0</Margins>
              <Name>TextBox12</Name>
              <Page isRef="7" />
              <Parent isRef="14" />
              <Text>{西药.Yp_Yfyl}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </TextBox12>
            <TextBox14 Ref="20" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>9.52,0,0.79,1.27</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Microsoft Sans Serif,10</Font>
              <Linked>True</Linked>
              <Margins>0,0,0,0</Margins>
              <Name>TextBox14</Name>
              <Page isRef="7" />
              <Parent isRef="14" />
              <Text>{西药.Mx_XsDw}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </TextBox14>
            <TextBox21 Ref="21" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>6.83,0,1.75,1.27</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Microsoft Sans Serif,10</Font>
              <HorAlignment>Right</HorAlignment>
              <Linked>True</Linked>
              <Margins>0,0,0,0</Margins>
              <Name>TextBox21</Name>
              <Page isRef="7" />
              <Parent isRef="14" />
              <Text>{西药.Mx_Gg}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </TextBox21>
            <TextBox22 Ref="22" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>3.81,0,1.75,1.27</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Microsoft Sans Serif,9</Font>
              <Linked>True</Linked>
              <Margins>0,0,0,0</Margins>
              <Name>TextBox22</Name>
              <Page isRef="7" />
              <Parent isRef="14" />
              <Text>{西药.IsJb}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </TextBox22>
            <TextBox6 Ref="23" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>5.56,0,1.27,1.27</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Microsoft Sans Serif,10</Font>
              <HorAlignment>Center</HorAlignment>
              <Linked>True</Linked>
              <Margins>0,0,0,0</Margins>
              <Name>TextBox6</Name>
              <Page isRef="7" />
              <Parent isRef="14" />
              <Text>{西药.Jx_Name}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </TextBox6>
          </Components>
          <Conditions isList="true" count="0" />
          <DataRelationName isNull="true" />
          <DataSourceName>西药</DataSourceName>
          <Filters isList="true" count="0" />
          <Linked>True</Linked>
          <Name>Detail1</Name>
          <Page isRef="7" />
          <Parent isRef="7" />
          <Sort isList="true" count="0" />
        </Detail1>
        <GroupFooter1 Ref="24" type="GroupFooterBand" isKey="true">
          <Brush>[255:255:255]</Brush>
          <CanGrow>False</CanGrow>
          <ClientRectangle>0,11.2,10.4,2.5</ClientRectangle>
          <Components isList="true" count="3">
            <TextBox5 Ref="25" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>7,0,3.31,1.19</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <HorAlignment>Center</HorAlignment>
              <Linked>True</Linked>
              <Margins>0,0,0,0</Margins>
              <Name>TextBox5</Name>
              <Page isRef="7" />
              <Parent isRef="24" />
              <Text>{Sum(GroupHeader1,西药.Mz_Money)}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </TextBox5>
            <Text1 Ref="26" type="Text" isKey="true">
              <Border>Bottom;Black;1;None;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>6.1,1.25,4.06,0.76</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text1</Name>
              <Page isRef="7" />
              <Parent isRef="24" />
              <Text>{门诊时间}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text1>
            <Line1 Ref="27" type="Shape" isKey="true">
              <BorderColor>Black</BorderColor>
              <Brush>Transparent</Brush>
              <ClientRectangle>2.44,-2.5,4.87,2.03</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Linked>True</Linked>
              <Locked>True</Locked>
              <Name>Line1</Name>
              <Page isRef="7" />
              <Parent isRef="24" />
              <ShapeType Ref="28" type="Stimulsoft.Report.Components.ShapeTypes.StiDiagonalUpLineShapeType" isKey="true" />
            </Line1>
          </Components>
          <Conditions isList="true" count="0" />
          <Linked>True</Linked>
          <Name>GroupFooter1</Name>
          <Page isRef="7" />
          <Parent isRef="7" />
        </GroupFooter1>
      </Components>
      <Conditions isList="true" count="0" />
      <Guid>a7dd96673ed048f595e8cfe5568e8c9b</Guid>
      <Margins>0.3,0.3,1,1</Margins>
      <Name>Page1</Name>
      <PageHeight>29.7</PageHeight>
      <PageWidth>11</PageWidth>
      <Report isRef="0" />
      <Watermark Ref="29" type="Stimulsoft.Report.Components.StiWatermark" isKey="true">
        <Font>Arial,100</Font>
        <TextBrush>[50:0:0:0]</TextBrush>
      </Watermark>
    </Page1>
    <Page5 Ref="30" type="Page" isKey="true">
      <Border>None;Black;2;Solid;False;4;Black</Border>
      <Brush>Transparent</Brush>
      <Components isList="true" count="5">
        <PageHeaderBand4 Ref="31" type="PageHeaderBand" isKey="true">
          <Brush>[255:255:255]</Brush>
          <CanGrow>False</CanGrow>
          <ClientRectangle>0,0.4,10.4,0</ClientRectangle>
          <Components isList="true" count="0" />
          <Conditions isList="true" count="0" />
          <Linked>True</Linked>
          <Name>PageHeaderBand4</Name>
          <Page isRef="30" />
          <Parent isRef="30" />
        </PageHeaderBand4>
        <PageFooterBand4 Ref="32" type="PageFooterBand" isKey="true">
          <Brush>[255:255:255]</Brush>
          <CanGrow>False</CanGrow>
          <ClientRectangle>0,27.7,10.4,0</ClientRectangle>
          <Components isList="true" count="0" />
          <Conditions isList="true" count="0" />
          <Linked>True</Linked>
          <Name>PageFooterBand4</Name>
          <Page isRef="30" />
          <Parent isRef="30" />
        </PageFooterBand4>
        <GroupHeaderBand4 Ref="33" type="GroupHeaderBand" isKey="true">
          <Brush>[255:255:255]</Brush>
          <ClientRectangle>0,1.2,10.4,6</ClientRectangle>
          <Components isList="true" count="3">
            <Text41 Ref="34" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>7.5,5.19,2.81,0.81</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9</Font>
              <HorAlignment>Center</HorAlignment>
              <Linked>True</Linked>
              <Margins>0,0,0,0</Margins>
              <Name>Text41</Name>
              <Page isRef="30" />
              <Parent isRef="33" />
              <Text>（         ）</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <VertAlignment>Center</VertAlignment>
            </Text41>
            <Text42 Ref="35" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>6.37,5.19,1.12,0.81</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <HorAlignment>Right</HorAlignment>
              <Linked>True</Linked>
              <Margins>0,0,0,0</Margins>
              <Name>Text42</Name>
              <Page isRef="30" />
              <Parent isRef="33" />
              <Text>试 敏</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <VertAlignment>Center</VertAlignment>
            </Text42>
            <Text43 Ref="36" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>0.19,5.19,6.19,0.79</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Microsoft Sans Serif,10.5</Font>
              <HorAlignment>Center</HorAlignment>
              <Linked>True</Linked>
              <Margins>0,0,0,0</Margins>
              <Name>Text43</Name>
              <Page isRef="30" />
              <Parent isRef="33" />
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <VertAlignment>Center</VertAlignment>
            </Text43>
          </Components>
          <Conditions isList="true" count="0" />
          <Linked>True</Linked>
          <Name>GroupHeaderBand4</Name>
          <Page isRef="30" />
          <Parent isRef="30" />
        </GroupHeaderBand4>
        <DataBand4 Ref="37" type="DataBand" isKey="true">
          <Brush>[255:255:255]</Brush>
          <BusinessObjectGuid isNull="true" />
          <ClientRectangle>0,8,10.4,2.4</ClientRectangle>
          <Components isList="true" count="8">
            <Text44 Ref="38" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>6.81,1.25,2.54,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>Arial,8</Font>
              <Linked>True</Linked>
              <Margins>0,0,0,0</Margins>
              <Name>Text44</Name>
              <Page isRef="30" />
              <Parent isRef="37" />
              <Text>{中成药.Mz_Money}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
            </Text44>
            <Text45 Ref="39" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>0.16,0,3.65,1.27</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Microsoft Sans Serif,9</Font>
              <Linked>True</Linked>
              <Margins>0,0,0,0</Margins>
              <Name>Text45</Name>
              <Page isRef="30" />
              <Parent isRef="37" />
              <Text>{中成药.Yp_Name}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text45>
            <Text46 Ref="40" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>8.57,0,0.95,1.27</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Microsoft Sans Serif,10</Font>
              <HideZeros>True</HideZeros>
              <HorAlignment>Right</HorAlignment>
              <Linked>True</Linked>
              <Margins>0,0,0,0</Margins>
              <Name>Text46</Name>
              <Page isRef="30" />
              <Parent isRef="37" />
              <Text>{中成药.Mz_Sl}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="41" type="CustomFormat" isKey="true">
                <StringFormat>0.####</StringFormat>
              </TextFormat>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text46>
            <Text47 Ref="42" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>0.16,1.27,10.16,1.11</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Microsoft Sans Serif,10</Font>
              <HorAlignment>Right</HorAlignment>
              <Linked>True</Linked>
              <Margins>0,0,0,0</Margins>
              <Name>Text47</Name>
              <Page isRef="30" />
              <Parent isRef="37" />
              <Text>{中成药.Yp_Yfyl}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text47>
            <Text48 Ref="43" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>9.52,0,0.79,1.27</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Microsoft Sans Serif,10</Font>
              <Linked>True</Linked>
              <Margins>0,0,0,0</Margins>
              <Name>Text48</Name>
              <Page isRef="30" />
              <Parent isRef="37" />
              <Text>{中成药.Mx_XsDw}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text48>
            <Text49 Ref="44" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>6.83,0,1.75,1.27</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Microsoft Sans Serif,10</Font>
              <HorAlignment>Right</HorAlignment>
              <Linked>True</Linked>
              <Margins>0,0,0,0</Margins>
              <Name>Text49</Name>
              <Page isRef="30" />
              <Parent isRef="37" />
              <Text>{中成药.Mx_Gg}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text49>
            <Text50 Ref="45" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>3.81,0,1.75,1.27</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Microsoft Sans Serif,9</Font>
              <Linked>True</Linked>
              <Margins>0,0,0,0</Margins>
              <Name>Text50</Name>
              <Page isRef="30" />
              <Parent isRef="37" />
              <Text>{中成药.IsJb}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text50>
            <Text51 Ref="46" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>5.56,0,1.27,1.27</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Microsoft Sans Serif,10</Font>
              <HorAlignment>Center</HorAlignment>
              <Linked>True</Linked>
              <Margins>0,0,0,0</Margins>
              <Name>Text51</Name>
              <Page isRef="30" />
              <Parent isRef="37" />
              <Text>{中成药.Jx_Name}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text51>
          </Components>
          <Conditions isList="true" count="0" />
          <DataRelationName isNull="true" />
          <DataSourceName>中成药</DataSourceName>
          <Filters isList="true" count="0" />
          <Linked>True</Linked>
          <Name>DataBand4</Name>
          <Page isRef="30" />
          <Parent isRef="30" />
          <Sort isList="true" count="0" />
        </DataBand4>
        <GroupFooterBand4 Ref="47" type="GroupFooterBand" isKey="true">
          <Brush>[255:255:255]</Brush>
          <CanGrow>False</CanGrow>
          <ClientRectangle>0,11.2,10.4,2.5</ClientRectangle>
          <Components isList="true" count="3">
            <Text52 Ref="48" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>7,0,3.31,1.19</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <HorAlignment>Center</HorAlignment>
              <Linked>True</Linked>
              <Margins>0,0,0,0</Margins>
              <Name>Text52</Name>
              <Page isRef="30" />
              <Parent isRef="47" />
              <Text>{Sum(中成药.Mz_Money)}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text52>
            <Text53 Ref="49" type="Text" isKey="true">
              <Border>Bottom;Black;1;None;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>6.1,1.25,4.06,0.76</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text53</Name>
              <Page isRef="30" />
              <Parent isRef="47" />
              <Text>{门诊时间}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text53>
            <Shape4 Ref="50" type="Shape" isKey="true">
              <BorderColor>Black</BorderColor>
              <Brush>Transparent</Brush>
              <ClientRectangle>2.44,-2.5,4.87,2.03</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Linked>True</Linked>
              <Locked>True</Locked>
              <Name>Shape4</Name>
              <Page isRef="30" />
              <Parent isRef="47" />
              <ShapeType Ref="51" type="Stimulsoft.Report.Components.ShapeTypes.StiDiagonalUpLineShapeType" isKey="true" />
            </Shape4>
          </Components>
          <Conditions isList="true" count="0" />
          <Linked>True</Linked>
          <Name>GroupFooterBand4</Name>
          <Page isRef="30" />
          <Parent isRef="30" />
        </GroupFooterBand4>
      </Components>
      <Conditions isList="true" count="0" />
      <Enabled>False</Enabled>
      <Guid>eb83d41fc8154ebbb69f31b7553e3fda</Guid>
      <Margins>0.3,0.3,1,1</Margins>
      <Name>Page5</Name>
      <PageHeight>29.7</PageHeight>
      <PageWidth>11</PageWidth>
      <Report isRef="0" />
      <Watermark Ref="52" type="Stimulsoft.Report.Components.StiWatermark" isKey="true">
        <Font>Arial,100</Font>
        <TextBrush>[50:0:0:0]</TextBrush>
      </Watermark>
    </Page5>
    <Page4 Ref="53" type="Page" isKey="true">
      <Border>None;Black;2;Solid;False;4;Black</Border>
      <Brush>Transparent</Brush>
      <Components isList="true" count="5">
        <PageHeaderBand3 Ref="54" type="PageHeaderBand" isKey="true">
          <Brush>[255:255:255]</Brush>
          <CanGrow>False</CanGrow>
          <ClientRectangle>0,0.4,10.4,0</ClientRectangle>
          <Components isList="true" count="0" />
          <Conditions isList="true" count="0" />
          <Linked>True</Linked>
          <Name>PageHeaderBand3</Name>
          <Page isRef="53" />
          <Parent isRef="53" />
        </PageHeaderBand3>
        <PageFooterBand3 Ref="55" type="PageFooterBand" isKey="true">
          <Brush>[255:255:255]</Brush>
          <CanGrow>False</CanGrow>
          <ClientRectangle>0,27.7,10.4,0</ClientRectangle>
          <Components isList="true" count="0" />
          <Conditions isList="true" count="0" />
          <Linked>True</Linked>
          <Name>PageFooterBand3</Name>
          <Page isRef="53" />
          <Parent isRef="53" />
        </PageFooterBand3>
        <GroupHeaderBand3 Ref="56" type="GroupHeaderBand" isKey="true">
          <Brush>[255:255:255]</Brush>
          <ClientRectangle>0,1.2,10.4,6</ClientRectangle>
          <Components isList="true" count="3">
            <Text28 Ref="57" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>7.5,5.19,2.81,0.81</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9</Font>
              <HorAlignment>Center</HorAlignment>
              <Linked>True</Linked>
              <Margins>0,0,0,0</Margins>
              <Name>Text28</Name>
              <Page isRef="53" />
              <Parent isRef="56" />
              <Text>（         ）</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <VertAlignment>Center</VertAlignment>
            </Text28>
            <Text29 Ref="58" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>6.37,5.19,1.12,0.81</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <HorAlignment>Right</HorAlignment>
              <Linked>True</Linked>
              <Margins>0,0,0,0</Margins>
              <Name>Text29</Name>
              <Page isRef="53" />
              <Parent isRef="56" />
              <Text>试 敏</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <VertAlignment>Center</VertAlignment>
            </Text29>
            <Text30 Ref="59" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>0.19,5.19,6.19,0.79</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Microsoft Sans Serif,10.5</Font>
              <HorAlignment>Center</HorAlignment>
              <Linked>True</Linked>
              <Margins>0,0,0,0</Margins>
              <Name>Text30</Name>
              <Page isRef="53" />
              <Parent isRef="56" />
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <VertAlignment>Center</VertAlignment>
            </Text30>
          </Components>
          <Conditions isList="true" count="0" />
          <Linked>True</Linked>
          <Name>GroupHeaderBand3</Name>
          <Page isRef="53" />
          <Parent isRef="53" />
        </GroupHeaderBand3>
        <DataBand3 Ref="60" type="DataBand" isKey="true">
          <Brush>[255:255:255]</Brush>
          <BusinessObjectGuid isNull="true" />
          <ClientRectangle>0,8,10.4,2.4</ClientRectangle>
          <Components isList="true" count="8">
            <Text31 Ref="61" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>6.81,1.25,2.54,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>Arial,8</Font>
              <Linked>True</Linked>
              <Margins>0,0,0,0</Margins>
              <Name>Text31</Name>
              <Page isRef="53" />
              <Parent isRef="60" />
              <Text>{中药处方.Mz_Money}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
            </Text31>
            <Text32 Ref="62" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>0.16,0,3.65,1.27</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Microsoft Sans Serif,9</Font>
              <Linked>True</Linked>
              <Margins>0,0,0,0</Margins>
              <Name>Text32</Name>
              <Page isRef="53" />
              <Parent isRef="60" />
              <Text>{中药处方.Yp_Name}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text32>
            <Text33 Ref="63" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>8.57,0,0.95,1.27</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Microsoft Sans Serif,10</Font>
              <HideZeros>True</HideZeros>
              <HorAlignment>Right</HorAlignment>
              <Linked>True</Linked>
              <Margins>0,0,0,0</Margins>
              <Name>Text33</Name>
              <Page isRef="53" />
              <Parent isRef="60" />
              <Text>{中药处方.Mz_Sl}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="64" type="CustomFormat" isKey="true">
                <StringFormat>0.####</StringFormat>
              </TextFormat>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text33>
            <Text34 Ref="65" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>0.16,1.27,10.16,1.11</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Microsoft Sans Serif,10</Font>
              <HorAlignment>Right</HorAlignment>
              <Linked>True</Linked>
              <Margins>0,0,0,0</Margins>
              <Name>Text34</Name>
              <Page isRef="53" />
              <Parent isRef="60" />
              <Text>{中药处方.Yp_Yfyl}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text34>
            <Text35 Ref="66" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>9.52,0,0.79,1.27</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Microsoft Sans Serif,10</Font>
              <Linked>True</Linked>
              <Margins>0,0,0,0</Margins>
              <Name>Text35</Name>
              <Page isRef="53" />
              <Parent isRef="60" />
              <Text>{中药处方.Mx_XsDw}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text35>
            <Text36 Ref="67" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>6.83,0,1.75,1.27</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Microsoft Sans Serif,10</Font>
              <HorAlignment>Right</HorAlignment>
              <Linked>True</Linked>
              <Margins>0,0,0,0</Margins>
              <Name>Text36</Name>
              <Page isRef="53" />
              <Parent isRef="60" />
              <Text>{中药处方.Mx_Gg}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text36>
            <Text37 Ref="68" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>3.81,0,1.75,1.27</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Microsoft Sans Serif,9</Font>
              <Linked>True</Linked>
              <Margins>0,0,0,0</Margins>
              <Name>Text37</Name>
              <Page isRef="53" />
              <Parent isRef="60" />
              <Text>{中药处方.IsJb}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text37>
            <Text38 Ref="69" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>5.56,0,1.27,1.27</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Microsoft Sans Serif,10</Font>
              <HorAlignment>Center</HorAlignment>
              <Linked>True</Linked>
              <Margins>0,0,0,0</Margins>
              <Name>Text38</Name>
              <Page isRef="53" />
              <Parent isRef="60" />
              <Text>{中药处方.Jx_Name}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text38>
          </Components>
          <Conditions isList="true" count="0" />
          <DataRelationName isNull="true" />
          <DataSourceName>中药处方</DataSourceName>
          <Filters isList="true" count="0" />
          <Linked>True</Linked>
          <Name>DataBand3</Name>
          <Page isRef="53" />
          <Parent isRef="53" />
          <Sort isList="true" count="0" />
        </DataBand3>
        <GroupFooterBand3 Ref="70" type="GroupFooterBand" isKey="true">
          <Brush>[255:255:255]</Brush>
          <CanGrow>False</CanGrow>
          <ClientRectangle>0,11.2,10.4,2.5</ClientRectangle>
          <Components isList="true" count="3">
            <Text39 Ref="71" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>7,0,3.31,1.19</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <HorAlignment>Center</HorAlignment>
              <Linked>True</Linked>
              <Margins>0,0,0,0</Margins>
              <Name>Text39</Name>
              <Page isRef="53" />
              <Parent isRef="70" />
              <Text>{Sum(卫材处方.Mz_Money)}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text39>
            <Text40 Ref="72" type="Text" isKey="true">
              <Border>Bottom;Black;1;None;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>6.1,1.25,4.06,0.76</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text40</Name>
              <Page isRef="53" />
              <Parent isRef="70" />
              <Text>{门诊时间}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text40>
            <Shape3 Ref="73" type="Shape" isKey="true">
              <BorderColor>Black</BorderColor>
              <Brush>Transparent</Brush>
              <ClientRectangle>2.44,-2.5,4.87,2.03</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Linked>True</Linked>
              <Locked>True</Locked>
              <Name>Shape3</Name>
              <Page isRef="53" />
              <Parent isRef="70" />
              <ShapeType Ref="74" type="Stimulsoft.Report.Components.ShapeTypes.StiDiagonalUpLineShapeType" isKey="true" />
            </Shape3>
          </Components>
          <Conditions isList="true" count="0" />
          <Linked>True</Linked>
          <Name>GroupFooterBand3</Name>
          <Page isRef="53" />
          <Parent isRef="53" />
        </GroupFooterBand3>
      </Components>
      <Conditions isList="true" count="0" />
      <Enabled>False</Enabled>
      <Guid>3513209e01ef4229b6ea780d417791e4</Guid>
      <Margins>0.3,0.3,1,1</Margins>
      <Name>Page4</Name>
      <PageHeight>29.7</PageHeight>
      <PageWidth>11</PageWidth>
      <Report isRef="0" />
      <Watermark Ref="75" type="Stimulsoft.Report.Components.StiWatermark" isKey="true">
        <Font>Arial,100</Font>
        <TextBrush>[50:0:0:0]</TextBrush>
      </Watermark>
    </Page4>
    <Page3 Ref="76" type="Page" isKey="true">
      <Border>None;Black;2;Solid;False;4;Black</Border>
      <Brush>Transparent</Brush>
      <Components isList="true" count="5">
        <PageHeaderBand2 Ref="77" type="PageHeaderBand" isKey="true">
          <Brush>[255:255:255]</Brush>
          <CanGrow>False</CanGrow>
          <ClientRectangle>0,0.4,10.4,0</ClientRectangle>
          <Components isList="true" count="0" />
          <Conditions isList="true" count="0" />
          <Linked>True</Linked>
          <Name>PageHeaderBand2</Name>
          <Page isRef="76" />
          <Parent isRef="76" />
        </PageHeaderBand2>
        <PageFooterBand2 Ref="78" type="PageFooterBand" isKey="true">
          <Brush>[255:255:255]</Brush>
          <CanGrow>False</CanGrow>
          <ClientRectangle>0,27.7,10.4,0</ClientRectangle>
          <Components isList="true" count="0" />
          <Conditions isList="true" count="0" />
          <Linked>True</Linked>
          <Name>PageFooterBand2</Name>
          <Page isRef="76" />
          <Parent isRef="76" />
        </PageFooterBand2>
        <GroupHeaderBand2 Ref="79" type="GroupHeaderBand" isKey="true">
          <Brush>[255:255:255]</Brush>
          <ClientRectangle>0,1.2,10.4,6</ClientRectangle>
          <Components isList="true" count="3">
            <Text15 Ref="80" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>7.5,5.19,2.81,0.81</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9</Font>
              <HorAlignment>Center</HorAlignment>
              <Linked>True</Linked>
              <Margins>0,0,0,0</Margins>
              <Name>Text15</Name>
              <Page isRef="76" />
              <Parent isRef="79" />
              <Text>（         ）</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <VertAlignment>Center</VertAlignment>
            </Text15>
            <Text16 Ref="81" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>6.37,5.19,1.12,0.81</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <HorAlignment>Right</HorAlignment>
              <Linked>True</Linked>
              <Margins>0,0,0,0</Margins>
              <Name>Text16</Name>
              <Page isRef="76" />
              <Parent isRef="79" />
              <Text>试 敏</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <VertAlignment>Center</VertAlignment>
            </Text16>
            <Text17 Ref="82" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>0.19,5.19,6.19,0.79</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Microsoft Sans Serif,10.5</Font>
              <HorAlignment>Center</HorAlignment>
              <Linked>True</Linked>
              <Margins>0,0,0,0</Margins>
              <Name>Text17</Name>
              <Page isRef="76" />
              <Parent isRef="79" />
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <VertAlignment>Center</VertAlignment>
            </Text17>
          </Components>
          <Conditions isList="true" count="0" />
          <Linked>True</Linked>
          <Name>GroupHeaderBand2</Name>
          <Page isRef="76" />
          <Parent isRef="76" />
        </GroupHeaderBand2>
        <DataBand2 Ref="83" type="DataBand" isKey="true">
          <Brush>[255:255:255]</Brush>
          <BusinessObjectGuid isNull="true" />
          <ClientRectangle>0,8,10.4,2.4</ClientRectangle>
          <Components isList="true" count="8">
            <Text18 Ref="84" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>6.81,1.25,2.54,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>Arial,8</Font>
              <Linked>True</Linked>
              <Margins>0,0,0,0</Margins>
              <Name>Text18</Name>
              <Page isRef="76" />
              <Parent isRef="83" />
              <Text>{卫材处方.Mz_Money}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
            </Text18>
            <Text19 Ref="85" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>0.16,0,3.65,1.27</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Microsoft Sans Serif,9</Font>
              <Linked>True</Linked>
              <Margins>0,0,0,0</Margins>
              <Name>Text19</Name>
              <Page isRef="76" />
              <Parent isRef="83" />
              <Text>{卫材处方.Yp_Name}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text19>
            <Text20 Ref="86" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>8.57,0,0.95,1.27</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Microsoft Sans Serif,10</Font>
              <HideZeros>True</HideZeros>
              <HorAlignment>Right</HorAlignment>
              <Linked>True</Linked>
              <Margins>0,0,0,0</Margins>
              <Name>Text20</Name>
              <Page isRef="76" />
              <Parent isRef="83" />
              <Text>{卫材处方.Mz_Sl}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="87" type="CustomFormat" isKey="true">
                <StringFormat>0.####</StringFormat>
              </TextFormat>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text20>
            <Text21 Ref="88" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>0.16,1.27,10.16,1.11</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Microsoft Sans Serif,10</Font>
              <HorAlignment>Right</HorAlignment>
              <Linked>True</Linked>
              <Margins>0,0,0,0</Margins>
              <Name>Text21</Name>
              <Page isRef="76" />
              <Parent isRef="83" />
              <Text>{卫材处方.Yp_Yfyl}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text21>
            <Text22 Ref="89" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>9.52,0,0.79,1.27</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Microsoft Sans Serif,10</Font>
              <Linked>True</Linked>
              <Margins>0,0,0,0</Margins>
              <Name>Text22</Name>
              <Page isRef="76" />
              <Parent isRef="83" />
              <Text>{卫材处方.Mx_XsDw}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text22>
            <Text23 Ref="90" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>6.83,0,1.75,1.27</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Microsoft Sans Serif,10</Font>
              <HorAlignment>Right</HorAlignment>
              <Linked>True</Linked>
              <Margins>0,0,0,0</Margins>
              <Name>Text23</Name>
              <Page isRef="76" />
              <Parent isRef="83" />
              <Text>{卫材处方.Mx_Gg}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text23>
            <Text24 Ref="91" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>3.81,0,1.75,1.27</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Microsoft Sans Serif,9</Font>
              <Linked>True</Linked>
              <Margins>0,0,0,0</Margins>
              <Name>Text24</Name>
              <Page isRef="76" />
              <Parent isRef="83" />
              <Text>{卫材处方.IsJb}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text24>
            <Text25 Ref="92" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>5.56,0,1.27,1.27</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Microsoft Sans Serif,10</Font>
              <HorAlignment>Center</HorAlignment>
              <Linked>True</Linked>
              <Margins>0,0,0,0</Margins>
              <Name>Text25</Name>
              <Page isRef="76" />
              <Parent isRef="83" />
              <Text>{卫材处方.Jx_Name}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text25>
          </Components>
          <Conditions isList="true" count="0" />
          <DataRelationName isNull="true" />
          <DataSourceName>卫材处方</DataSourceName>
          <Filters isList="true" count="0" />
          <Linked>True</Linked>
          <Name>DataBand2</Name>
          <Page isRef="76" />
          <Parent isRef="76" />
          <Sort isList="true" count="0" />
        </DataBand2>
        <GroupFooterBand2 Ref="93" type="GroupFooterBand" isKey="true">
          <Brush>[255:255:255]</Brush>
          <CanGrow>False</CanGrow>
          <ClientRectangle>0,11.2,10.4,2.5</ClientRectangle>
          <Components isList="true" count="3">
            <Text26 Ref="94" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>7,0,3.31,1.19</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <HorAlignment>Center</HorAlignment>
              <Linked>True</Linked>
              <Margins>0,0,0,0</Margins>
              <Name>Text26</Name>
              <Page isRef="76" />
              <Parent isRef="93" />
              <Text>{Sum(卫材处方.Mz_Money)}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text26>
            <Text27 Ref="95" type="Text" isKey="true">
              <Border>Bottom;Black;1;None;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>6.1,1.25,4.06,0.76</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text27</Name>
              <Page isRef="76" />
              <Parent isRef="93" />
              <Text>{门诊时间}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text27>
            <Shape2 Ref="96" type="Shape" isKey="true">
              <BorderColor>Black</BorderColor>
              <Brush>Transparent</Brush>
              <ClientRectangle>2.44,-2.5,4.87,2.03</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Linked>True</Linked>
              <Locked>True</Locked>
              <Name>Shape2</Name>
              <Page isRef="76" />
              <Parent isRef="93" />
              <ShapeType Ref="97" type="Stimulsoft.Report.Components.ShapeTypes.StiDiagonalUpLineShapeType" isKey="true" />
            </Shape2>
          </Components>
          <Conditions isList="true" count="0" />
          <Linked>True</Linked>
          <Name>GroupFooterBand2</Name>
          <Page isRef="76" />
          <Parent isRef="76" />
        </GroupFooterBand2>
      </Components>
      <Conditions isList="true" count="0" />
      <Guid>e96a5bf3e516430190154eb3b2f614e9</Guid>
      <Margins>0.3,0.3,1,1</Margins>
      <Name>Page3</Name>
      <PageHeight>29.7</PageHeight>
      <PageWidth>11</PageWidth>
      <Report isRef="0" />
      <Watermark Ref="98" type="Stimulsoft.Report.Components.StiWatermark" isKey="true">
        <Font>Arial,100</Font>
        <TextBrush>[50:0:0:0]</TextBrush>
      </Watermark>
    </Page3>
    <Page2 Ref="99" type="Page" isKey="true">
      <Border>None;Black;2;Solid;False;4;Black</Border>
      <Brush>Transparent</Brush>
      <Components isList="true" count="5">
        <PageHeaderBand1 Ref="100" type="PageHeaderBand" isKey="true">
          <Brush>[255:255:255]</Brush>
          <CanGrow>False</CanGrow>
          <ClientRectangle>0,0.4,10.4,0</ClientRectangle>
          <Components isList="true" count="0" />
          <Conditions isList="true" count="0" />
          <Linked>True</Linked>
          <Name>PageHeaderBand1</Name>
          <Page isRef="99" />
          <Parent isRef="99" />
        </PageHeaderBand1>
        <PageFooterBand1 Ref="101" type="PageFooterBand" isKey="true">
          <Brush>[255:255:255]</Brush>
          <CanGrow>False</CanGrow>
          <ClientRectangle>0,27.7,10.4,0</ClientRectangle>
          <Components isList="true" count="0" />
          <Conditions isList="true" count="0" />
          <Linked>True</Linked>
          <Name>PageFooterBand1</Name>
          <Page isRef="99" />
          <Parent isRef="99" />
        </PageFooterBand1>
        <GroupHeaderBand1 Ref="102" type="GroupHeaderBand" isKey="true">
          <Brush>[255:255:255]</Brush>
          <ClientRectangle>0,1.2,10.4,6</ClientRectangle>
          <Components isList="true" count="3">
            <Text2 Ref="103" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>7.5,5.19,2.81,0.81</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9</Font>
              <HorAlignment>Center</HorAlignment>
              <Linked>True</Linked>
              <Margins>0,0,0,0</Margins>
              <Name>Text2</Name>
              <Page isRef="99" />
              <Parent isRef="102" />
              <Text>（         ）</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <VertAlignment>Center</VertAlignment>
            </Text2>
            <Text3 Ref="104" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>6.37,5.19,1.12,0.81</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <HorAlignment>Right</HorAlignment>
              <Linked>True</Linked>
              <Margins>0,0,0,0</Margins>
              <Name>Text3</Name>
              <Page isRef="99" />
              <Parent isRef="102" />
              <Text>试 敏</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <VertAlignment>Center</VertAlignment>
            </Text3>
            <Text4 Ref="105" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>0.19,5.19,6.19,0.79</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Microsoft Sans Serif,10.5</Font>
              <HorAlignment>Center</HorAlignment>
              <Linked>True</Linked>
              <Margins>0,0,0,0</Margins>
              <Name>Text4</Name>
              <Page isRef="99" />
              <Parent isRef="102" />
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <VertAlignment>Center</VertAlignment>
            </Text4>
          </Components>
          <Conditions isList="true" count="0" />
          <Linked>True</Linked>
          <Name>GroupHeaderBand1</Name>
          <Page isRef="99" />
          <Parent isRef="99" />
        </GroupHeaderBand1>
        <DataBand1 Ref="106" type="DataBand" isKey="true">
          <Brush>[255:255:255]</Brush>
          <BusinessObjectGuid isNull="true" />
          <ClientRectangle>0,8,10.4,2.4</ClientRectangle>
          <Components isList="true" count="8">
            <Text5 Ref="107" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>6.81,1.25,2.54,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>Arial,8</Font>
              <Linked>True</Linked>
              <Margins>0,0,0,0</Margins>
              <Name>Text5</Name>
              <Page isRef="99" />
              <Parent isRef="106" />
              <Text>{诊疗处方.Mz_Money}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
            </Text5>
            <Text6 Ref="108" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>0.16,0,3.65,1.27</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Microsoft Sans Serif,9</Font>
              <Linked>True</Linked>
              <Margins>0,0,0,0</Margins>
              <Name>Text6</Name>
              <Page isRef="99" />
              <Parent isRef="106" />
              <Text>{诊疗处方.Yp_Name}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text6>
            <Text7 Ref="109" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>8.57,0,0.95,1.27</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Microsoft Sans Serif,10</Font>
              <HideZeros>True</HideZeros>
              <HorAlignment>Right</HorAlignment>
              <Linked>True</Linked>
              <Margins>0,0,0,0</Margins>
              <Name>Text7</Name>
              <Page isRef="99" />
              <Parent isRef="106" />
              <Text>{诊疗处方.Mz_Sl}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="110" type="CustomFormat" isKey="true">
                <StringFormat>0.####</StringFormat>
              </TextFormat>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text7>
            <Text8 Ref="111" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>0.16,1.27,10.16,1.11</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Microsoft Sans Serif,10</Font>
              <HorAlignment>Right</HorAlignment>
              <Linked>True</Linked>
              <Margins>0,0,0,0</Margins>
              <Name>Text8</Name>
              <Page isRef="99" />
              <Parent isRef="106" />
              <Text>{诊疗处方.Yp_Yfyl}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text8>
            <Text9 Ref="112" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>9.52,0,0.79,1.27</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Microsoft Sans Serif,10</Font>
              <Linked>True</Linked>
              <Margins>0,0,0,0</Margins>
              <Name>Text9</Name>
              <Page isRef="99" />
              <Parent isRef="106" />
              <Text>{诊疗处方.Mx_XsDw}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text9>
            <Text10 Ref="113" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>6.83,0,1.75,1.27</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Microsoft Sans Serif,10</Font>
              <HorAlignment>Right</HorAlignment>
              <Linked>True</Linked>
              <Margins>0,0,0,0</Margins>
              <Name>Text10</Name>
              <Page isRef="99" />
              <Parent isRef="106" />
              <Text>{诊疗处方.Mx_Gg}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text10>
            <Text11 Ref="114" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>3.81,0,1.75,1.27</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Microsoft Sans Serif,9</Font>
              <Linked>True</Linked>
              <Margins>0,0,0,0</Margins>
              <Name>Text11</Name>
              <Page isRef="99" />
              <Parent isRef="106" />
              <Text>{诊疗处方.IsJb}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text11>
            <Text12 Ref="115" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>5.56,0,1.27,1.27</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Microsoft Sans Serif,10</Font>
              <HorAlignment>Center</HorAlignment>
              <Linked>True</Linked>
              <Margins>0,0,0,0</Margins>
              <Name>Text12</Name>
              <Page isRef="99" />
              <Parent isRef="106" />
              <Text>{诊疗处方.Jx_Name}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text12>
          </Components>
          <Conditions isList="true" count="0" />
          <DataRelationName isNull="true" />
          <DataSourceName>诊疗处方</DataSourceName>
          <Filters isList="true" count="0" />
          <Linked>True</Linked>
          <Name>DataBand1</Name>
          <Page isRef="99" />
          <Parent isRef="99" />
          <Sort isList="true" count="0" />
        </DataBand1>
        <GroupFooterBand1 Ref="116" type="GroupFooterBand" isKey="true">
          <Brush>[255:255:255]</Brush>
          <CanGrow>False</CanGrow>
          <ClientRectangle>0,11.2,10.4,2.5</ClientRectangle>
          <Components isList="true" count="3">
            <Text13 Ref="117" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>7,0,3.31,1.19</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <HorAlignment>Center</HorAlignment>
              <Linked>True</Linked>
              <Margins>0,0,0,0</Margins>
              <Name>Text13</Name>
              <Page isRef="99" />
              <Parent isRef="116" />
              <Text>{Sum(诊疗处方.Mz_Money)}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text13>
            <Text14 Ref="118" type="Text" isKey="true">
              <Border>Bottom;Black;1;None;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>6.1,1.25,4.06,0.76</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text14</Name>
              <Page isRef="99" />
              <Parent isRef="116" />
              <Text>{门诊时间}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text14>
            <Shape1 Ref="119" type="Shape" isKey="true">
              <BorderColor>Black</BorderColor>
              <Brush>Transparent</Brush>
              <ClientRectangle>2.44,-2.5,4.87,2.03</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Linked>True</Linked>
              <Locked>True</Locked>
              <Name>Shape1</Name>
              <Page isRef="99" />
              <Parent isRef="116" />
              <ShapeType Ref="120" type="Stimulsoft.Report.Components.ShapeTypes.StiDiagonalUpLineShapeType" isKey="true" />
            </Shape1>
          </Components>
          <Conditions isList="true" count="0" />
          <Linked>True</Linked>
          <Name>GroupFooterBand1</Name>
          <Page isRef="99" />
          <Parent isRef="99" />
        </GroupFooterBand1>
      </Components>
      <Conditions isList="true" count="0" />
      <Guid>bddbc866068b4cf3b9c3ce468c54a1b3</Guid>
      <Margins>0.3,0.3,1,1</Margins>
      <Name>Page2</Name>
      <PageHeight>29.7</PageHeight>
      <PageWidth>11</PageWidth>
      <Report isRef="0" />
      <Watermark Ref="121" type="Stimulsoft.Report.Components.StiWatermark" isKey="true">
        <Font>Arial,100</Font>
        <TextBrush>[50:0:0:0]</TextBrush>
      </Watermark>
    </Page2>
  </Pages>
  <PrinterSettings Ref="122" type="Stimulsoft.Report.Print.StiPrinterSettings" isKey="true" />
  <ReferencedAssemblies isList="true" count="8">
    <value>System.Dll</value>
    <value>System.Drawing.Dll</value>
    <value>System.Windows.Forms.Dll</value>
    <value>System.Data.Dll</value>
    <value>System.Xml.Dll</value>
    <value>Stimulsoft.Controls.Dll</value>
    <value>Stimulsoft.Base.Dll</value>
    <value>Stimulsoft.Report.Dll</value>
  </ReferencedAssemblies>
  <ReportAlias>门诊处方表</ReportAlias>
  <ReportChanged>11/10/2018 3:30:51 PM</ReportChanged>
  <ReportCreated>11/8/2018 3:41:46 PM</ReportCreated>
  <ReportDescription>ActiveReports Version: 3.1</ReportDescription>
  <ReportFile>C:\Users\<USER>\Desktop\ZTHis5\ZTHisOutpatient\Rpt\医保处方(长春).mrt</ReportFile>
  <ReportGuid>33c6d1c11b7949fb8ac6941df0fd28eb</ReportGuid>
  <ReportName>门诊处方表</ReportName>
  <ReportUnit>Centimeters</ReportUnit>
  <ReportVersion>2011.2.1026</ReportVersion>
  <Script>using System;
using System.Drawing;
using System.Windows.Forms;
using System.Data;
using Stimulsoft.Controls;
using Stimulsoft.Base.Drawing;
using Stimulsoft.Report;
using Stimulsoft.Report.Dialogs;
using Stimulsoft.Report.Components;

namespace Reports
{
    public class ARNet_Document : Stimulsoft.Report.StiReport
    {
        public ARNet_Document()        {
            this.InitializeComponent();
        }

        #region StiReport Designer generated code - do not modify
        #endregion StiReport Designer generated code - do not modify
    }
}
</Script>
  <ScriptLanguage>CSharp</ScriptLanguage>
  <Styles isList="true" count="0" />
</StiSerializer>