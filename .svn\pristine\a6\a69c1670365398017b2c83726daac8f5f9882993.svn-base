﻿Imports System.Text
Imports DCSoft.WinForms.Controls
Imports ZTHisInpatient


Public Class EmrStation
    Dim V_Finish As Boolean = False             '初始化完成
    Dim dt As New DataTable
    Dim dtset As New DataTable
    Dim bllBl As New BLLOld.B_Bl
    Dim iCount As Integer  '查询出的病人数
    Private _CurrentPatient As ModelOld.M_PatientInfo = Nothing
    Private CurrentItem As DCCardListViewItem = Nothing
    Public Shared vSelectedNodeTag As String
    Dim sql As String = ""
    Dim BllEmr_Mblb As New BLLOld.B_Emr_Mblb
    Dim Emr_bl As New BLLOld.B_Emr_Bl
    Dim yyks As New BLLOld.B_Zd_YyKs
    Dim yybq As New BLLOld.B_Zd_YyBq1


    Private Sub EmrStation_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        Call Init()
        Call CxButton_Click(Nothing, Nothing)

        If HisPara.PublicConfig.DzblCkXz = "1" Then
            sql = "Emr_Bl.ks_code='" & HisVar.HisVar.XmKs & "' and "
        ElseIf HisPara.PublicConfig.DzblCkXz = "2" Then
            sql = "Emr_Bl.ys_code='" & HisVar.HisVar.JsrYsCode & "' and "
        End If

    End Sub


    Private Sub DcCardListViewControl1_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles DcCardListViewControl1.Load
        Call InitDCCardListViewTemplate()     '初始化控件

        AddHandler DcCardListViewControl1.MouseClick, AddressOf DcCardListViewControl1_MouseClick
    End Sub

#Region "控件初始化"

    Private Sub InitDCCardListViewTemplate()

        DcCardListViewControl1.CardBorderColor = Color.Blue
        DcCardListViewControl1.CardBorderWith = 2
        DcCardListViewControl1.ShowCardShade = True
        DcCardListViewControl1.ImageAnimateInterval = 100
        DcCardListViewControl1.CardWidth = 155
        DcCardListViewControl1.CardHeight = 180
        DcCardListViewControl1.TooltipWidth = 290
        DcCardListViewControl1.TooltipHeight = 180


        DcCardListViewControl1.CardTemplate.AddImage("FaceImage", Nothing, 3, 3, 71, 63)
        Dim ChuangWeiHao As DCCardStringItem = Me.DcCardListViewControl1.CardTemplate.AddString("Area", "病区", 80, 10, 80, 21)
        ChuangWeiHao.Align = StringAlignment.Near
        ChuangWeiHao.FontName = "宋体"
        ChuangWeiHao.FontSize = 12
        ChuangWeiHao.FontStyle = FontStyle.Bold
        ChuangWeiHao.Color = Color.Blue

        Dim NianLing As DCCardStringItem = Me.DcCardListViewControl1.CardTemplate.AddString("BedName", "病床", 80, 31, 80, 21)
        NianLing.Align = StringAlignment.Near
        NianLing.FontName = "宋体"
        NianLing.FontSize = 12
        NianLing.FontStyle = FontStyle.Bold


        Dim XingMing As DCCardStringItem = Me.DcCardListViewControl1.CardTemplate.AddString("Name", Nothing, 2, 70, 130, 21)
        XingMing.Align = StringAlignment.Near
        XingMing.FontName = "宋体"
        XingMing.FontSize = 12
        XingMing.FontStyle = FontStyle.Bold


        Dim JingBing As DCCardStringItem = Me.DcCardListViewControl1.CardTemplate.AddString("Diagnose", Nothing, 2, 91, 151, 21)
        JingBing.Align = StringAlignment.Near
        JingBing.FontName = "宋体"
        JingBing.FontSize = 12


        Dim YuJiaoJin_Label As DCCardStringItem = Me.DcCardListViewControl1.CardTemplate.AddString(Nothing, "押金：", 2, 112, 75, 21)
        YuJiaoJin_Label.Align = StringAlignment.Near
        YuJiaoJin_Label.FontName = "宋体"
        YuJiaoJin_Label.FontSize = 12

        Dim YuJiaoJin As DCCardStringItem = Me.DcCardListViewControl1.CardTemplate.AddString("TotalCost", Nothing, 65, 112, 90, 21)
        YuJiaoJin.Align = StringAlignment.Near
        YuJiaoJin.FontName = "宋体"
        YuJiaoJin.FontSize = 12

        Dim ShiJiFeiYong_Label As DCCardStringItem = Me.DcCardListViewControl1.CardTemplate.AddString(Nothing, "总费用：", 2, 133, 75, 21)
        ShiJiFeiYong_Label.Align = StringAlignment.Near
        ShiJiFeiYong_Label.FontName = "宋体"
        ShiJiFeiYong_Label.FontSize = 12

        Dim ShiJiFeiYong As DCCardStringItem = Me.DcCardListViewControl1.CardTemplate.AddString("SpendCost", Nothing, 65, 133, 90, 21)
        ShiJiFeiYong.Align = StringAlignment.Near
        ShiJiFeiYong.FontName = "宋体"
        ShiJiFeiYong.FontSize = 12

        Dim ShiJiYuE_Label As DCCardStringItem = Me.DcCardListViewControl1.CardTemplate.AddString(Nothing, "余额：", 2, 154, 75, 21)
        ShiJiYuE_Label.Align = StringAlignment.Near
        ShiJiYuE_Label.FontName = "宋体"
        ShiJiYuE_Label.FontSize = 12
        ShiJiYuE_Label.FontStyle = FontStyle.Bold

        Dim ShiJiYuE As DCCardStringItem = Me.DcCardListViewControl1.CardTemplate.AddString("Balance", Nothing, 65, 154, 90, 21)
        ShiJiYuE.Align = StringAlignment.Near
        ShiJiYuE.FontName = "宋体"
        ShiJiYuE.FontSize = 12
        ShiJiYuE.FontStyle = FontStyle.Bold
        DcCardListViewControl1.CardTemplate.AddImage("ShenqingCy", Nothing, 23, 46, 107, 87)


        Dim xm_Label As DCCardStringItem = Me.DcCardListViewControl1.TooltipContentItems.AddString(Nothing, "姓  名：", 40, 10, 90, 21)
        xm_Label.FontName = "宋体"
        xm_Label.FontSize = 13
        xm_Label.FontStyle = FontStyle.Bold
        xm_Label.Color = Color.Blue

        Dim xm As DCCardStringItem = Me.DcCardListViewControl1.TooltipContentItems.AddString("Name", Nothing, 130, 10, 130, 21)
        xm.FontName = "宋体"
        xm.FontSize = 13
        xm.FontStyle = FontStyle.Bold
        xm.Color = Color.Blue

        Dim Ry_RyDate_Label As DCCardStringItem = Me.DcCardListViewControl1.TooltipContentItems.AddString(Nothing, "入院时间：", 2, 31, 90, 21)
        Ry_RyDate_Label.FontName = "宋体"
        Ry_RyDate_Label.FontSize = 12
        Ry_RyDate_Label.FontStyle = FontStyle.Bold

        Dim Ry_RyDate As DCCardStringItem = Me.DcCardListViewControl1.TooltipContentItems.AddString("Ry_RyDate", Nothing, 90, 31, 160, 21)
        Ry_RyDate.FontName = "宋体"
        Ry_RyDate.FontSize = 12
        Ry_RyDate.FontStyle = FontStyle.Bold

        Dim Ks_Name_Label As DCCardStringItem = Me.DcCardListViewControl1.TooltipContentItems.AddString(Nothing, "科    室：", 2, 52, 90, 21)
        Ks_Name_Label.FontName = "宋体"
        Ks_Name_Label.FontSize = 12
        Ks_Name_Label.FontStyle = FontStyle.Bold

        Dim Ks_Name As DCCardStringItem = Me.DcCardListViewControl1.TooltipContentItems.AddString("Ks_Name", Nothing, 90, 52, 125, 21)
        Ks_Name.FontName = "宋体"
        Ks_Name.FontSize = 12
        Ks_Name.FontStyle = FontStyle.Bold

        Dim Ys_Name_Label As DCCardStringItem = Me.DcCardListViewControl1.TooltipContentItems.AddString(Nothing, "主治医师：", 2, 73, 90, 21)
        Ys_Name_Label.FontName = "宋体"
        Ys_Name_Label.FontSize = 12
        Ys_Name_Label.FontStyle = FontStyle.Bold

        Dim Ys_Name As DCCardStringItem = Me.DcCardListViewControl1.TooltipContentItems.AddString("Ys_Name", Nothing, 90, 73, 125, 21)
        Ys_Name.FontName = "宋体"
        Ys_Name.FontSize = 12
        Ys_Name.FontStyle = FontStyle.Bold

        Dim Bxlb_Name_Label As DCCardStringItem = Me.DcCardListViewControl1.TooltipContentItems.AddString(Nothing, "病人类别：", 2, 94, 90, 21)
        Bxlb_Name_Label.FontName = "宋体"
        Bxlb_Name_Label.FontSize = 12
        Bxlb_Name_Label.FontStyle = FontStyle.Bold

        Dim Bxlb_Name As DCCardStringItem = Me.DcCardListViewControl1.TooltipContentItems.AddString("Bxlb_Name", Nothing, 90, 94, 125, 21)
        Bxlb_Name.FontName = "宋体"
        Bxlb_Name.FontSize = 12
        Bxlb_Name.FontStyle = FontStyle.Bold

        Dim Ry_Address_Label As DCCardStringItem = Me.DcCardListViewControl1.TooltipContentItems.AddString(Nothing, "家庭地址：", 2, 115, 90, 21)
        Ry_Address_Label.FontName = "宋体"
        Ry_Address_Label.FontSize = 12
        Ry_Address_Label.FontStyle = FontStyle.Bold

        Dim Ry_Address As DCCardStringItem = Me.DcCardListViewControl1.TooltipContentItems.AddString("Ry_Address", Nothing, 90, 115, 125, 21)
        Ry_Address.FontName = "宋体"
        Ry_Address.FontSize = 12
        Ry_Address.FontStyle = FontStyle.Bold

        Dim HospitalDay_Label As DCCardStringItem = Me.DcCardListViewControl1.TooltipContentItems.AddString(Nothing, "在院天数：", 2, 136, 90, 21)
        HospitalDay_Label.FontName = "宋体"
        HospitalDay_Label.FontSize = 12
        HospitalDay_Label.FontStyle = FontStyle.Bold

        Dim HospitalDay As DCCardStringItem = Me.DcCardListViewControl1.TooltipContentItems.AddString("HospitalDay", Nothing, 90, 136, 125, 21)
        HospitalDay.FontName = "宋体"
        HospitalDay.FontSize = 12
        HospitalDay.FontStyle = FontStyle.Bold
    End Sub
    Public Sub Init()

        With Me.DzblTreeView
            .Nodes.Clear()
            .FullRowSelect = True
            .HideSelection = False
            .HotTracking = True
            .Scrollable = True
            .ShowRootLines = False
            .Sorted = False
        End With

        With ZtSingleComobo
            .Additem = "在院"
            .Additem = "出院"
            .DisplayColumns(1).Visible = False
            .DroupDownWidth = .Width
            .SelectedIndex = 0
        End With


        KsDateEdit.Value = Now.AddDays(-30)


        With ksDtComobo
            .DataView = yyks.Getksname("").Tables(0).DefaultView
            .Init_Colum("Ks_Code", "科室编码", 0, "右")
            .Init_Colum("Ks_Name", "科室名称", 80, "中")
            .Init_Colum("Ks_Jc", "科室简称", 0, "右")
            .DisplayMember = "Ks_Name"
            .ValueMember = "Ks_Code"
            .RowFilterNotTextNull = "Ks_Jc"
            .RowFilterTextNull = ""
            .DroupDownWidth = .Width - 40
            .MaxDropDownItems = 15
            .SelectedValue = -1
        End With

        With bqDtComobo
            .DataView = yybq.GetAllList().Tables(0).DefaultView
            .Init_Colum("Bq_Code", "病区编码", 0, "右")
            .Init_Colum("Bq_Name", "病区名称", 80, "中")
            .Init_Colum("Bq_Jc", "病区简称", 0, "右")
            .DisplayMember = "Bq_Name"
            .ValueMember = "Bq_Code"
            .RowFilterNotTextNull = "Bq_Jc"
            .RowFilterTextNull = ""
            .DroupDownWidth = .Width - 40
            .MaxDropDownItems = 15
            .SelectedValue = -1
        End With

    End Sub


#End Region

#Region "控件动作"
    Private Sub ZtSingleComobo_RowChange(sender As Object, e As System.EventArgs) Handles ZtSingleComobo.RowChange
        If ZtSingleComobo.Text = "出院" Then
            KsDateEdit.Enabled = True
            JsDateEdit.Enabled = True
            CwTextBox.Enabled = False
        Else
            KsDateEdit.Enabled = False
            JsDateEdit.Enabled = False
            CwTextBox.Enabled = True

        End If
    End Sub


    Private Sub P_Init_Data1(ByVal V_Mblb_Code As String)
        If BllEmr_Mblb.Exists(V_Mblb_Code) Or V_Mblb_Code = "00000" Then
        ElseIf V_Mblb_Code = "99998" Then

            Dim V_Form = New XyBl_Basy(_CurrentPatient.Bl_Code)
            BaseFunc.BaseFunc.addTabControl(V_Form, _CurrentPatient.Name + "西医病案首页")

        ElseIf V_Mblb_Code = "99999" Then

            Dim V_Form = New ZyBl_Basy(_CurrentPatient.Bl_Code)
            BaseFunc.BaseFunc.addTabControl(V_Form, _CurrentPatient.Name + "中医病案首页")
        Else
            Dim emr_bl_mode As New ModelOld.M_Emr_Bl
            emr_bl_mode = Emr_bl.GetModel(DzblTreeView.SelectedNode.Tag)
            Dim V_Form As New ZtHis.Emr.EmrEditBl(emr_bl_mode, False)
            V_Form.Name = V_Form.Name & _CurrentPatient.Bl_id
            BaseFunc.BaseFunc.addTabControl(V_Form, _CurrentPatient.Name & "-" & DzblTreeView.SelectedNode.Text)
        End If
    End Sub


    Private Sub CxButton_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles CxButton.Click

        Dim thread As New Threading.Thread(AddressOf Inital)
        thread.IsBackground = True
        thread.Start()


    End Sub
#End Region

#Region "Tree"
    Private Sub Init_Tree()
        V_Finish = False
        '根节点

        DzblTreeView.Nodes.Clear()
        Dim My_Node As New TreeNode
        With My_Node
            .Tag = "00000"
            .Text = _CurrentPatient.Name
            .ImageIndex = 0
            .SelectedImageIndex = 0
        End With

        DzblTreeView.Nodes.Add(My_Node)

        Dim My_XyBasy As New TreeNode
        With My_XyBasy
            .Tag = "99998"
            .Text = "西医病案首页"
            .ImageIndex = 0
            .SelectedImageIndex = 0
        End With
        DzblTreeView.Nodes.Add(My_XyBasy)

        Dim My_Basy As New TreeNode
        With My_Basy
            .Tag = "99999"
            .Text = "中医病案首页"
            .ImageIndex = 0
            .SelectedImageIndex = 0
        End With

        DzblTreeView.Nodes.Add(My_Basy)

        For Each Emrbl As ModelOld.M_Emr_Bl In Emr_bl.GetModelList(sql & "bl_Code='" & _CurrentPatient.Bl_Code & "' order by Emr_Mb.Mb_Code")
            Dim childnode As New TreeNode
            childnode = TreeGrow(Emrbl)
            If childnode IsNot Nothing Then
                My_Node.Nodes.Add(childnode)
            End If
        Next
        '一级数据

        With Me.DzblTreeView
            .SelectedNode = DzblTreeView.TopNode
            .SelectedNode.ExpandAll()
            .Select()
        End With
        V_Finish = True
    End Sub

    Private Function TreeGrow(ByVal _Emr_Bl As ModelOld.M_Emr_Bl) As TreeNode
        '给患者的每个病历生成叶子节点
        Dim leafNode As New TreeNode
        With leafNode
            .Name = _Emr_Bl.id
            .Tag = _Emr_Bl.id
            .Text = _Emr_Bl.Mb_Name
            .ImageIndex = 3
            .SelectedImageIndex = 3
        End With
        Dim _node() As TreeNode
        '查询叶子节点的父节点是否存在，存在加进去，不存在生成一个父节点
        _node = DzblTreeView.Nodes.Find(_Emr_Bl.Mblb_Code, True)
        If _node.Length > 0 Then
            _node(0).Nodes.Add(leafNode)
            Return Nothing
        End If
        Dim mblb As ModelOld.M_Emr_Mblb
        mblb = BllEmr_Mblb.GetModel(_Emr_Bl.Mblb_Code)
        Dim leafNodeFather As New TreeNode
        leafNodeFather = ProduceNode(mblb.Mblb_Code, mblb.Mblb_Name)
        leafNodeFather.Nodes.Add(leafNode)
        TreeGrow = FindFarther(leafNodeFather, mblb.Father_Code)
    End Function

    Private Function FindFarther(ByVal childnode As TreeNode, ByVal father_Code As String) As TreeNode
        If father_Code <> "00000" Then
            Dim _node() As TreeNode
            '查询节点的父节点是否存在，存在加进去，不存在生成一个父节点
            _node = DzblTreeView.Nodes.Find(father_Code, True)
            If _node.Length > 0 Then
                _node(0).Nodes.Add(childnode)
                Return Nothing
            End If
            Dim mblb As ModelOld.M_Emr_Mblb
            mblb = BllEmr_Mblb.GetModel(father_Code)
            Dim childnodeFather As New TreeNode
            childnodeFather = ProduceNode(mblb.Mblb_Code, mblb.Mblb_Name)
            childnodeFather.Nodes.Add(childnode)
            Return FindFarther(childnodeFather, mblb.Father_Code)
        Else
            Return childnode
        End If
    End Function

    Private Function ProduceNode(ByVal Name As String, ByVal Text As String) As TreeNode
        Dim _node As New TreeNode
        With _node
            .Name = Name
            .Tag = Name
            .Text = Text
            .ImageIndex = 1
            .SelectedImageIndex = 2
        End With
        Return _node
    End Function

#End Region


#Region "自定义函数"
    Delegate Sub MyInvoke()
    Private Sub Inital()
        If Me.InvokeRequired = True Then
            Dim del As New MyInvoke(AddressOf Inital)
            Me.BeginInvoke(del)
        Else
            Call Data_Query()
            Dim ds As List(Of ModelOld.M_PatientInfo) = GetPatientEntities() '导入病人信息
            DcCardListViewControl1.DataSource = ds
            Dim ZyCount As Integer = bllBl.GetRecordCount(" Ry_CyJsr IS  NULL")
            Label2.Text = iCount & "人"
            Call Deposit_Caution()
        End If
    End Sub

    Private Sub Data_Query()
        Dim strSql As New StringBuilder


        strSql.Append("SELECT  bl.Yy_code,bl.bl_code,bl.Ks_Code,Ks_Name,bl.Ks_code,V_YyBc.Bc_Code,Bc_Name,Bc_Jc,Bq_Code,Bq_Name,isnull(Emr_GuiDang,0) Emr_GuiDang, ")
        strSql.Append("Jb_Code,Ry_Name,Ry_Jc,Ry_Sex,Ry_Sfzh,Ry_Address,Jb_Code,Jb_Name ,bl.Ys_Code,Ys_Name,Bl.Bxlb_Code,Bxlb_Name,Ry_RyDate,Ry_Cyjsr,Ry_Csdate, ")
        strSql.Append("isnull(Jf_Money,0)Jf_Money,Isnull(Xf_YpMoney,0)Xf_YpMoney,Isnull(Xf_Money,0)-Isnull(Xf_YpMoney,0) Xf_XmMoney,")
        strSql.Append("ISNULL(Xf_Money,0)Xf_Money,isnull(Jf_Money,0)-isnull(Xf_Money,0) AS New_Money,DATEDIFF(DAY,Ry_RyDate,GETDATE()) HospitalDay,Cy_Qr")
        If ZtSingleComobo.Text = "在院" Then
            strSql.Append(" FROM (SELECT * FROM dbo.Bl WHERE Ry_Cyjsr IS  NULL ")
        Else
            strSql.Append(" FROM (SELECT * FROM dbo.Bl WHERE Ry_Cyjsr IS not NULL and Ry_CyDate BETWEEN '" & KsDateEdit.Value & "' and '" & JsDateEdit.Value & "' ")
        End If

        If Trim(ksDtComobo.Text & "") <> "" Then
            strSql.Append(" and  Ks_Code = '" & ksDtComobo.SelectedValue & "' ")
        End If

        If Trim(XmTextBox.Text & "") <> "" Then
            strSql.Append("and  Ry_Name like '%" & XmTextBox.Text & "%'")
        End If

        If HisPara.PublicConfig.ZyCfKsXz = "是" Then
            strSql.Append(" and bl.Ks_Code='" & HisVar.HisVar.XmKs & "' ")
        End If
        strSql.Append(" ) Bl LEFT JOIN dbo.Zd_YyKs ON  Zd_YyKs.Ks_Code = Bl.Ks_Code")
        strSql.Append(" LEFT JOIN dbo.Zd_YyYs ON Zd_YyYs.Ys_Code = Bl.Ys_Code ")
        strSql.Append(" LEFT JOIN dbo.Zd_Bxlb ON  Zd_Bxlb.Bxlb_Code = Bl.Bxlb_Code")
        If bqDtComobo.SelectedValue Is Nothing Then

        Else

        End If
        If ZtSingleComobo.Text <> "出院" And Trim(CwTextBox.Text & "") <> "" Then
            strSql.Append("  join ( select * from V_YyBc ")
            strSql.Append("where Bc_Name like '%" & CwTextBox.Text & "%' ")
        Else
            strSql.Append(" left join ( select * from V_YyBc ")
        End If

        strSql.Append(" ) V_YyBc ON  Bl.Bc_Code=V_YyBc.Bc_Code ")
        strSql.Append(" LEFT join (Select Sum(Cf_Money) AS Xf_Money,Sum(Cf_YpMoney) as Xf_YpMoney,Bl_Code From Bl_Cf where Cf_Qr='是' Group By Bl_Code) a on Bl.Bl_Code=a.Bl_Code ")
        strSql.Append(" left join(Select Sum(Jf_Money) AS Jf_Money,Bl_Code From Bl_Jf Group By Bl_Code) b on Bl.Bl_Code = b.Bl_Code")
        If bqDtComobo.SelectedValue Is Nothing Then

        Else
            strSql.Append(" where Bq_Code='" & bqDtComobo.SelectedValue & "'")
        End If
        strSql.Append(" order by Ry_RyDate")
        dt = HisVar.HisVar.Sqldal.Query(strSql.ToString()).Tables(0)
    End Sub

    Private Function GetPatientEntities() As List(Of ModelOld.M_PatientInfo)
        Dim image1 As Image = MyResources.C_Resources.getimage("boy")
        Dim image2 As Image = MyResources.C_Resources.getimage("girl")
        Dim entities As New List(Of ModelOld.M_PatientInfo)()
        iCount = 0
        For Each row In dt.Rows
            Dim pa As New ModelOld.M_PatientInfo()
            With pa
                .Bl_Code = row("Bl_Code") & ""
                .Area = row("Bq_Name") & ""
                .Name = row("Ry_Name") & ""
                .Sex = Microsoft.VisualBasic.Switch(Trim(row("Ry_Sex") & "") = "男", 1, Trim(row("Ry_Sex") & "") = "女", 0, Trim(row("Ry_Sex") & "") = "", 2)
                .BedID = row("Bc_code") & ""
                .BedName = row("Bc_Name") & ""
                .Diagnose = row("Jb_Name") & ""
                If Trim(row("Bl_Code") & "") = "" Then
                    .SpendCost = Nothing
                Else
                    .SpendCost = row("Xf_Money")
                    iCount += 1
                End If
                If Trim(row("Ry_Sfzh")) = "" Then
                    .Age = Year(Now()) - Year(row("Ry_Csdate"))
                Else
                    .Age = Year(Now()) - row("Ry_Sfzh").ToString.Substring(6, 4)
                End If

                .TotalCost = IIf(Trim(row("Bl_Code") & "") = "", Nothing, row("Jf_Money"))
                .Balance = IIf(Trim(row("Bl_Code") & "") = "", Nothing, row("New_Money"))
                .Ry_RyDate = row("Ry_RyDate") & ""
                .Ks_Name = row("Ks_Name") & ""
                .Ys_Name = row("Ys_Name") & ""
                .Ys_code = row("Ys_Code") & ""
                .Bxlb_Name = row("Bxlb_Name") & ""
                .Ry_Address = row("Ry_Address") & ""
                .HospitalDay = IIf(row("HospitalDay") & "" <> "", row("HospitalDay") & "天", row("HospitalDay") & "")
                .Cy_Qr = row("Cy_Qr") & ""
                If .Sex = 1 Then
                    .FaceImage = image1
                ElseIf .Sex = 0 Then
                    .FaceImage = image2
                Else
                    .FaceImage = Nothing
                End If

                If row("Ry_Cyjsr") & "" <> "" Then
                    .ShenqingCy = MyResources.C_Resources.getimage("已出院")
                Else
                    .ShenqingCy = Nothing
                End If
                .Ks_code = row("ks_code") & ""
                .Emr_GuiDang = row("Emr_GuiDang")
            End With
            entities.Add(pa)
        Next
        Return entities
    End Function

    Private Sub Deposit_Caution()
        For Each item In DcCardListViewControl1.Items
            If Trim(item.GetValue("name") & "") = "" Then
                item.SetValue("TotalCost", "")
                item.SetValue("SpendCost", "")
                item.SetValue("Balance", "")
            ElseIf Trim(item.GetValue("name") & "") <> "" And Trim(item.GetValue("Balance") & "") < HisPara.PublicConfig.Qfed And
                item.GetValue("ShenqingCy") Is Nothing Then
                item.BorderColor = Color.Red
                item.BorderWidth = 3
                item.Invalidate()
            End If
        Next
    End Sub
#End Region

    Private Sub MyButton1_Click(sender As System.Object, e As System.EventArgs) Handles MyButton1.Click
        If _CurrentPatient Is Nothing Then
            MsgBox("请选择病人")
            Exit Sub
        End If
        If HisVar.HisVar.XmKs & "" = "" Then
            MsgBox("您不属于任何科室，不能添加病例！", MsgBoxStyle.Information + MsgBoxStyle.OkOnly, "提示:")
            Exit Sub
        End If
        If HisVar.HisVar.JsrYsCode & "" = "" Then
            MsgBox("请绑定医师，不能添加病例！", MsgBoxStyle.Information + MsgBoxStyle.OkOnly, "提示:")
            Exit Sub
        End If
        Dim strSql As New StringBuilder
        strSql.Append("SELECT Emr_GuiDang FROM dbo.Bl WHERE Bl_Code='" & _CurrentPatient.Bl_Code & "'")
        dtset = HisVar.HisVar.Sqldal.Query(strSql.ToString()).Tables(0)
        If dtset.Rows(0).Item("Emr_GuiDang") Is DBNull.Value OrElse dtset.Rows(0).Item("Emr_GuiDang") = False Then
            _CurrentPatient.insertflage = True
            _CurrentPatient.Bl_id = ""
            _CurrentPatient.Mb_code = ""
            _CurrentPatient.Mb_name = ""
            Dim V_Form As New ZtHis.Emr.EmrAddBl(_CurrentPatient)
            BaseFunc.BaseFunc.addTabControl(V_Form, _CurrentPatient.Name & "-" & sender.Text)
        Else
            MsgBox("病人已经归档不可添加电子病例", MsgBoxStyle.Critical + MsgBoxStyle.OkOnly, "提示:")
            Exit Sub
        End If
    End Sub

    Private Sub DzblTreeView_MouseDoubleClick(sender As Object, e As System.Windows.Forms.MouseEventArgs) Handles DzblTreeView.MouseDoubleClick
        If V_Finish = False Then Exit Sub '     '初始化完成
        If HisVar.HisVar.XmKs & "" = "" Then
            MsgBox("您不属于任何科室，不能添加病例！", MsgBoxStyle.Information + MsgBoxStyle.OkOnly, "提示:")
            Exit Sub
        End If
        If HisVar.HisVar.JsrYsCode & "" = "" Then
            MsgBox("请绑定医师，不能添加病例！", MsgBoxStyle.Information + MsgBoxStyle.OkOnly, "提示:")
            Exit Sub
        End If
        If HisVar.HisVar.JsrColor = Nothing Then

            MsgBox("请设置电子病历痕迹颜色", MsgBoxStyle.Critical + MsgBoxStyle.OkOnly, "提示:")

            Exit Sub
        End If
        Call P_Init_Data1(Me.DzblTreeView.SelectedNode.Tag)
        vSelectedNodeTag = DzblTreeView.SelectedNode.Tag
    End Sub

    Private Sub DzblTreeView_MouseDown(sender As System.Object, e As System.Windows.Forms.MouseEventArgs) Handles DzblTreeView.MouseDown
        If e.Button = Windows.Forms.MouseButtons.Right Then
            If BllEmr_Mblb.Exists(DzblTreeView.SelectedNode.Tag) Or DzblTreeView.SelectedNode.Tag = "00000" Then
                DzblTreeView.ContextMenuStrip = Nothing
            Else
                DzblTreeView.ContextMenuStrip = ContextMenuStrip1
            End If
        End If
    End Sub

    Private Sub Node_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles DeleNode.Click
        If Emr_bl.Delete(DzblTreeView.SelectedNode.Tag) Then
            HisControl.msg.Show("病例删除成功!", "提示")
            Init_Tree()
        Else
            HisControl.msg.Show("病例删除失败!", "提示")
        End If
    End Sub

    Private Sub DcCardListViewControl1_MouseClick(sender As System.Object, e As System.Windows.Forms.MouseEventArgs) Handles DcCardListViewControl1.MouseClick
        If e.Button = System.Windows.Forms.MouseButtons.Left Then
            _CurrentPatient = Nothing
            CurrentItem = Me.DcCardListViewControl1.GetItemAt(e.X, e.Y) '获取条目位置
            If CurrentItem Is Nothing Then Exit Sub

            _CurrentPatient = TryCast(CurrentItem.DataBoundItem, ModelOld.M_PatientInfo)
            Init_Tree()
            If _CurrentPatient.Emr_GuiDang = True Then
                MyButton1.Enabled = False
            Else
                MyButton1.Enabled = True
            End If

        End If
    End Sub
End Class



