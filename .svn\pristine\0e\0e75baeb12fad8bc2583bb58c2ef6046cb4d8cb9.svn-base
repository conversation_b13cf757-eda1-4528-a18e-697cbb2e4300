﻿<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<StiSerializer version="1.02" type="Net" application="StiReport">
  <Dictionary Ref="1" type="Dictionary" isKey="true">
    <BusinessObjects isList="true" count="0" />
    <Databases isList="true" count="0" />
    <DataSources isList="true" count="2">
      <住院日结单 Ref="2" type="DataTableSource" isKey="true">
        <Alias>住院日结单</Alias>
        <Columns isList="true" count="5">
          <value>V_Lb,System.String</value>
          <value>Bxlb_Name,System.String</value>
          <value>Cf_Lb,System.String</value>
          <value>V_Order,System.String</value>
          <value>Cf_Money,System.Decimal</value>
        </Columns>
        <Dictionary isRef="1" />
        <Name>住院日结单</Name>
        <NameInSource>住院日结单</NameInSource>
      </住院日结单>
      <住院分类汇总 Ref="3" type="DataTableSource" isKey="true">
        <Alias>住院分类汇总</Alias>
        <Columns isList="true" count="6">
          <value>Jf_XjMoney,System.Decimal</value>
          <value>Jf_JkkMoney,System.Decimal</value>
          <value>Bl_M_Th,System.Decimal</value>
          <value>Sk_Money,System.Decimal</value>
          <value>Cy_Rc,System.Int32</value>
          <value>Bxlb_Name,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Name>住院分类汇总</Name>
        <NameInSource>住院分类汇总</NameInSource>
      </住院分类汇总>
    </DataSources>
    <Relations isList="true" count="0" />
    <Report isRef="0" />
    <Variables isList="true" count="4">
      <value>,操作员,操作员,System.String,,False,False</value>
      <value>,标题,标题,System.String,,False,False</value>
      <value>,汇总时间段,汇总时间段,System.String,,False,False</value>
      <value>,打印时间,打印时间,System.String,,False,False</value>
    </Variables>
  </Dictionary>
  <EngineVersion>EngineV2</EngineVersion>
  <GlobalizationStrings isList="true" count="0" />
  <MetaTags isList="true" count="0" />
  <Pages isList="true" count="1">
    <Page1 Ref="4" type="Page" isKey="true">
      <Border>None;Black;2;Solid;False;4;Black</Border>
      <Brush>Transparent</Brush>
      <Components isList="true" count="3">
        <ReportTitleBand1 Ref="5" type="ReportTitleBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,0.4,27.7,6.5</ClientRectangle>
          <Components isList="true" count="10">
            <Text1 Ref="6" type="Text" isKey="true">
              <Border>Bottom;Black;1;Double;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>3.1,0.3,13,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>黑体,15,Regular,Point,False,134</Font>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text1</Name>
              <Page isRef="4" />
              <Parent isRef="5" />
              <Text>{标题}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text1>
            <Text4 Ref="7" type="Text" isKey="true">
              <Border>None;Black;1;Double;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>2,1,10,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Regular,Point,False,134</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text4</Name>
              <Page isRef="4" />
              <Parent isRef="5" />
              <Text>{汇总时间段}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text4>
            <Text2 Ref="8" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>12,1,5,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Regular,Point,False,134</Font>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text2</Name>
              <Page isRef="4" />
              <Parent isRef="5" />
              <Text>{打印时间}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="9" type="CustomFormat" isKey="true">
                <StringFormat>yyyy-MM-dd HH:mm:ss</StringFormat>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text2>
            <Text5 Ref="10" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>1.3,5.9,2.2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Regular,Point,False,134</Font>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text5</Name>
              <Page isRef="4" />
              <Parent isRef="5" />
              <Text>患者类别</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text5>
            <Text7 Ref="11" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>3.5,5.9,3,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Regular,Point,False,134</Font>
              <Guid>3bc83af0700c4362be93b28ff3549bf6</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text7</Name>
              <Page isRef="4" />
              <Parent isRef="5" />
              <Text>现金押金</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text7>
            <Text8 Ref="12" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>9.5,5.9,2.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Regular,Point,False,134</Font>
              <Guid>abb521736c8a4bb6adcd44122b38b478</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text8</Name>
              <Page isRef="4" />
              <Parent isRef="5" />
              <Text>出院退回</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text8>
            <Text9 Ref="13" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>12.3,5.9,2.9,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Regular,Point,False,134</Font>
              <Guid>8212d6b5ccea4f32926f49cedb1434c4</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text9</Name>
              <Page isRef="4" />
              <Parent isRef="5" />
              <Text>实收金额</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text9>
            <Text14 Ref="14" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>15.2,5.9,2.2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Regular,Point,False,134</Font>
              <Guid>903deba24ce94e42bc3e7684d1bb4b4f</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text14</Name>
              <Page isRef="4" />
              <Parent isRef="5" />
              <Text>出院人次</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text14>
            <Text3 Ref="15" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>6.5,5.9,3,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Regular,Point,False,134</Font>
              <Guid>bacdbc4031324597971765e460a2ad65</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text3</Name>
              <Page isRef="4" />
              <Parent isRef="5" />
              <Text>健康卡押金</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text3>
            <CrossTab2 Ref="16" type="Stimulsoft.Report.CrossTab.StiCrossTab" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,2,19,3.4</ClientRectangle>
              <Components isList="true" count="10">
                <CrossTab2_ColTotal1 Ref="17" type="CrossColumnTotal" isKey="true">
                  <Border>All;[155:155:155];1;Solid;False;4;Black</Border>
                  <Brush>[255:255:255]</Brush>
                  <ClientRectangle>4.95,0.45,0,0.9</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Font>宋体,9.75,Regular,Point,False,0</Font>
                  <Guid>09c37151d621423fb0d99a08c96070bb</Guid>
                  <HorAlignment>Center</HorAlignment>
                  <Margins>0,0,0,0</Margins>
                  <Name>CrossTab2_ColTotal1</Name>
                  <Page isRef="4" />
                  <Parent isRef="16" />
                  <Restrictions>AllowMove, AllowResize, AllowSelect, AllowChange</Restrictions>
                  <Text>Total</Text>
                  <TextBrush>Black</TextBrush>
                  <Type>Expression</Type>
                </CrossTab2_ColTotal1>
                <CrossTab2_ColTotal2 Ref="18" type="CrossColumnTotal" isKey="true">
                  <Border>All;[155:155:155];1;Solid;False;4;Black</Border>
                  <Brush>[255:255:255]</Brush>
                  <ClientRectangle>3.45,0.85,1.5,0.5</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Font>宋体,9.75,Regular,Point,False,134</Font>
                  <Guid>2c5dab5d1b70492f933ec22c2d2bee0d</Guid>
                  <HorAlignment>Center</HorAlignment>
                  <Margins>0,0,0,0</Margins>
                  <Name>CrossTab2_ColTotal2</Name>
                  <Page isRef="4" />
                  <Parent isRef="16" />
                  <Restrictions>AllowMove, AllowResize, AllowSelect, AllowChange</Restrictions>
                  <Text>费用合计</Text>
                  <TextBrush>Black</TextBrush>
                  <Type>Expression</Type>
                </CrossTab2_ColTotal2>
                <CrossTab2_RowTotal1 Ref="19" type="CrossRowTotal" isKey="true">
                  <Border>All;[155:155:155];1;Solid;False;4;Black</Border>
                  <Brush>[255:255:255]</Brush>
                  <ClientRectangle>0,1.8,1.7,0.5</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Font>宋体,9.75,Regular,Point,False,134</Font>
                  <Guid>519dd480beb54ef2859736e8eb1108ff</Guid>
                  <HorAlignment>Center</HorAlignment>
                  <Margins>0,0,0,0</Margins>
                  <Name>CrossTab2_RowTotal1</Name>
                  <Page isRef="4" />
                  <Parent isRef="16" />
                  <Restrictions>AllowMove, AllowResize, AllowSelect, AllowChange</Restrictions>
                  <Text>合计</Text>
                  <TextBrush>Black</TextBrush>
                  <Type>Expression</Type>
                </CrossTab2_RowTotal1>
                <CrossTab2_Row1_Title Ref="20" type="CrossTitle" isKey="true">
                  <Border>All;[155:155:155];1;Solid;False;4;Black</Border>
                  <Brush>White</Brush>
                  <ClientRectangle>0,0.45,1.7,0.9</ClientRectangle>
                  <Font>Arial,8</Font>
                  <Guid>c3f85a1f9e0b45b79f805a9ff6dd588d</Guid>
                  <HorAlignment>Center</HorAlignment>
                  <Margins>0,0,0,0</Margins>
                  <Name>CrossTab2_Row1_Title</Name>
                  <Page isRef="4" />
                  <Parent isRef="16" />
                  <Restrictions>AllowMove, AllowResize, AllowSelect, AllowChange</Restrictions>
                  <TextBrush>[105:105:105]</TextBrush>
                  <Type>Expression</Type>
                  <TypeOfComponent>Row:CrossTab2_Row1</TypeOfComponent>
                </CrossTab2_Row1_Title>
                <CrossTab2_LeftTitle Ref="21" type="CrossTitle" isKey="true">
                  <Border>All;[155:155:155];1;Solid;False;4;Black</Border>
                  <Brush>White</Brush>
                  <ClientRectangle>0,0,1.7,0.4</ClientRectangle>
                  <Font>Arial,8</Font>
                  <Guid>e1b2860acf004ae1bfc3759276dd960b</Guid>
                  <HorAlignment>Center</HorAlignment>
                  <Margins>0,0,0,0</Margins>
                  <Name>CrossTab2_LeftTitle</Name>
                  <Page isRef="4" />
                  <Parent isRef="16" />
                  <Restrictions>AllowMove, AllowResize, AllowSelect, AllowChange</Restrictions>
                  <Text>住院日结单</Text>
                  <TextBrush>Black</TextBrush>
                  <TypeOfComponent>LeftTitle</TypeOfComponent>
                </CrossTab2_LeftTitle>
                <CrossTab2_Row1 Ref="22" type="CrossRow" isKey="true">
                  <Alias>Cf_Lb</Alias>
                  <Border>All;[155:155:155];1;Solid;False;4;Black</Border>
                  <Brush>White</Brush>
                  <ClientRectangle>0,1.4,1.7,0.4</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <DisplayValue>{住院日结单.Cf_Lb}</DisplayValue>
                  <Font>宋体,9.75,Regular,Point,False,0</Font>
                  <Guid>e060f4dfbe634fd28124a9550f7d267a</Guid>
                  <HorAlignment>Center</HorAlignment>
                  <Margins>0,0,0,0</Margins>
                  <Name>CrossTab2_Row1</Name>
                  <Page isRef="4" />
                  <Parent isRef="16" />
                  <Restrictions>AllowMove, AllowResize, AllowSelect, AllowChange</Restrictions>
                  <SortType>ByValue</SortType>
                  <Text>Cf_Lb</Text>
                  <TextBrush>Black</TextBrush>
                  <TotalGuid>519dd480beb54ef2859736e8eb1108ff</TotalGuid>
                  <Value>{住院日结单.V_Order}</Value>
                </CrossTab2_Row1>
                <CrossTab2_Column1 Ref="23" type="CrossColumn" isKey="true">
                  <Alias>V_Lb</Alias>
                  <Border>All;[155:155:155];1;Solid;False;4;Black</Border>
                  <Brush>White</Brush>
                  <ClientRectangle>1.75,0.45,3.2,0.4</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <DisplayValue>{住院日结单.V_Lb}</DisplayValue>
                  <Font>宋体,9.75,Bold,Point,False,134</Font>
                  <Guid>a8b4494418ef448e97a57918be0284d0</Guid>
                  <HorAlignment>Center</HorAlignment>
                  <Margins>0,0,0,0</Margins>
                  <Name>CrossTab2_Column1</Name>
                  <Page isRef="4" />
                  <Parent isRef="16" />
                  <Restrictions>AllowMove, AllowResize, AllowSelect, AllowChange</Restrictions>
                  <ShowTotal>False</ShowTotal>
                  <SortDirection>Desc</SortDirection>
                  <Text>V_Lb</Text>
                  <TextBrush>Black</TextBrush>
                  <TotalGuid>09c37151d621423fb0d99a08c96070bb</TotalGuid>
                  <Value>{住院日结单.V_Lb}</Value>
                </CrossTab2_Column1>
                <CrossTab2_Column2 Ref="24" type="CrossColumn" isKey="true">
                  <Alias>Bxlb_Name</Alias>
                  <Border>All;[155:155:155];1;Solid;False;4;Black</Border>
                  <Brush>White</Brush>
                  <ClientRectangle>1.75,0.85,1.7,0.5</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <DisplayValue>{住院日结单.Bxlb_Name}</DisplayValue>
                  <Font>宋体,9.75,Regular,Point,False,0</Font>
                  <Guid>7acc9e9fd63249ba94f738ae10290e83</Guid>
                  <HorAlignment>Center</HorAlignment>
                  <Margins>0,0,0,0</Margins>
                  <Name>CrossTab2_Column2</Name>
                  <Page isRef="4" />
                  <Parent isRef="16" />
                  <Restrictions>AllowMove, AllowResize, AllowSelect, AllowChange</Restrictions>
                  <Text>Bxlb_Name</Text>
                  <TextBrush>Black</TextBrush>
                  <TotalGuid>2c5dab5d1b70492f933ec22c2d2bee0d</TotalGuid>
                  <Value>{住院日结单.Bxlb_Name}</Value>
                </CrossTab2_Column2>
                <CrossTab2_Sum1 Ref="25" type="CrossSummary" isKey="true">
                  <Alias>Cf_Money</Alias>
                  <Border>All;[155:155:155];1;Solid;False;4;Black</Border>
                  <Brush>[255:255:255]</Brush>
                  <ClientRectangle>1.75,1.4,1.7,0.4</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Font>宋体,9.75,Regular,Point,False,0</Font>
                  <Guid>1a93fe71e79f46c8aaa67dbaed7a82d2</Guid>
                  <Margins>0,0,0,0</Margins>
                  <Name>CrossTab2_Sum1</Name>
                  <Page isRef="4" />
                  <Parent isRef="16" />
                  <Restrictions>AllowMove, AllowResize, AllowSelect, AllowChange</Restrictions>
                  <Text>0</Text>
                  <TextBrush>Black</TextBrush>
                  <Type>Expression</Type>
                  <Value>{住院日结单.Cf_Money}</Value>
                </CrossTab2_Sum1>
                <CrossTab2_RightTitle Ref="26" type="CrossTitle" isKey="true">
                  <Border>All;[155:155:155];1;Solid;False;4;Black</Border>
                  <Brush>White</Brush>
                  <ClientRectangle>1.75,0,3.2,0.4</ClientRectangle>
                  <Enabled>False</Enabled>
                  <Font>Arial,8</Font>
                  <Guid>8bec03f7685241848f636d35377f3fcb</Guid>
                  <HorAlignment>Center</HorAlignment>
                  <Margins>0,0,0,0</Margins>
                  <Name>CrossTab2_RightTitle</Name>
                  <Page isRef="4" />
                  <Parent isRef="16" />
                  <Restrictions>AllowMove, AllowResize, AllowSelect, AllowChange</Restrictions>
                  <TextBrush>[105:105:105]</TextBrush>
                  <Type>Expression</Type>
                  <TypeOfComponent>RightTitle</TypeOfComponent>
                </CrossTab2_RightTitle>
              </Components>
              <Conditions isList="true" count="0" />
              <DataRelationName />
              <DataSourceName>住院日结单</DataSourceName>
              <EmptyValue />
              <Filters isList="true" count="0" />
              <HorAlignment>Center</HorAlignment>
              <Name>CrossTab2</Name>
              <Page isRef="4" />
              <Parent isRef="5" />
              <Sort isList="true" count="0" />
            </CrossTab2>
          </Components>
          <Conditions isList="true" count="0" />
          <Name>ReportTitleBand1</Name>
          <Page isRef="4" />
          <Parent isRef="4" />
        </ReportTitleBand1>
        <DataBand1 Ref="27" type="DataBand" isKey="true">
          <Brush>Transparent</Brush>
          <BusinessObjectGuid isNull="true" />
          <ClientRectangle>0,7.7,27.7,0.6</ClientRectangle>
          <Components isList="true" count="6">
            <Text10 Ref="28" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>1.3,0,2.2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>96251dbd92e040f9bfe9e4a85e5c2c75</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text10</Name>
              <Page isRef="4" />
              <Parent isRef="27" />
              <Text>{住院分类汇总.Bxlb_Name}</Text>
              <TextBrush>Black</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text10>
            <Text11 Ref="29" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>3.5,0,3,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>f40e5ada21314a57a0f2dbc511e376b3</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text11</Name>
              <Page isRef="4" />
              <Parent isRef="27" />
              <Text>{住院分类汇总.Jf_XjMoney}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text11>
            <Text12 Ref="30" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>9.5,0,2.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>e71d208eff054749bc7e0f3fe86bf2b6</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text12</Name>
              <Page isRef="4" />
              <Parent isRef="27" />
              <Text>{住院分类汇总.Bl_M_Th}</Text>
              <TextBrush>Black</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text12>
            <Text13 Ref="31" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>12.3,0,2.9,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>3893fdbc01724a8c966fb13774f36db9</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text13</Name>
              <Page isRef="4" />
              <Parent isRef="27" />
              <Text>{住院分类汇总.Sk_Money}</Text>
              <TextBrush>Black</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text13>
            <Text15 Ref="32" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>15.2,0,2.2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>5b3c8b462fc947f8a86dc941b53fa3ee</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text15</Name>
              <Page isRef="4" />
              <Parent isRef="27" />
              <Text>{住院分类汇总.Cy_Rc}</Text>
              <TextBrush>Black</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text15>
            <Text6 Ref="33" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>6.5,0,3,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>36eb175a176d451b86aa9d8610cbfa4a</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text6</Name>
              <Page isRef="4" />
              <Parent isRef="27" />
              <Text>{住院分类汇总.Jf_JkkMoney}</Text>
              <TextBrush>Black</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text6>
          </Components>
          <Conditions isList="true" count="0" />
          <DataRelationName isNull="true" />
          <DataSourceName>住院分类汇总</DataSourceName>
          <Filters isList="true" count="0" />
          <Name>DataBand1</Name>
          <Page isRef="4" />
          <Parent isRef="4" />
          <Sort isList="true" count="0" />
        </DataBand1>
        <ReportSummaryBand1 Ref="34" type="ReportSummaryBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,9.1,27.7,2.3</ClientRectangle>
          <Components isList="true" count="12">
            <Text50 Ref="35" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0.9,1.1,1.6,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text50</Name>
              <Page isRef="4" />
              <Parent isRef="34" />
              <Text>药房签字：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text50>
            <Text51 Ref="36" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>6.55,1.1,1.95,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>369f219ad2a2493288969724e9fe33d5</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text51</Name>
              <Page isRef="4" />
              <Parent isRef="34" />
              <Text>收款人签字：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text51>
            <Text53 Ref="37" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>2.5,1.1,4,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text53</Name>
              <Page isRef="4" />
              <Parent isRef="34" />
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text53>
            <Text54 Ref="38" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>8.5,1.1,4,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>2ab1848e61874f21a836eb529a5cdbe8</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text54</Name>
              <Page isRef="4" />
              <Parent isRef="34" />
              <TextBrush>Black</TextBrush>
            </Text54>
            <Text52 Ref="39" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>12.5,1.1,1.6,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>c686db24119b48dba4221964f5f2863a</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text52</Name>
              <Page isRef="4" />
              <Parent isRef="34" />
              <Text>会计签字：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text52>
            <Text55 Ref="40" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>14.1,1.1,4,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>10066f814e1345fa9e701643d6c32064</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text55</Name>
              <Page isRef="4" />
              <Parent isRef="34" />
              <TextBrush>Black</TextBrush>
            </Text55>
            <Text16 Ref="41" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>1.3,0,2.2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>8a3d7c3af56949b59082fc53eaa30ac3</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text16</Name>
              <Page isRef="4" />
              <Parent isRef="34" />
              <Text>合计</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text16>
            <Text17 Ref="42" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>3.5,0,3,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>8df0d6e917eb4f07a31127ddee9fc455</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text17</Name>
              <Page isRef="4" />
              <Parent isRef="34" />
              <Text>{Sum(DataBand1,住院分类汇总.Jf_XjMoney)}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text17>
            <Text18 Ref="43" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>9.5,0,2.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>ddb44e7ef62d42ae8c798afccac8c438</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text18</Name>
              <Page isRef="4" />
              <Parent isRef="34" />
              <Text>{Sum(DataBand1,住院分类汇总.Bl_M_Th)}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text18>
            <Text25 Ref="44" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>12.3,0,2.9,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>bb9053f5538c4c4583e53cf10e0f6870</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text25</Name>
              <Page isRef="4" />
              <Parent isRef="34" />
              <Text>{Sum(DataBand1,住院分类汇总.Sk_Money)}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text25>
            <Text26 Ref="45" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>15.2,0,2.2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>e94dd611016d4ec7864325dd593013de</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text26</Name>
              <Page isRef="4" />
              <Parent isRef="34" />
              <Text>{Sum(DataBand1,住院分类汇总.Cy_Rc)}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text26>
            <Text19 Ref="46" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>6.5,0,3,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>d7f60d0569f64942957abec49409b4e5</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text19</Name>
              <Page isRef="4" />
              <Parent isRef="34" />
              <Text>{Sum(DataBand1,住院分类汇总.Jf_JkkMoney)}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text19>
          </Components>
          <Conditions isList="true" count="0" />
          <Name>ReportSummaryBand1</Name>
          <Page isRef="4" />
          <Parent isRef="4" />
        </ReportSummaryBand1>
      </Components>
      <Conditions isList="true" count="0" />
      <Guid>2a1844cd22fa45a493e6260cccb64b3b</Guid>
      <Margins>1,1,1,1</Margins>
      <Name>Page1</Name>
      <Orientation>Landscape</Orientation>
      <PageHeight>21</PageHeight>
      <PageWidth>29.7</PageWidth>
      <Report isRef="0" />
      <Watermark Ref="47" type="Stimulsoft.Report.Components.StiWatermark" isKey="true">
        <Font>Arial,100</Font>
        <TextBrush>[50:0:0:0]</TextBrush>
      </Watermark>
    </Page1>
  </Pages>
  <PrinterSettings Ref="48" type="Stimulsoft.Report.Print.StiPrinterSettings" isKey="true" />
  <ReferencedAssemblies isList="true" count="8">
    <value>System.Dll</value>
    <value>System.Drawing.Dll</value>
    <value>System.Windows.Forms.Dll</value>
    <value>System.Data.Dll</value>
    <value>System.Xml.Dll</value>
    <value>Stimulsoft.Controls.Dll</value>
    <value>Stimulsoft.Base.Dll</value>
    <value>Stimulsoft.Report.Dll</value>
  </ReferencedAssemblies>
  <ReportAlias>住院日结</ReportAlias>
  <ReportChanged>8/26/2016 9:19:24 AM</ReportChanged>
  <ReportCreated>12/22/2011 4:28:53 PM</ReportCreated>
  <ReportFile>F:\01.His\唐山版His\his2010\output\Rpt\住院日结横打.mrt</ReportFile>
  <ReportGuid>a53eb71d32c4457eabd64b559f6b54ec</ReportGuid>
  <ReportName>住院日结</ReportName>
  <ReportUnit>Centimeters</ReportUnit>
  <ReportVersion>2011.2.1026</ReportVersion>
  <Script>using System;
using System.Drawing;
using System.Windows.Forms;
using System.Data;
using Stimulsoft.Controls;
using Stimulsoft.Base.Drawing;
using Stimulsoft.Report;
using Stimulsoft.Report.Dialogs;
using Stimulsoft.Report.Components;

namespace Reports
{
    public class Report : Stimulsoft.Report.StiReport
    {
        public Report()        {
            this.InitializeComponent();
        }

        #region StiReport Designer generated code - do not modify#endregion StiReport Designer generated code - do not modify
    }
}
</Script>
  <ScriptLanguage>CSharp</ScriptLanguage>
  <Styles isList="true" count="0" />
</StiSerializer>