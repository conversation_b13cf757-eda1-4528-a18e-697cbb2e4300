﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace ERX.Model
{
    /// <summary>
    /// 电子处方信息査询
    /// </summary>
    public class MdlhospRxDetlQueryIn
    {
        public string fixmedinsCode { get; set; }   //定点医疗机构编码
        public string hiRxno { get; set; }  //医保处方号
        public string mdtrtId { get; set; } //医保就诊ID 
        public string ecToken { get; set; } //电子凭证令牌
        public string psnName { get; set; } //人员名称
        public string psnCertType { get; set; } //人员证件类型 
        public string certno { get; set; }  //证件号码

    }
    public class MdlhospRxDetlQueryOut
    {
        public string hiRxno { get; set; }  //医保处方编号 
        public string fixmedinsCode { get; set; }  //定点医疗机构编号
        public string fixmedinsName { get; set; }  //定点医药机构名称
        public string rxStasCodg { get; set; }  //医保处方状态编码
        public string rxStasName { get; set; }  //医保处方状态名称
        public string rxUsedStasCodg { get; set; }  //医保处方使用状态编码
        public string rxUsedStasName { get; set; }  //医保处方使用状态名称
        public string prscTime { get; set; }  //开方时间 
        public string rxDrugCnt { get; set; }  //药品数量(剂数)
        public string rxUsedWayCodg { get; set; }  //处方整剂用法编号
        public string rxUsedWayName { get; set; }  //处方整剂用法名称
        public string rxFrquCodg { get; set; }  //处方整剂频次编号
        public string rxFrquName { get; set; }  //处方整剂频次名称
        public string rxDosunt { get; set; }  //处方整剂剂量单位
        public string rxDoscnt { get; set; }//处方整剂单次剂量数
        public string rxDrordDscr { get; set; }  //方整剂医嘱说明
        public string valiDays { get; set; }  //处方有效天数 
        public string valiEndTime { get; set; }  //有效截止时间
        public string reptFlag { get; set; }  //复用(多次)使用标志
        public string maxReptCnt { get; set; }  //最大复用次数 
        public string reptdCnt { get; set; }  //己复用次数 
        public string minInrvDays { get; set; }  //使用最小间隔(天数)
        public string rxTypeCode { get; set; }  //处方类别编号 
        public string rxTypeName { get; set; }  //处方类别名称 
        public string longRxFlag { get; set; }  //长期处方标志
        public List<MdlrxDetlListOut> rxDetlList { get; set; }
        public MdlrxOtpinfoOut rxOtpinfo { get; set; }
        public List<MdlrxDiseListOut> rxDiseList { get; set; }


    }
    public class MdlrxDetlListOut
    {
        public string medListCodg { get; set; }  //医疗目录编码 
        public string fixmedinsHilistld { get; set; }  //定点医药机构目录编号
        public string hospPrepFlag { get; set; }  // 院内制剂标志
        public string rxItemTypeCode { get; set; }  //处方项目分类编码
        public string rxItemTypeName { get; set; }  //处方项目分类名称
        public string tcmdrugTypeName { get; set; }  //中药类别名称
        public string tcmdrugTypeCode { get; set; }  //中药类别代码 
        public string tcmherbFoote { get; set; }  //草药脚注
        public string mednTypeCode { get; set; }  //药物类型代码
        public string mednTypeName { get; set; }  //药物类型
        public string mainMedcFlag { get; set; }  //主要用药标志 
        public string urgtFlag { get; set; }  //加急标志 
        public string basMednFlag { get; set; }  //基本药物标志
        public string impDrugFlag { get; set; }  //是否进口药品
        public string drugProdname { get; set; }  //药品商品名
        public string gennameCodg { get; set; }  //通用名编码
        public string drugGenname { get; set; }  //药品通用名 
        public string drugDosform { get; set; }  //药品剂型 
        public string drugSpec { get; set; }  //药品规格 
        public string prdrName { get; set; }  //生厂厂家 
        public string drugPric { get; set; }  //药品单价 
        public string drugSumamt { get; set; }  //药品总金额
        public string medcWayCodg { get; set; }  //用药途径代码 
        public string medcWayDscr { get; set; }  //用药途径描述
        public string medcBegntime { get; set; }  //用药开始时间
        public string medcEndtime { get; set; }  //用药结束时间
        public string medcDays { get; set; }  //用药天数 
        public string drugCnt { get; set; }  //药品发药数量
        public string drugDosunt { get; set; }  //药品发药单位 
        public string sinDoscnt { get; set; }  //单次用量 
        public string sinDosunt { get; set; }  //单次剂量单位
        public string usedFrquCodg { get; set; }  //使用频次编码
        public string usedFrquName { get; set; }  //使用频次名称 
        public string drugTotlcnt { get; set; }  //用药总量 
        public string drugTotlcntEmp { get; set; }  //用药总量单位
        public string hospApprFlag { get; set; }  //医院审批标志


    }
    public class MdlrxOtpinfoOut
    {
        public string medType { get; set; }  //医疗类别
        public string iptOpNo { get; set; }  //住院/门诊号
        public string otpIptFlag { get; set; }  //门诊住院标志
        public string patnName { get; set; }  //患者姓名
        public string patnAge { get; set; }  //年龄
        public string patnHgt { get; set; }  //患者身高 
        public string patnWt { get; set; }  //患者体重
        public string gend { get; set; }  //性别 
        public string gesoVal { get; set; }  //妊娠（孕周)
        public string nwbFlag { get; set; }  //新生儿标志 
        public string nwbAge { get; set; }  //新生儿日、月龄
        public string suckPrdFag { get; set; }  //哺乳期标志 
        public string algsHis { get; set; }  //过敏史 
        public string insutype { get; set; }  //险种类型 
        public string prscDeptName { get; set; }  //开方科室名称
        public string prscDrName { get; set; }  //开方医师姓名
        public string pharName { get; set; }  //v药师姓名
        public string pharChkTime { get; set; }  //医疗机构药师审方时间
        public string mdtrtTime { get; set; }  //就诊时间 
        public string diseCodg { get; set; }  //病种编码
        public string diseName { get; set; }  //病种名称 
        public string spDiseFlag { get; set; }  //是否特殊病种 
        public string maindiagCode { get; set; }  //主诊断代码
        public string maindiagName { get; set; }  //主诊断名称 
        public string diseCondDscr { get; set; }  //疾病病情描述
        public string fstdiagFlag { get; set; }  //是否初诊 


    }
    public class MdlrxDiseListOut
    {
        public string diagType { get; set; }  //诊断类别 
        public string maindiagFlag { get; set; }  //主诊断标志 
        public string diagSrtNo { get; set; }  //诊断排序号
        public string diagCode { get; set; }  //诊断代码 
        public string diagName { get; set; }  //诊断名称
        public string diagDept { get; set; }  //诊断科室
        public string diagDrNo { get; set; }  //诊断医生编码
        public string diagDrName { get; set; }  //诊断医生姓名
        public string diagTime { get; set; }  //诊断时间 
        public string tcmDiseCode { get; set; }  //中医病名代码 
        public string tcmDiseName { get; set; }  //中医病名
        public string tcmsympCode { get; set; }  //中医症候代码 
        public string tcmsymp { get; set; }  //中医症候


    }
}
