﻿Imports System.Data.SqlClient
Imports System.Xml
Imports System.Text
Imports System.IO
Imports SocialExplorer.IO.FastDBF


Public Class BasyExport
    Private My_Table As New DataTable
    Public My_Cm As CurrencyManager
    Dim My_View As DataView
    Dim Xzqh As String = 130200           '行政区划代码

    Private Sub MyGrid1_Load(sender As System.Object, e As System.EventArgs) Handles MyGrid1.Load
        TableLayoutPanel1.Dock = DockStyle.Fill
        Call Form_Init()
    End Sub

    Private Sub Form_Init()

        With MyGrid1

            .Init_Column("选项", "IsCheck", 50, "中", "CHECK", True)
            .Init_Column("医院", "USERNAME", 0, "中", "", False)
            .Init_Column("编码", "BAH", 120, "中", "", False)
            .Init_Column("姓名", "XM", 100, "中", "", False)
            .Init_Column("电话", "DH", 100, "中", "", False)
            .Init_Column("住址", "HKDZ", 100, "中", "", False)
            .Init_Column("入院日期", "RYSJ", 100, "中", "", False)
            .Init_Column("出院日期", "CYSJ", 100, "中", "", False)
            .Init_Column("总费用", "ZFY", 100, "中", "", False)

        End With
        MyGrid1.MarqueeStyle = C1.Win.C1TrueDBGrid.MarqueeEnum.HighlightCell
        MyGrid1.Select()
        DateTimePicker1.Value = Format(Now, "yyyy-MM-dd 00:00:00")
        DateTimePicker2.Value = Format(Now, "yyyy-MM-dd 23:59:59")
    End Sub

    Public My_Dataset As New DataSet
    '新增病案首页导出
    Private Sub C1Button9_Click(sender As System.Object, e As System.EventArgs) Handles C1Button9.Click

        If Trim(MyTextBox1.Text) = "" Then
            MsgBox("请填写医院名称！")
            Exit Sub
        End If

        Call Export()

    End Sub
    Private Sub Export()

        '上传病案首页的网址中有详细的规则文档
        '退伍医院使用n41标准 原来唐山乡镇使用n43标准 部分字段名存在差异

        Dim DbfText As String
        Dim odbf As New DbfFile(Encoding.GetEncoding(936))
        DbfText = Trim(MyTextBox1.Text)
        Dim dialog As New FolderBrowserDialog()

        dialog.Description = "请选择文件路径"
        If dialog.ShowDialog() = DialogResult.OK Then
            Dim foldPath As String = dialog.SelectedPath    '获得选择的路径；  
            'LuJing = foldPath              '路径赋值给文本框；  
            FileCopy(Application.StartupPath & "\BasyExport_N41.dbf", foldPath & "\N41_" & DbfText & ".dbf")
            odbf.Open(foldPath + "\N41_" & DbfText & ".dbf", FileMode.Open)
        End If

        For Each My_Row As DataRow In My_Dataset.Tables("查询结果").Select("IsCheck=True")
            Dim orec As New DbfRecord(odbf.Header) With {
.AllowDecimalTruncate = True
}

            orec("USERNAME") = My_Row("BAH") & ""
            orec("YLFKFS") = My_Row("YLFKFS") & ""
            orec("JKKH") = My_Row("JKKH") & ""
            orec("ZYCS") = My_Row("ZYCS") & ""
            orec("BAH") = My_Row("BAH") & ""
            orec("XM") = My_Row("XM") & ""
            orec("XB") = My_Row("XB") & ""
            orec("CSRQ") = My_Row("CSRQ") & ""
            orec("NL") = My_Row("NL") & ""
            orec("GJ") = My_Row("GJ") & ""
            orec("BZYZSNL") = My_Row("BZYZSNL") & ""
            orec("XSECSTZ") = My_Row("XSECSTZ") & ""
            orec("XSERYTZ") = My_Row("XSERYTZ") & ""
            orec("CSD") = My_Row("CSD") & ""
            orec("GG") = My_Row("GG") & ""
            orec("MZ") = My_Row("MZ") & ""
            orec("SFZH") = My_Row("SFZH") & ""
            orec("ZY") = My_Row("ZY") & ""
            orec("HY") = My_Row("HY") & ""
            orec("XZZ") = My_Row("XZZ") & ""
            orec("DH") = My_Row("DH") & ""
            orec("YB1") = My_Row("YB1") & ""
            orec("HKDZ") = My_Row("HKDZ") & ""
            orec("YB2") = My_Row("YB2") & ""
            orec("GZDWJDZ") = My_Row("GZDWJDZ") & ""
            orec("DWDH") = My_Row("DWDH") & ""
            orec("YB3") = My_Row("YB3") & ""
            orec("LXRXM") = My_Row("LXRXM") & ""
            orec("GX") = My_Row("GX") & ""
            orec("DZ") = My_Row("DZ") & ""
            orec("DH2") = My_Row("DH2") & ""
            orec("RYTJ") = My_Row("RYTJ") & ""
            orec("RYSJ") = Format(My_Row("RYSJ"), "yyyyMMdd")
            orec("RYSJS") = My_Row("RYSJS") & ""
            orec("RYKB") = My_Row("RYKB") & ""
            orec("RYBF") = My_Row("RYBF") & ""
            orec("ZKKB") = My_Row("ZKKB") & ""
            orec("CYSJ") = Format(My_Row("CYSJ"), "yyyyMMdd")
            orec("CYSJS") = My_Row("CYSJS") & ""
            orec("CYKB") = My_Row("CYKB") & ""
            orec("CYBF") = My_Row("CYBF") & ""
            orec("SJZYTS") = My_Row("SJZYTS") & ""
            orec("MZZD") = My_Row("MZZD") & ""
            orec("JBBM") = My_Row("JBBM") & ""


            Dim strZd As String '用于查询患者的诊断信息 BL_FIRSTOUTDIAG 表
            strZd = "select DIAGNOSIS_CD as JBDM,DIAGNOSIS_NAME as QTZD  from BL_FIRSTOUTDIAG where ORG_CODE='" & My_Row("ORG_CODE") & "' and INPAT_FORM_NO='" & My_Row("INPAT_FORM_NO") & "' and REPORT_FORM_NO='" & My_Row("REPORT_FORM_NO") & "' "
            HisVar.HisVar.Sqldal.QueryDt(My_Dataset, strZd, "病案首页诊断", True)

#Region "诊断和诊断编码赋值"
            Dim zdCount As Int32 '用于标识当前是第几个其他诊断
            zdCount = 1

            Dim QTZD As String
            Dim JBDM As String

            For Each ZD_Row As DataRow In My_Dataset.Tables("病案首页诊断").Rows

                If zdCount > 15 Then
                    Exit For
                End If
                If ZD_Row("QTZD").ToString().Contains("主要诊断") Then

                    orec("ZYZD") = (ZD_Row("QTZD") & "").ToString().Replace("主要诊断", "")
                    orec("JBDM") = ZD_Row("JBDM") & ""

                End If
                If ZD_Row("QTZD").ToString().Contains("其他诊断") Then

                    QTZD = "QTZD" & zdCount   '用循环实现 多个其他诊断的赋值操作
                    JBDM = "JBDM" & zdCount

                    orec(QTZD) = (ZD_Row("QTZD") & "").ToString().Replace("其他诊断", "")
                    orec(JBDM) = ZD_Row("JBDM") & ""

                    zdCount += 1
                End If
            Next

#End Region

#Region "入院病情赋值"
            orec("RYBQ") = My_Row("RYBQ") & ""
            Dim RYBQ As String
            For i As Integer = 1 To 15

                RYBQ = "RYBQ" & i
                orec(RYBQ) = My_Row(RYBQ) & ""
            Next

#End Region

            orec("WBYY") = My_Row("WBYY") & ""
            orec("H23") = My_Row("H23") & ""
            orec("BLZD") = My_Row("BLZD") & ""
            orec("JBMM") = My_Row("JBMM") & ""
            orec("BLH") = My_Row("BLH") & ""
            orec("YWGM") = My_Row("YWGM") & ""
            orec("GMYW") = My_Row("GMYW") & ""
            orec("SWHZSJ") = My_Row("SWHZSJ") & ""
            orec("XX") = My_Row("XX") & ""
            orec("RH") = My_Row("RH") & ""
            orec("KZR") = My_Row("KZR") & ""
            orec("ZRYS") = My_Row("ZRYS") & ""
            orec("ZZYS") = My_Row("ZZYS") & ""
            orec("ZYYS") = My_Row("ZYYS") & ""
            orec("ZRHS") = My_Row("ZRHS") & ""
            orec("JXYS") = My_Row("JXYS") & ""
            orec("SXYS") = My_Row("SXYS") & ""
            orec("BMY") = My_Row("BMY") & ""
            orec("ZKYS") = My_Row("ZKYS") & ""
            orec("ZKHS") = My_Row("ZKHS") & ""
            orec("ZKRQ") = My_Row("ZKRQ") & ""
#Region "患者手术记录"

            Dim stroper As String '用于查询患者的 手术信息
            stroper = "SELECT OPERATION_CODE AS SSJCZBM,OPERATION_DTIME AS SSJCZRQ,OPERATION_LEVEL_CD AS SSJB,OPERATION_NAME AS SSJCZMC,SURGEON_NAME  AS SZ,ASSISTANT1_NAME AS YZ,ASSISTANT2_NAME AS EZ,INCISION_TYPE_CODE AS QKDJ,CASE INCISION_HEALING_CODE WHEN '0' THEN '9' ELSE INCISION_HEALING_CODE END  AS QKYHLB, CASE ANESTHESIA_CODE WHEN '0' THEN '9' ELSE ANESTHESIA_CODE END AS MZFS,ANESTHESIOLOGIST_NAME AS MZYS 
                     FROM BL_FIRSTOPER where ORG_CODE='" & My_Row("ORG_CODE") & "' and INPAT_FORM_NO='" & My_Row("INPAT_FORM_NO") & "' and REPORT_FORM_NO='" & My_Row("REPORT_FORM_NO") & "' "
            HisVar.HisVar.Sqldal.QueryDt(My_Dataset, stroper, "病案首页手术", True)

            Dim opercount As Int32
            opercount = 1

            Dim SSJCZBM As String
            Dim SSJCZRQ As String
            Dim SSJB As String
            Dim SSJCZMC As String
            Dim SZ As String
            Dim YZ As String
            Dim EZ As String
            Dim QKDJ As String
            Dim QKYHLB As String
            Dim MZFS As String
            Dim MZYS As String

            For Each operRow As DataRow In My_Dataset.Tables("病案首页手术").Rows
                If opercount > 7 Then
                    Exit For
                End If

                SSJCZBM = "SSJCZBM" & opercount
                SSJCZRQ = "SSJCZRQ" & opercount
                SSJB = "SSJB" & opercount
                SSJCZMC = "SSJCZMC" & opercount
                SZ = "SZ" & opercount
                YZ = "YZ" & opercount
                EZ = "EZ" & opercount
                QKDJ = "QKDJ" & opercount
                QKYHLB = "QKYHLB" & opercount
                MZFS = "MZFS" & opercount
                MZYS = "MZYS" & opercount

                orec(SSJCZBM) = operRow("SSJCZBM") & ""
                orec(SSJCZRQ) = operRow("SSJCZRQ") & ""
                orec(SSJB) = operRow("SSJB") & ""
                orec(SSJCZMC) = operRow("SSJCZMC") & ""
                orec(SZ) = operRow("SZ") & ""
                orec(YZ) = operRow("YZ") & ""
                orec(EZ) = operRow("EZ") & ""
                orec(QKDJ) = operRow("QKDJ") & ""
                orec(QKYHLB) = operRow("QKYHLB") & ""
                orec(MZFS) = operRow("MZFS") & ""
                orec(MZYS) = operRow("MZYS") & ""

                opercount += 1

            Next

#End Region


            orec("LYFS") = My_Row("LYFS") & ""
            orec("YZZY_YLJG") = My_Row("YZZY_YLJG") & ""
            orec("WSY_YLJG") = My_Row("WSY_YLJG") & ""
            orec("SFZZYJH") = My_Row("SFZZYJH") & ""
            orec("MD") = My_Row("MD") & ""
            orec("RYQ_T") = My_Row("RYQ_T") & ""
            orec("RYQ_XS") = My_Row("RYQ_XS") & ""
            orec("RYQ_F") = My_Row("RYQ_F") & ""
            orec("RYH_T") = My_Row("RYH_T") & ""
            orec("RYH_XS") = My_Row("RYH_XS") & ""
            orec("RYH_F") = My_Row("RYH_F") & ""
            orec("RYH_F") = My_Row("RYH_F") & ""

            orec("ZFY") = My_Row("ZFY") & "" '总费用

            orec("ZFJE") = IIf(My_Row("ZFJE") <= 0, "", My_Row("ZFJE")) '自付
            orec("YLFUF") = IIf(My_Row("YLFUF") <= 0, "", My_Row("YLFUF"))  '一般医疗服务费
            orec("ZLCZF") = IIf(My_Row("ZLCZF") <= 0, "", My_Row("ZLCZF")) '一般治疗操作费
            orec("HLF") = IIf(My_Row("HLF") <= 0, "", My_Row("HLF"))   '护理费
            orec("QTFY") = IIf(My_Row("QTFY") <= 0, "", My_Row("QTFY")) '其他费
            orec("BLZDF") = IIf(My_Row("BLZDF") <= 0, "", My_Row("BLZDF")) '诊断类：(5)病理诊断费
            orec("SYSZDF") = IIf(My_Row("SYSZDF") <= 0, "", My_Row("SYSZDF")) '实验室诊断费
            orec("YXXZDF") = IIf(My_Row("YXXZDF") <= 0, "", My_Row("YXXZDF")) '影像学诊断费
            orec("LCZDXMF") = IIf(My_Row("LCZDXMF") <= 0, "", My_Row("LCZDXMF"))
            orec("FSSZLXMF") = IIf(My_Row("FSSZLXMF") <= 0, "", My_Row("FSSZLXMF"))
            orec("WLZLF") = IIf(My_Row("WLZLF") <= 0, "", My_Row("WLZLF"))
            orec("SSZLF") = IIf(My_Row("SSZLF") <= 0, "", My_Row("SSZLF"))
            orec("MAF") = IIf(My_Row("MAF") <= 0, "", My_Row("MAF"))
            orec("SSF") = IIf(My_Row("SSF") <= 0, "", My_Row("SSF"))
            orec("KFF") = IIf(My_Row("KFF") <= 0, "", My_Row("KFF"))
            orec("ZYZLF") = IIf(My_Row("ZYZLF") <= 0, "", My_Row("ZYZLF"))

            orec("XYF") = IIf(My_Row("XYF") <= 0, "", My_Row("XYF"))
            orec("KJYWF") = IIf(My_Row("KJYWF") <= 0, "", My_Row("KJYWF"))
            orec("ZCYF") = IIf(My_Row("ZCYF") <= 0, "", My_Row("ZCYF"))
            orec("ZCYF1") = IIf(My_Row("ZCYF1") <= 0, "", My_Row("ZCYF1"))
            orec("XF") = IIf(My_Row("XF") <= 0, "", My_Row("XF"))
            orec("BDBLZPF") = IIf(My_Row("BDBLZPF") <= 0, "", My_Row("BDBLZPF"))
            orec("QDBLZPF") = IIf(My_Row("QDBLZPF") <= 0, "", My_Row("QDBLZPF"))
            orec("NXYZLZPF") = IIf(My_Row("NXYZLZPF") <= 0, "", My_Row("NXYZLZPF"))
            orec("XBYZLZPF") = IIf(My_Row("XBYZLZPF") <= 0, "", My_Row("XBYZLZPF"))
            orec("HCYYCLF") = IIf(My_Row("HCYYCLF") <= 0, "", My_Row("HCYYCLF"))
            orec("YYCLF") = IIf(My_Row("YYCLF") <= 0, "", My_Row("YYCLF"))
            orec("YCXYYCLF") = IIf(My_Row("YCXYYCLF") <= 0, "", My_Row("YCXYYCLF"))
            orec("QTF") = IIf(My_Row("QTF") <= 0, "", My_Row("QTF"))

            odbf.Write(orec, True)
        Next

        odbf.Close()
        MsgBox("N41_" & DbfText & ".dbf 已导出成功！")
    End Sub

    Private Sub Button1_Click(sender As System.Object, e As System.EventArgs) Handles Button1.Click
        Call Xz()

        Dim str As String = "SELECT 'FALSE' as IsCheck, PAY_WAY_CODE+1 AS YLFKFS,CARD_NO AS JKKH,CASE (SELECT COUNT(1) FROM bl WHERE Ry_Sfzh=ID_NO AND Ry_Sfzh !='') WHEN '0' THEN '1' ELSE (SELECT COUNT(1) FROM bl WHERE Ry_Sfzh=ID_NO AND Ry_Sfzh !='') END AS ZYCS,Bl_Code AS BAH,NAME AS XM,CASE SEX_CODE WHEN '0' THEN '1' WHEN '-' THEN '2' ELSE '9' END  AS XB,BIRTH_DATE AS CSRQ,AGE AS NL,'CHN' AS GJ,
       AGE_BABY AS BZYZSNL,BIRTH_WEIGHT AS XSECSTZ,IN_WEIGHT AS XSERYTZ,BIRTH_ADDR_PROVINCE AS CSD,Ry_Jg AS GG,NATIONALITY_CODE AS MZ,ID_NO AS SFZH,'90' AS ZY,case MARRIAGE_CODE when '0' then '未婚' when '1' then '已婚' when '2' then '丧偶' when '3' then '离婚' else '其他' end AS HY,
	   PRESENT_ADDR_PROVINCE AS XZZ,PRESENT_ADDR_TEL_NO AS DH,PRESENT_ADDR_POSTAL_CODE AS YB1,REGISTER_ADDR_PROVINCE AS HKDZ,'' AS YB2 ,EMPLOYER_NAME +' 地址：'+EMPLOYER_ADDR_PROVINCE AS GZDWJDZ,
	   EMPLOYER_TEL_NO AS DWDH,EMPLOYER_POSTAL_CODE AS YB3,BL_FIRSTPAGEXY.CONTACT_NAME AS LXRXM,(select Relationship_Name  from Dict_Relationship where Relationship_Code =CONTACT_RELATIONSHIP_CD) AS GX,BL_FIRSTPAGEXY.CONTACT_ADDRESS AS DZ,BL_FIRSTPAGEXY.CONTACT_TEL_NO AS DH2,
	   CASE IN_PATH_CODE WHEN '0' THEN '9' ELSE IN_PATH_CODE+1 END AS RYTJ,ADMISSION_DTIME AS RYSJ,0 AS RYSJS,'' AS RYKB,IN_DEPT_ROOM AS RYBF,MOVE_DEPT_CODE AS ZKKB,Ry_CyDate AS CYSJ,0 AS CYSJS,'' AS CYKB,
       OUT_DEPT_ROOM AS CYBF,ACTUAL_IN_DAYS AS SJZYTS,XyOUTPAT_DIAG AS MZZD,XyOUTPAT_DIAG_CODE AS JBBM,'' as RYBQ,
	   ''QTZD8,''JBDM8,''RYBQ8,''QTZD1,''JBDM1,''RYBQ1,''QTZD9,''JBDM9,''RYBQ9,''QTZD2,''JBDM2,''RYBQ2,''QTZD10,''JBDM10,''RYBQ10,''QTZD3,''JBDM3,''RYBQ3,''QTZD11,''JBDM11,''RYBQ11,''QTZD4,''JBDM4,''RYBQ4,''QTZD12,''JBDM12,''RYBQ12,
	   ''QTZD5,''JBDM5,''RYBQ5,''QTZD13,''JBDM13,''RYBQ13,''QTZD6,''JBDM6,''RYBQ6,''QTZD14,''JBDM14,''RYBQ14,''QTZD7,''JBDM7,''RYBQ7,''QTZD15,''JBDM15,''RYBQ15,'',DAMAGE_POISON_REASON AS WBYY,''H23,''BLZD,''JBMM,'','' AS BLH,
	   DRUG_ALLERGY_MARK AS YWGM,DRUG_ALLERGENS_NAME AS GMYW,AUTOPSY_MARK AS SWHZSJ,ABO_CODE AS XX,RH_CODE AS RH,DEPT_DIRECTOR_NAME AS KZR,CHIEF_DOCTOR_NAME AS ZRYS,IN_CHARGE_DOCTOR_NAME AS ZZYS,RESIDENT_DOCTOR_NAME AS ZYYS,RESP_NURSE_NAME AS ZRHS,
	   LEARNING_DOCTOR_NAME AS JXYS,INTERN_DOCTOR_NAME AS SXYS,CATALOGER_NAME AS BMY,CASE_QUALITY_CD AS BAZL,QC_DOCTOR_NAME AS ZKYS,QC_NURSE_NAME AS ZKHS,QC_DTIME AS ZKRQ,''SSJCZBM1,''SSJCZRQ1,''SSJB1,''SSJCZMC1,''SZ1,''YZ1,''EZ1,''QKDJ1,''QKYHLB1,
	   ''MZFS1,''MAYS1,''SSJCZBM2,''SSJCZRQ2,''SSJB2,''SSJCZMC2,''SZ2,''YZ2,''EZ2,''QKDJ2,''QKYHLB2,''MZFS2,''MZYS2,''SSJCZBM3,''SSJCZRQ3,''SSJB3,''SSJCZMC3,''SZ3,''YZ3,''EZ3,''QKDJ3,''QKYHLB3,''MZFS3,''MZYS3,
	   ''SSJCZBM4,''SSJCZRQ4,''SSJB4,''SSJCZM4,''SZ4,''YZ4,''EZ4,''QKDJ4,''QKYHLB4,''MZFS4,''MZYS4,''SSJCZBM5,''SSJCZRQ5,''SSJB5,''SSJCZM5,''SZ5,''YZ5,''EZ5,''QKDJ5,''QKYHLB5,''MZFS5,''MZYS5,
	   ''SSJCZBM6,''SSJCZRQ6,''SSJB6,''SSJCZM6,''SZ6,''YZ6,''EZ6,''QKDJ6,''QKYHLB6,''MZFS6,''MZYS6,''SSJCZBM7,''SSJCZRQ7,''SSJB7,''SSJCZM7,''SZ7,''YZ7,''EZ7,''QKDJ7,''QKYHLB7,''MZFS7,''MZYS7,
	   case DISCHARGE_CLASS_CD when 0 then 1 else  DISCHARGE_CLASS_CD  end AS LYFS,ORDER_REFERRAL_ORG AS YZZY_YLJG,ORDER_REFERRAL_ORG AS WSY_YLJG,'' AS SFZZYJH,REHOSP_AFTER31_PURPOSE AS MD,0 RYQ_T,0 RYQ_XS,0 RYQ_F,0 RYH_T,0 RYH_XS,0 RYH_F,
	   FEE_TOTAL AS ZFY,FEE_SELF_PAY AS ZFJE,FEE_GENERAL_MEDICAL AS YLFUF,FEE_GENERAL_TREAT AS ZLCZF,FEE_TEND AS HLF,FEE_MEDICAL_OTHER AS QTFY,FEE_PATHOLOGY AS BLZDF,FEE_LABORATORY AS SYSZDF,FEE_IMAGING AS YXXZDF,
	   FEE_CLINC AS LCZDXMF,FEE_NONSURGICAL_TREAT AS FSSZLXMF,FEE_CLIN_PHYSICAL AS WLZLF,FEE_SURGICAL_TREAT AS SSZLF,FEE_ANAES AS MAF,FEE_OPERATION AS SSF,FEE_RECOVERY AS KFF,FEE_CN_TREATMENT AS ZYZLF,
	   FEE_WESTERN_MEDICINE AS XYF,FEE_ANTIMICROBIAL AS KJYWF,FEE_CN_MEDICINE AS ZCYF,FEE_CN_HERBAL_MEDICINE AS ZCYF1,FEE_BLOOD  AS XF,FEE_ALBUMIN AS BDBLZPF,FEE_GLOBULIN AS QDBLZPF,FEE_BCF AS NXYZLZPF,
	   FEE_CYTOKINE AS XBYZLZPF,FEE_CHECK_MATERIAL AS HCYYCLF,FEE_TREAT_MATERIAL AS YYCLF,FEE_OPER_MATERIAL AS YCXYYCLF,FEE_OTHER AS QTF,ORG_CODE,INPAT_FORM_NO,REPORT_FORM_NO
       FROM BL_FIRSTPAGEXY LEFT JOIN dbo.Bl  ON Bl_Code=PATIENT_ID 
            
        WHERE Ry_Cyjsr <> 'NULL' AND Ry_RyDate BETWEEN '" & DateTimePicker1.Value & "' AND '" & DateTimePicker2.Value & "' "

        HisVar.HisVar.Sqldal.QueryDt(My_Dataset, str, "查询结果", True)

        My_Dataset.Tables("查询结果").Columns("IsCheck").ReadOnly = False
        MyGrid1.DataTable = My_Dataset.Tables("查询结果")

    End Sub

    Private Sub Xz()          '乡镇区划代码
        If HisPara.PublicConfig.XqName.Contains("路南") Then
            Xzqh = 130202
        End If
        If HisPara.PublicConfig.XqName.Contains("路北") Then
            Xzqh = 130203
        End If
        If HisPara.PublicConfig.XqName.Contains("古冶") Then
            Xzqh = 130204
        End If
        If HisPara.PublicConfig.XqName.Contains("开平") Then
            Xzqh = 130205
        End If
        If HisPara.PublicConfig.XqName.Contains("丰南") Then
            Xzqh = 130207
        End If
        If HisPara.PublicConfig.XqName.Contains("丰润") Then
            Xzqh = 130208
        End If
        If HisPara.PublicConfig.XqName.Contains("曹妃甸") Then
            Xzqh = 130209
        End If
        If HisPara.PublicConfig.XqName.Contains("滦县") Then
            Xzqh = 130223
        End If
        If HisPara.PublicConfig.XqName.Contains("滦南") Then
            Xzqh = 130224
        End If
        If HisPara.PublicConfig.XqName.Contains("乐亭") Then
            Xzqh = 130225
        End If
        If HisPara.PublicConfig.XqName.Contains("迁西") Then
            Xzqh = 130227
        End If
        If HisPara.PublicConfig.XqName.Contains("玉田") Then
            Xzqh = 130229
        End If
        If HisPara.PublicConfig.XqName.Contains("遵化") Then
            Xzqh = 130281
        End If
        If HisPara.PublicConfig.XqName.Contains("迁安") Then
            Xzqh = 130283
        End If
    End Sub

End Class