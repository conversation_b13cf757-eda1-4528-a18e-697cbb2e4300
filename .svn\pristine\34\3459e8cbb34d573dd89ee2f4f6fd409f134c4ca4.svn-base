﻿Imports System.Xml
Imports System.Reflection

Public Class Connect_Edit
    '获取当前程序的运行配置文件路径
    Dim Asm As Assembly = Assembly.GetExecutingAssembly()
    Dim _path As String = Asm.Location.Substring(0, (Asm.Location.LastIndexOf("\\") + 1)) + "His2010.exe.config"
    Dim _doc As New XmlDocument
    Dim _configString As String = "configuration/userSettings/His2010.My.MySettings/"
    Private Sub Connect_Edit_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        _doc.Load(_path)
        TextBox1.Text = GetConfigValue("DB_Ip")
        TextBox2.Text = GetConfigValue("DB_Name")
        TextBox3.Text = GetConfigValue("DB_Pwd")
    End Sub

    Private Sub Button1_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Button1.Click
        SaveConfig("DB_Ip", TextBox1.Text) '报销类别
        SaveConfig("DB_Name", TextBox2.Text) '服务器IP
        SaveConfig("DB_Pwd", TextBox3.Text) 'Interface路径

    End Sub

    Private Function GetConfigValue(ByVal _name As String) As String
        Dim configNode As System.Xml.XmlNode = _doc.SelectSingleNode(_configString & "setting[@name='" & _name & "']/value")
        If configNode IsNot Nothing Then
            Return configNode.InnerText
        End If
        Return ""
    End Function

    Private Sub SaveConfig(ByVal _name As String, ByVal _value As String)
        Dim configNode As System.Xml.XmlNode = _doc.SelectSingleNode(_configString & "setting[@name='" & _name & "']/value")
        If configNode IsNot Nothing Then
            Dim nls As System.Xml.XmlNodeList = configNode.ChildNodes
            Dim xn1 As System.Xml.XmlNode
            For Each xn1 In nls
                xn1.InnerText = _value
            Next xn1
            _doc.Save(_path)
        End If
    End Sub
End Class