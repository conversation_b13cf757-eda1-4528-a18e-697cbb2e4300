﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class LIS_TestXm2
    Inherits HisControl.BaseForm

    'Form 重写 Dispose，以清理组件列表。
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Windows 窗体设计器所必需的
    Private components As System.ComponentModel.IContainer

    '注意: 以下过程是 Windows 窗体设计器所必需的
    '可以使用 Windows 窗体设计器修改它。
    '不要使用代码编辑器修改它。
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(LIS_TestXm2))
        Me.TableLayoutPanel1 = New System.Windows.Forms.TableLayoutPanel()
        Me.CodeTextBox = New CustomControl.MyTextBox()
        Me.DevMyDtComobo1 = New CustomControl.MyDtComobo()
        Me.TestSampleMyDtComobo2 = New CustomControl.MyDtComobo()
        Me.NameMyTextBox2 = New CustomControl.MyTextBox()
        Me.JcMyTextBox3 = New CustomControl.MyTextBox()
        Me.ChannelMyTextBox4 = New CustomControl.MyTextBox()
        Me.AccuracyMyNumericEdit1 = New CustomControl.MyNumericEdit()
        Me.AddNumMyNumericEdit2 = New CustomControl.MyNumericEdit()
        Me.ConversionNumMyNumericEdit3 = New CustomControl.MyNumericEdit()
        Me.MemoTextBox5 = New CustomControl.MyTextBox()
        Me.Button1 = New CustomControl.MyButton()
        Me.Button2 = New CustomControl.MyButton()
        Me.C1CommandHolder1 = New C1.Win.C1Command.C1CommandHolder()
        Me.Move1 = New C1.Win.C1Command.C1Command()
        Me.Move2 = New C1.Win.C1Command.C1Command()
        Me.Move3 = New C1.Win.C1Command.C1Command()
        Me.Move4 = New C1.Win.C1Command.C1Command()
        Me.Move5 = New C1.Win.C1Command.C1Command()
        Me.Comm1 = New C1.Win.C1Command.C1Command()
        Me.Comm3 = New C1.Win.C1Command.C1Command()
        Me.C1CommandControl1 = New C1.Win.C1Command.C1CommandControl()
        Me.T_Label = New C1.Win.C1Input.C1Label()
        Me.C1CommandControl2 = New C1.Win.C1Command.C1CommandControl()
        Me.T_Label2 = New C1.Win.C1Input.C1Label()
        Me.Comm2 = New C1.Win.C1Command.C1Command()
        Me.Comm4 = New C1.Win.C1Command.C1Command()
        Me.Comm5 = New C1.Win.C1Command.C1Command()
        Me.Panel1 = New System.Windows.Forms.Panel()
        Me.T_Label3 = New C1.Win.C1Input.C1Label()
        Me.T_Line3 = New System.Windows.Forms.Label()
        Me.ToolBar1 = New C1.Win.C1Command.C1ToolBar()
        Me.Link6 = New C1.Win.C1Command.C1CommandLink()
        Me.Link1 = New C1.Win.C1Command.C1CommandLink()
        Me.Link2 = New C1.Win.C1Command.C1CommandLink()
        Me.Link7 = New C1.Win.C1Command.C1CommandLink()
        Me.Link3 = New C1.Win.C1Command.C1CommandLink()
        Me.Link4 = New C1.Win.C1Command.C1CommandLink()
        Me.Link5 = New C1.Win.C1Command.C1CommandLink()
        Me.Panel2 = New System.Windows.Forms.Panel()
        Me.T_Line1 = New System.Windows.Forms.Label()
        Me.C1ToolBar1 = New C1.Win.C1Command.C1ToolBar()
        Me.C1CommandLink1 = New C1.Win.C1Command.C1CommandLink()
        Me.C1CommandLink2 = New C1.Win.C1Command.C1CommandLink()
        Me.C1CommandLink3 = New C1.Win.C1Command.C1CommandLink()
        Me.C1CommandLink4 = New C1.Win.C1Command.C1CommandLink()
        Me.C1CommandLink5 = New C1.Win.C1Command.C1CommandLink()
        Me.MyGrid1 = New CustomControl.MyGrid()
        Me.MyTextBox1 = New CustomControl.MyTextBox()
        Me.TableLayoutPanel1.SuspendLayout()
        CType(Me.C1CommandHolder1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.T_Label, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.T_Label2, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.Panel1.SuspendLayout()
        CType(Me.T_Label3, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.ToolBar1.SuspendLayout()
        Me.Panel2.SuspendLayout()
        Me.SuspendLayout()
        '
        'TableLayoutPanel1
        '
        Me.TableLayoutPanel1.ColumnCount = 5
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 260.0!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 285.0!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 285.0!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 100.0!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 131.0!))
        Me.TableLayoutPanel1.Controls.Add(Me.CodeTextBox, 0, 0)
        Me.TableLayoutPanel1.Controls.Add(Me.DevMyDtComobo1, 0, 1)
        Me.TableLayoutPanel1.Controls.Add(Me.TestSampleMyDtComobo2, 1, 1)
        Me.TableLayoutPanel1.Controls.Add(Me.NameMyTextBox2, 1, 0)
        Me.TableLayoutPanel1.Controls.Add(Me.JcMyTextBox3, 2, 0)
        Me.TableLayoutPanel1.Controls.Add(Me.ChannelMyTextBox4, 2, 1)
        Me.TableLayoutPanel1.Controls.Add(Me.AccuracyMyNumericEdit1, 0, 2)
        Me.TableLayoutPanel1.Controls.Add(Me.AddNumMyNumericEdit2, 1, 2)
        Me.TableLayoutPanel1.Controls.Add(Me.ConversionNumMyNumericEdit3, 2, 2)
        Me.TableLayoutPanel1.Controls.Add(Me.MemoTextBox5, 0, 3)
        Me.TableLayoutPanel1.Dock = System.Windows.Forms.DockStyle.Top
        Me.TableLayoutPanel1.Location = New System.Drawing.Point(0, 0)
        Me.TableLayoutPanel1.Name = "TableLayoutPanel1"
        Me.TableLayoutPanel1.RowCount = 4
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 28.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 28.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 28.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 56.0!))
        Me.TableLayoutPanel1.Size = New System.Drawing.Size(1061, 130)
        Me.TableLayoutPanel1.TabIndex = 0
        '
        'CodeTextBox
        '
        Me.CodeTextBox.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.CodeTextBox.Captain = "编    码"
        Me.CodeTextBox.CaptainBackColor = System.Drawing.Color.Transparent
        Me.CodeTextBox.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.CodeTextBox.CaptainForeColor = System.Drawing.Color.DarkRed
        Me.CodeTextBox.CaptainWidth = 60.0!
        Me.CodeTextBox.ContentForeColor = System.Drawing.Color.Black
        Me.CodeTextBox.DisabledForeColor = System.Drawing.Color.FromArgb(CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer))
        Me.CodeTextBox.Location = New System.Drawing.Point(3, 4)
        Me.CodeTextBox.Multiline = False
        Me.CodeTextBox.Name = "CodeTextBox"
        Me.CodeTextBox.PasswordChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.CodeTextBox.ReadOnly = False
        Me.CodeTextBox.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.CodeTextBox.SelectionStart = 0
        Me.CodeTextBox.SelectStart = 0
        Me.CodeTextBox.Size = New System.Drawing.Size(254, 20)
        Me.CodeTextBox.TabIndex = 0
        Me.CodeTextBox.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.CodeTextBox.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle
        '
        'DevMyDtComobo1
        '
        Me.DevMyDtComobo1.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.DevMyDtComobo1.Captain = "检验仪器"
        Me.DevMyDtComobo1.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.DevMyDtComobo1.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.DevMyDtComobo1.CaptainWidth = 60.0!
        Me.DevMyDtComobo1.DataSource = Nothing
        Me.DevMyDtComobo1.ItemHeight = 16
        Me.DevMyDtComobo1.ItemTextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.DevMyDtComobo1.Location = New System.Drawing.Point(3, 32)
        Me.DevMyDtComobo1.MaximumSize = New System.Drawing.Size(10000, 20)
        Me.DevMyDtComobo1.MinimumSize = New System.Drawing.Size(0, 20)
        Me.DevMyDtComobo1.Name = "DevMyDtComobo1"
        Me.DevMyDtComobo1.ReadOnly = False
        Me.DevMyDtComobo1.Size = New System.Drawing.Size(254, 20)
        Me.DevMyDtComobo1.TabIndex = 3
        Me.DevMyDtComobo1.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        '
        'TestSampleMyDtComobo2
        '
        Me.TestSampleMyDtComobo2.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.TestSampleMyDtComobo2.Captain = "检 验 标 本"
        Me.TestSampleMyDtComobo2.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.TestSampleMyDtComobo2.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.TestSampleMyDtComobo2.CaptainWidth = 85.0!
        Me.TestSampleMyDtComobo2.DataSource = Nothing
        Me.TestSampleMyDtComobo2.ItemHeight = 16
        Me.TestSampleMyDtComobo2.ItemTextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.TestSampleMyDtComobo2.Location = New System.Drawing.Point(263, 32)
        Me.TestSampleMyDtComobo2.MaximumSize = New System.Drawing.Size(10000, 20)
        Me.TestSampleMyDtComobo2.MinimumSize = New System.Drawing.Size(0, 20)
        Me.TestSampleMyDtComobo2.Name = "TestSampleMyDtComobo2"
        Me.TestSampleMyDtComobo2.ReadOnly = False
        Me.TestSampleMyDtComobo2.Size = New System.Drawing.Size(279, 20)
        Me.TestSampleMyDtComobo2.TabIndex = 4
        Me.TestSampleMyDtComobo2.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        '
        'NameMyTextBox2
        '
        Me.NameMyTextBox2.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.NameMyTextBox2.Captain = "检验项目名称"
        Me.NameMyTextBox2.CaptainBackColor = System.Drawing.Color.Transparent
        Me.NameMyTextBox2.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.NameMyTextBox2.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.NameMyTextBox2.CaptainWidth = 85.0!
        Me.NameMyTextBox2.ContentForeColor = System.Drawing.Color.Black
        Me.NameMyTextBox2.DisabledForeColor = System.Drawing.Color.FromArgb(CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer))
        Me.NameMyTextBox2.Location = New System.Drawing.Point(263, 4)
        Me.NameMyTextBox2.Multiline = False
        Me.NameMyTextBox2.Name = "NameMyTextBox2"
        Me.NameMyTextBox2.PasswordChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.NameMyTextBox2.ReadOnly = False
        Me.NameMyTextBox2.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.NameMyTextBox2.SelectionStart = 0
        Me.NameMyTextBox2.SelectStart = 0
        Me.NameMyTextBox2.Size = New System.Drawing.Size(279, 20)
        Me.NameMyTextBox2.TabIndex = 1
        Me.NameMyTextBox2.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.NameMyTextBox2.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle
        '
        'JcMyTextBox3
        '
        Me.JcMyTextBox3.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.JcMyTextBox3.Captain = "检验项目简称"
        Me.JcMyTextBox3.CaptainBackColor = System.Drawing.Color.Transparent
        Me.JcMyTextBox3.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.JcMyTextBox3.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.JcMyTextBox3.CaptainWidth = 85.0!
        Me.JcMyTextBox3.ContentForeColor = System.Drawing.Color.Black
        Me.JcMyTextBox3.DisabledForeColor = System.Drawing.Color.FromArgb(CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer))
        Me.JcMyTextBox3.Location = New System.Drawing.Point(548, 4)
        Me.JcMyTextBox3.Multiline = False
        Me.JcMyTextBox3.Name = "JcMyTextBox3"
        Me.JcMyTextBox3.PasswordChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.JcMyTextBox3.ReadOnly = False
        Me.JcMyTextBox3.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.JcMyTextBox3.SelectionStart = 0
        Me.JcMyTextBox3.SelectStart = 0
        Me.JcMyTextBox3.Size = New System.Drawing.Size(279, 20)
        Me.JcMyTextBox3.TabIndex = 2
        Me.JcMyTextBox3.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.JcMyTextBox3.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle
        '
        'ChannelMyTextBox4
        '
        Me.ChannelMyTextBox4.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.ChannelMyTextBox4.Captain = "项目通道码"
        Me.ChannelMyTextBox4.CaptainBackColor = System.Drawing.Color.Transparent
        Me.ChannelMyTextBox4.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.ChannelMyTextBox4.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.ChannelMyTextBox4.CaptainWidth = 85.0!
        Me.ChannelMyTextBox4.ContentForeColor = System.Drawing.Color.Black
        Me.ChannelMyTextBox4.DisabledForeColor = System.Drawing.Color.FromArgb(CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer))
        Me.ChannelMyTextBox4.Location = New System.Drawing.Point(548, 32)
        Me.ChannelMyTextBox4.Multiline = False
        Me.ChannelMyTextBox4.Name = "ChannelMyTextBox4"
        Me.ChannelMyTextBox4.PasswordChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.ChannelMyTextBox4.ReadOnly = False
        Me.ChannelMyTextBox4.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.ChannelMyTextBox4.SelectionStart = 0
        Me.ChannelMyTextBox4.SelectStart = 0
        Me.ChannelMyTextBox4.Size = New System.Drawing.Size(279, 20)
        Me.ChannelMyTextBox4.TabIndex = 5
        Me.ChannelMyTextBox4.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.ChannelMyTextBox4.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle
        '
        'AccuracyMyNumericEdit1
        '
        Me.AccuracyMyNumericEdit1.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.AccuracyMyNumericEdit1.Captain = "项目精度"
        Me.AccuracyMyNumericEdit1.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.AccuracyMyNumericEdit1.CaptainWidth = 60.0!
        Me.AccuracyMyNumericEdit1.Location = New System.Drawing.Point(3, 60)
        Me.AccuracyMyNumericEdit1.MaximumSize = New System.Drawing.Size(100000, 80)
        Me.AccuracyMyNumericEdit1.MinimumSize = New System.Drawing.Size(0, 20)
        Me.AccuracyMyNumericEdit1.Name = "AccuracyMyNumericEdit1"
        Me.AccuracyMyNumericEdit1.NumFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.AccuracyMyNumericEdit1.ReadOnly = False
        Me.AccuracyMyNumericEdit1.Size = New System.Drawing.Size(254, 20)
        Me.AccuracyMyNumericEdit1.TabIndex = 6
        Me.AccuracyMyNumericEdit1.ValueIsDbNull = False
        '
        'AddNumMyNumericEdit2
        '
        Me.AddNumMyNumericEdit2.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.AddNumMyNumericEdit2.Captain = "项目加算值"
        Me.AddNumMyNumericEdit2.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.AddNumMyNumericEdit2.CaptainWidth = 85.0!
        Me.AddNumMyNumericEdit2.Location = New System.Drawing.Point(263, 60)
        Me.AddNumMyNumericEdit2.MaximumSize = New System.Drawing.Size(100000, 80)
        Me.AddNumMyNumericEdit2.MinimumSize = New System.Drawing.Size(0, 20)
        Me.AddNumMyNumericEdit2.Name = "AddNumMyNumericEdit2"
        Me.AddNumMyNumericEdit2.NumFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.AddNumMyNumericEdit2.ReadOnly = False
        Me.AddNumMyNumericEdit2.Size = New System.Drawing.Size(279, 20)
        Me.AddNumMyNumericEdit2.TabIndex = 7
        Me.AddNumMyNumericEdit2.ValueIsDbNull = False
        '
        'ConversionNumMyNumericEdit3
        '
        Me.ConversionNumMyNumericEdit3.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.ConversionNumMyNumericEdit3.Captain = "项目换算比"
        Me.ConversionNumMyNumericEdit3.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.ConversionNumMyNumericEdit3.CaptainWidth = 85.0!
        Me.ConversionNumMyNumericEdit3.Location = New System.Drawing.Point(548, 60)
        Me.ConversionNumMyNumericEdit3.MaximumSize = New System.Drawing.Size(100000, 80)
        Me.ConversionNumMyNumericEdit3.MinimumSize = New System.Drawing.Size(0, 20)
        Me.ConversionNumMyNumericEdit3.Name = "ConversionNumMyNumericEdit3"
        Me.ConversionNumMyNumericEdit3.NumFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.ConversionNumMyNumericEdit3.ReadOnly = False
        Me.ConversionNumMyNumericEdit3.Size = New System.Drawing.Size(279, 20)
        Me.ConversionNumMyNumericEdit3.TabIndex = 8
        Me.ConversionNumMyNumericEdit3.ValueIsDbNull = False
        '
        'MemoTextBox5
        '
        Me.MemoTextBox5.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.MemoTextBox5.Captain = "备    注"
        Me.MemoTextBox5.CaptainBackColor = System.Drawing.Color.Transparent
        Me.MemoTextBox5.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.MemoTextBox5.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.MemoTextBox5.CaptainWidth = 60.0!
        Me.TableLayoutPanel1.SetColumnSpan(Me.MemoTextBox5, 3)
        Me.MemoTextBox5.ContentForeColor = System.Drawing.Color.Black
        Me.MemoTextBox5.DisabledForeColor = System.Drawing.Color.FromArgb(CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer))
        Me.MemoTextBox5.Location = New System.Drawing.Point(3, 87)
        Me.MemoTextBox5.Multiline = True
        Me.MemoTextBox5.Name = "MemoTextBox5"
        Me.MemoTextBox5.PasswordChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.MemoTextBox5.ReadOnly = False
        Me.MemoTextBox5.ScrollBars = System.Windows.Forms.ScrollBars.Vertical
        Me.MemoTextBox5.SelectionStart = 0
        Me.MemoTextBox5.SelectStart = 0
        Me.MemoTextBox5.Size = New System.Drawing.Size(824, 38)
        Me.MemoTextBox5.TabIndex = 9
        Me.MemoTextBox5.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.MemoTextBox5.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Top
        '
        'Button1
        '
        Me.Button1.Anchor = System.Windows.Forms.AnchorStyles.Left
        Me.Button1.ButtonImageSize = CustomControl.MyButton.imageSize.small
        Me.Button1.DialogResult = System.Windows.Forms.DialogResult.None
        Me.Button1.Location = New System.Drawing.Point(318, 1)
        Me.Button1.Name = "Button1"
        Me.Button1.Size = New System.Drawing.Size(67, 27)
        Me.Button1.TabIndex = 0
        Me.Button1.Tag = "保存"
        Me.Button1.Text = "保存"
        '
        'Button2
        '
        Me.Button2.Anchor = System.Windows.Forms.AnchorStyles.Left
        Me.Button2.ButtonImageSize = CustomControl.MyButton.imageSize.small
        Me.Button2.DialogResult = System.Windows.Forms.DialogResult.Cancel
        Me.Button2.Location = New System.Drawing.Point(391, 1)
        Me.Button2.Name = "Button2"
        Me.Button2.Size = New System.Drawing.Size(67, 27)
        Me.Button2.TabIndex = 1
        Me.Button2.Tag = "取消"
        Me.Button2.Text = "取消"
        '
        'C1CommandHolder1
        '
        Me.C1CommandHolder1.Commands.Add(Me.Move1)
        Me.C1CommandHolder1.Commands.Add(Me.Move2)
        Me.C1CommandHolder1.Commands.Add(Me.Move3)
        Me.C1CommandHolder1.Commands.Add(Me.Move4)
        Me.C1CommandHolder1.Commands.Add(Me.Move5)
        Me.C1CommandHolder1.Commands.Add(Me.Comm1)
        Me.C1CommandHolder1.Commands.Add(Me.Comm3)
        Me.C1CommandHolder1.Commands.Add(Me.C1CommandControl1)
        Me.C1CommandHolder1.Commands.Add(Me.C1CommandControl2)
        Me.C1CommandHolder1.Commands.Add(Me.Comm2)
        Me.C1CommandHolder1.Commands.Add(Me.Comm4)
        Me.C1CommandHolder1.Commands.Add(Me.Comm5)
        Me.C1CommandHolder1.Owner = Me
        '
        'Move1
        '
        Me.Move1.Image = CType(resources.GetObject("Move1.Image"), System.Drawing.Image)
        Me.Move1.Name = "Move1"
        Me.Move1.ShortcutText = ""
        Me.Move1.Text = "最前"
        Me.Move1.ToolTipText = "移至最前记录"
        '
        'Move2
        '
        Me.Move2.Image = CType(resources.GetObject("Move2.Image"), System.Drawing.Image)
        Me.Move2.Name = "Move2"
        Me.Move2.ShortcutText = ""
        Me.Move2.Text = "上移"
        Me.Move2.ToolTipText = "上移记录"
        '
        'Move3
        '
        Me.Move3.Image = CType(resources.GetObject("Move3.Image"), System.Drawing.Image)
        Me.Move3.Name = "Move3"
        Me.Move3.ShortcutText = ""
        Me.Move3.Text = "下移"
        Me.Move3.ToolTipText = "下移记录"
        '
        'Move4
        '
        Me.Move4.Image = CType(resources.GetObject("Move4.Image"), System.Drawing.Image)
        Me.Move4.Name = "Move4"
        Me.Move4.ShortcutText = ""
        Me.Move4.Text = "最后"
        Me.Move4.ToolTipText = "移至最后记录"
        '
        'Move5
        '
        Me.Move5.Image = CType(resources.GetObject("Move5.Image"), System.Drawing.Image)
        Me.Move5.Name = "Move5"
        Me.Move5.ShortcutText = ""
        Me.Move5.Text = "新增"
        '
        'Comm1
        '
        Me.Comm1.Image = Global.ZtHis.Lis.My.Resources.Resources.增加
        Me.Comm1.Name = "Comm1"
        Me.Comm1.ShortcutText = ""
        Me.Comm1.Text = "增加"
        Me.Comm1.ToolTipText = "增加记录"
        '
        'Comm3
        '
        Me.Comm3.Image = Global.ZtHis.Lis.My.Resources.Resources.刷新
        Me.Comm3.Name = "Comm3"
        Me.Comm3.ShortcutText = ""
        Me.Comm3.Text = "刷新"
        '
        'C1CommandControl1
        '
        Me.C1CommandControl1.Control = Me.T_Label
        Me.C1CommandControl1.HotFrame = C1.Win.C1Command.HotFrameEnum.None
        Me.C1CommandControl1.Name = "C1CommandControl1"
        Me.C1CommandControl1.ShortcutText = ""
        Me.C1CommandControl1.ShowShortcut = False
        Me.C1CommandControl1.ShowTextAsToolTip = False
        '
        'T_Label
        '
        Me.T_Label.AutoSize = True
        Me.T_Label.BackColor = System.Drawing.Color.Transparent
        Me.T_Label.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.T_Label.Location = New System.Drawing.Point(1, 5)
        Me.T_Label.Name = "T_Label"
        Me.T_Label.Size = New System.Drawing.Size(29, 12)
        Me.T_Label.TabIndex = 0
        Me.T_Label.Tag = Nothing
        Me.T_Label.Text = "记录"
        Me.T_Label.TextAlign = System.Drawing.ContentAlignment.BottomCenter
        Me.T_Label.TextDetached = True
        '
        'C1CommandControl2
        '
        Me.C1CommandControl2.Control = Me.T_Label2
        Me.C1CommandControl2.HotFrame = C1.Win.C1Command.HotFrameEnum.None
        Me.C1CommandControl2.Name = "C1CommandControl2"
        Me.C1CommandControl2.ShortcutText = ""
        Me.C1CommandControl2.ShowShortcut = False
        Me.C1CommandControl2.ShowTextAsToolTip = False
        '
        'T_Label2
        '
        Me.T_Label2.AutoSize = True
        Me.T_Label2.BackColor = System.Drawing.Color.Transparent
        Me.T_Label2.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.T_Label2.Location = New System.Drawing.Point(80, 5)
        Me.T_Label2.Name = "T_Label2"
        Me.T_Label2.Size = New System.Drawing.Size(23, 12)
        Me.T_Label2.TabIndex = 4
        Me.T_Label2.Tag = Nothing
        Me.T_Label2.Text = "(1)"
        Me.T_Label2.TextAlign = System.Drawing.ContentAlignment.BottomCenter
        Me.T_Label2.TextDetached = True
        '
        'Comm2
        '
        Me.Comm2.Image = Global.ZtHis.Lis.My.Resources.Resources.删除
        Me.Comm2.Name = "Comm2"
        Me.Comm2.ShortcutText = ""
        Me.Comm2.Text = "删除"
        Me.Comm2.ToolTipText = "删除记录"
        '
        'Comm4
        '
        Me.Comm4.Image = CType(resources.GetObject("Comm4.Image"), System.Drawing.Image)
        Me.Comm4.Name = "Comm4"
        Me.Comm4.ShortcutText = ""
        Me.Comm4.Text = "上移"
        '
        'Comm5
        '
        Me.Comm5.Image = CType(resources.GetObject("Comm5.Image"), System.Drawing.Image)
        Me.Comm5.Name = "Comm5"
        Me.Comm5.ShortcutText = ""
        Me.Comm5.Text = "下移"
        '
        'Panel1
        '
        Me.Panel1.Controls.Add(Me.T_Label3)
        Me.Panel1.Controls.Add(Me.Button2)
        Me.Panel1.Controls.Add(Me.Button1)
        Me.Panel1.Controls.Add(Me.T_Line3)
        Me.Panel1.Controls.Add(Me.ToolBar1)
        Me.Panel1.Dock = System.Windows.Forms.DockStyle.Top
        Me.Panel1.Location = New System.Drawing.Point(0, 130)
        Me.Panel1.Name = "Panel1"
        Me.Panel1.Size = New System.Drawing.Size(1061, 29)
        Me.Panel1.TabIndex = 1
        '
        'T_Label3
        '
        Me.T_Label3.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.T_Label3.Location = New System.Drawing.Point(233, 8)
        Me.T_Label3.Name = "T_Label3"
        Me.T_Label3.Size = New System.Drawing.Size(35, 15)
        Me.T_Label3.TabIndex = 4
        Me.T_Label3.Tag = Nothing
        Me.T_Label3.Text = "Σ=1"
        Me.T_Label3.TextAlign = System.Drawing.ContentAlignment.BottomCenter
        Me.T_Label3.TextDetached = True
        '
        'T_Line3
        '
        Me.T_Line3.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D
        Me.T_Line3.Dock = System.Windows.Forms.DockStyle.Top
        Me.T_Line3.Location = New System.Drawing.Point(0, 0)
        Me.T_Line3.Name = "T_Line3"
        Me.T_Line3.Size = New System.Drawing.Size(1061, 2)
        Me.T_Line3.TabIndex = 0
        Me.T_Line3.Text = "Label2"
        '
        'ToolBar1
        '
        Me.ToolBar1.AccessibleName = "Tool Bar"
        Me.ToolBar1.BackColor = System.Drawing.Color.Transparent
        Me.ToolBar1.CommandHolder = Nothing
        Me.ToolBar1.CommandLinks.AddRange(New C1.Win.C1Command.C1CommandLink() {Me.Link6, Me.Link1, Me.Link2, Me.Link7, Me.Link3, Me.Link4, Me.Link5})
        Me.ToolBar1.Controls.Add(Me.T_Label)
        Me.ToolBar1.Controls.Add(Me.T_Label2)
        Me.ToolBar1.Location = New System.Drawing.Point(1, 3)
        Me.ToolBar1.MinButtonSize = 22
        Me.ToolBar1.Movable = False
        Me.ToolBar1.Name = "ToolBar1"
        Me.ToolBar1.Size = New System.Drawing.Size(207, 22)
        Me.ToolBar1.Text = "C1ToolBar2"
        Me.ToolBar1.VisualStyle = C1.Win.C1Command.VisualStyle.Custom
        Me.ToolBar1.VisualStyleBase = C1.Win.C1Command.VisualStyle.Classic
        '
        'Link6
        '
        Me.Link6.Command = Me.C1CommandControl1
        '
        'Link1
        '
        Me.Link1.Command = Me.Move1
        Me.Link1.Delimiter = True
        Me.Link1.SortOrder = 1
        '
        'Link2
        '
        Me.Link2.Command = Me.Move2
        Me.Link2.SortOrder = 2
        Me.Link2.Text = "上移"
        Me.Link2.ToolTipText = "上移记录"
        '
        'Link7
        '
        Me.Link7.Command = Me.C1CommandControl2
        Me.Link7.SortOrder = 3
        '
        'Link3
        '
        Me.Link3.Command = Me.Move3
        Me.Link3.SortOrder = 4
        '
        'Link4
        '
        Me.Link4.Command = Me.Move4
        Me.Link4.SortOrder = 5
        '
        'Link5
        '
        Me.Link5.ButtonLook = CType((C1.Win.C1Command.ButtonLookFlags.Text Or C1.Win.C1Command.ButtonLookFlags.Image), C1.Win.C1Command.ButtonLookFlags)
        Me.Link5.Command = Me.Move5
        Me.Link5.Delimiter = True
        Me.Link5.SortOrder = 6
        Me.Link5.Text = "新增"
        Me.Link5.ToolTipText = "新增记录"
        '
        'Panel2
        '
        Me.Panel2.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.Panel2.Controls.Add(Me.T_Line1)
        Me.Panel2.Controls.Add(Me.C1ToolBar1)
        Me.Panel2.Dock = System.Windows.Forms.DockStyle.Top
        Me.Panel2.Location = New System.Drawing.Point(0, 159)
        Me.Panel2.Name = "Panel2"
        Me.Panel2.Size = New System.Drawing.Size(1061, 31)
        Me.Panel2.TabIndex = 2
        '
        'T_Line1
        '
        Me.T_Line1.BackColor = System.Drawing.SystemColors.Control
        Me.T_Line1.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D
        Me.T_Line1.Dock = System.Windows.Forms.DockStyle.Bottom
        Me.T_Line1.Location = New System.Drawing.Point(0, 27)
        Me.T_Line1.Name = "T_Line1"
        Me.T_Line1.Size = New System.Drawing.Size(1059, 2)
        Me.T_Line1.TabIndex = 0
        Me.T_Line1.Text = "Label1"
        '
        'C1ToolBar1
        '
        Me.C1ToolBar1.AccessibleName = "Tool Bar"
        Me.C1ToolBar1.BackColor = System.Drawing.Color.Transparent
        Me.C1ToolBar1.CommandHolder = Nothing
        Me.C1ToolBar1.CommandLinks.AddRange(New C1.Win.C1Command.C1CommandLink() {Me.C1CommandLink1, Me.C1CommandLink2, Me.C1CommandLink3, Me.C1CommandLink4, Me.C1CommandLink5})
        Me.C1ToolBar1.Location = New System.Drawing.Point(3, 3)
        Me.C1ToolBar1.MinButtonSize = 22
        Me.C1ToolBar1.Movable = False
        Me.C1ToolBar1.Name = "C1ToolBar1"
        Me.C1ToolBar1.Size = New System.Drawing.Size(275, 22)
        Me.C1ToolBar1.Text = "C1ToolBar1"
        Me.C1ToolBar1.VisualStyle = C1.Win.C1Command.VisualStyle.Custom
        Me.C1ToolBar1.VisualStyleBase = C1.Win.C1Command.VisualStyle.Classic
        '
        'C1CommandLink1
        '
        Me.C1CommandLink1.ButtonLook = CType((C1.Win.C1Command.ButtonLookFlags.Text Or C1.Win.C1Command.ButtonLookFlags.Image), C1.Win.C1Command.ButtonLookFlags)
        Me.C1CommandLink1.Command = Me.Comm1
        Me.C1CommandLink1.Text = "增加"
        '
        'C1CommandLink2
        '
        Me.C1CommandLink2.ButtonLook = CType((C1.Win.C1Command.ButtonLookFlags.Text Or C1.Win.C1Command.ButtonLookFlags.Image), C1.Win.C1Command.ButtonLookFlags)
        Me.C1CommandLink2.Command = Me.Comm2
        Me.C1CommandLink2.SortOrder = 1
        '
        'C1CommandLink3
        '
        Me.C1CommandLink3.ButtonLook = CType((C1.Win.C1Command.ButtonLookFlags.Text Or C1.Win.C1Command.ButtonLookFlags.Image), C1.Win.C1Command.ButtonLookFlags)
        Me.C1CommandLink3.Command = Me.Comm3
        Me.C1CommandLink3.SortOrder = 2
        Me.C1CommandLink3.Text = "刷新"
        Me.C1CommandLink3.ToolTipText = "更新记录"
        '
        'C1CommandLink4
        '
        Me.C1CommandLink4.ButtonLook = CType((C1.Win.C1Command.ButtonLookFlags.Text Or C1.Win.C1Command.ButtonLookFlags.Image), C1.Win.C1Command.ButtonLookFlags)
        Me.C1CommandLink4.Command = Me.Comm4
        Me.C1CommandLink4.SortOrder = 3
        Me.C1CommandLink4.Text = "上移"
        '
        'C1CommandLink5
        '
        Me.C1CommandLink5.ButtonLook = CType((C1.Win.C1Command.ButtonLookFlags.Text Or C1.Win.C1Command.ButtonLookFlags.Image), C1.Win.C1Command.ButtonLookFlags)
        Me.C1CommandLink5.Command = Me.Comm5
        Me.C1CommandLink5.SortOrder = 4
        '
        'MyGrid1
        '
        Me.MyGrid1.CanCustomCol = False
        Me.MyGrid1.Col = 0
        Me.MyGrid1.ColumnFooters = False
        Me.MyGrid1.DirectionAfterEnter = C1.Win.C1TrueDBGrid.DirectionAfterEnterEnum.MoveRight
        Me.MyGrid1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.MyGrid1.FetchRowStyles = False
        Me.MyGrid1.Location = New System.Drawing.Point(0, 190)
        Me.MyGrid1.Margin = New System.Windows.Forms.Padding(0)
        Me.MyGrid1.MarqueeStyle = C1.Win.C1TrueDBGrid.MarqueeEnum.HighlightRow
        Me.MyGrid1.Name = "MyGrid1"
        Me.MyGrid1.Size = New System.Drawing.Size(1061, 371)
        Me.MyGrid1.TabAction = C1.Win.C1TrueDBGrid.TabActionEnum.ControlNavigation
        Me.MyGrid1.TabIndex = 3
        Me.MyGrid1.Xmlpath = Nothing
        '
        'MyTextBox1
        '
        Me.MyTextBox1.Captain = "地    址"
        Me.MyTextBox1.CaptainBackColor = System.Drawing.Color.Transparent
        Me.MyTextBox1.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.MyTextBox1.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.MyTextBox1.CaptainWidth = 60.0!
        Me.MyTextBox1.ContentForeColor = System.Drawing.Color.Black
        Me.MyTextBox1.DisabledForeColor = System.Drawing.Color.FromArgb(CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer))
        Me.MyTextBox1.Location = New System.Drawing.Point(3, 59)
        Me.MyTextBox1.Multiline = True
        Me.MyTextBox1.Name = "MyTextBox1"
        Me.MyTextBox1.PasswordChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.MyTextBox1.ReadOnly = False
        Me.MyTextBox1.ScrollBars = System.Windows.Forms.ScrollBars.Vertical
        Me.MyTextBox1.SelectionStart = 0
        Me.MyTextBox1.SelectStart = 0
        Me.MyTextBox1.Size = New System.Drawing.Size(547, 22)
        Me.MyTextBox1.TabIndex = 10
        Me.MyTextBox1.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.MyTextBox1.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Top
        '
        'LIS_TestXm2
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 12.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(1061, 561)
        Me.Controls.Add(Me.MyGrid1)
        Me.Controls.Add(Me.Panel2)
        Me.Controls.Add(Me.Panel1)
        Me.Controls.Add(Me.TableLayoutPanel1)
        Me.Name = "LIS_TestXm2"
        Me.Text = "检验项目信息"
        Me.TableLayoutPanel1.ResumeLayout(False)
        CType(Me.C1CommandHolder1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.T_Label, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.T_Label2, System.ComponentModel.ISupportInitialize).EndInit()
        Me.Panel1.ResumeLayout(False)
        CType(Me.T_Label3, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ToolBar1.ResumeLayout(False)
        Me.ToolBar1.PerformLayout()
        Me.Panel2.ResumeLayout(False)
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents TableLayoutPanel1 As System.Windows.Forms.TableLayoutPanel
    Friend WithEvents CodeTextBox As CustomControl.MyTextBox
    Friend WithEvents C1CommandHolder1 As C1.Win.C1Command.C1CommandHolder
    Friend WithEvents Move5 As C1.Win.C1Command.C1Command
    Friend WithEvents Panel1 As System.Windows.Forms.Panel
    Friend WithEvents Button2 As CustomControl.MyButton
    Friend WithEvents Button1 As CustomControl.MyButton
    Friend WithEvents T_Line3 As System.Windows.Forms.Label
    Friend WithEvents ToolBar1 As C1.Win.C1Command.C1ToolBar
    Friend WithEvents Link1 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents Link2 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents Link3 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents Link4 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents Link5 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents Comm1 As C1.Win.C1Command.C1Command
    Friend WithEvents Comm3 As C1.Win.C1Command.C1Command
    Friend WithEvents Panel2 As System.Windows.Forms.Panel
    Friend WithEvents T_Line1 As System.Windows.Forms.Label
    Friend WithEvents C1ToolBar1 As C1.Win.C1Command.C1ToolBar
    Friend WithEvents C1CommandLink1 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents C1CommandLink3 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents MyGrid1 As CustomControl.MyGrid
    Friend WithEvents MyTextBox1 As CustomControl.MyTextBox
    Friend WithEvents C1CommandControl1 As C1.Win.C1Command.C1CommandControl
    Friend WithEvents T_Label As C1.Win.C1Input.C1Label
    Friend WithEvents Link6 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents C1CommandControl2 As C1.Win.C1Command.C1CommandControl
    Friend WithEvents T_Label2 As C1.Win.C1Input.C1Label
    Friend WithEvents Link7 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents T_Label3 As C1.Win.C1Input.C1Label
    Friend WithEvents Comm2 As C1.Win.C1Command.C1Command
    Friend WithEvents C1CommandLink2 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents DevMyDtComobo1 As CustomControl.MyDtComobo
    Friend WithEvents TestSampleMyDtComobo2 As CustomControl.MyDtComobo
    Friend WithEvents NameMyTextBox2 As CustomControl.MyTextBox
    Friend WithEvents JcMyTextBox3 As CustomControl.MyTextBox
    Friend WithEvents ChannelMyTextBox4 As CustomControl.MyTextBox
    Friend WithEvents AccuracyMyNumericEdit1 As CustomControl.MyNumericEdit
    Friend WithEvents AddNumMyNumericEdit2 As CustomControl.MyNumericEdit
    Friend WithEvents ConversionNumMyNumericEdit3 As CustomControl.MyNumericEdit
    Friend WithEvents MemoTextBox5 As CustomControl.MyTextBox
    Friend WithEvents Move1 As C1.Win.C1Command.C1Command
    Friend WithEvents Move2 As C1.Win.C1Command.C1Command
    Friend WithEvents Move3 As C1.Win.C1Command.C1Command
    Friend WithEvents Move4 As C1.Win.C1Command.C1Command
    Friend WithEvents Comm4 As C1.Win.C1Command.C1Command
    Friend WithEvents C1CommandLink4 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents Comm5 As C1.Win.C1Command.C1Command
    Friend WithEvents C1CommandLink5 As C1.Win.C1Command.C1CommandLink




End Class
