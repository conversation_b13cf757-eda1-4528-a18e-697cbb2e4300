﻿Imports System.IO.StreamWriter
Imports System.Data.SqlClient
Imports System.Xml
Imports System.IO

Public Class DataBackUp
    '----------------------------------------------------------------------------   
    '功能：在vb.net中备份sql   server数据库   
    '----------------------------------------------------------------------------   
    '引用Microsoft SQLDMO Object Library       

    '声明 
    Dim configFileName As String = Application.StartupPath & "\His2010.exe.config"
    Dim doc As New System.Xml.XmlDocument()
    Dim My_Dataset As New DataSet
    'Public WithEvents bkps As SQLDMO.Backup
    'Public WithEvents Orestore As SQLDMO.Restore
    Declare Unicode Function GetShortPathName Lib "kernel32.dll" Alias "GetShortPathNameW" (ByVal longPath As String, ByVal ShortPath As String, ByVal bufferSize As Integer) As Integer
    Dim PathFile As String
    '显示进度       
    'Private Sub bkps_PercentComplete(ByVal Message As String, ByVal Percent As Integer) Handles bkps.PercentComplete
    '    ProgressBar1.Value = ProgressBar1.Maximum * (Percent / 100)
    'End Sub

    Function ShortPathName(ByVal Path As String) As String
        Dim RetVal As Integer
        Dim ShortFileName As String = ""
        Dim cLine As Integer
        Dim PathFolder As String

        cLine = Path.LastIndexOf("\")
        PathFolder = Mid(Path, 1, cLine + 1)
        PathFile = Mid(Path, cLine + 2)
        ShortFileName = Space(Path.Length)
        RetVal = GetShortPathName(PathFolder, ShortFileName, Len(Path))
        Return ShortFileName
    End Function

    Private Sub Button1_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Button1.Click
        MsgBox("请将数据库文件备份到除C盘以外，其他盘的特定文件夹内！", MsgBoxStyle.Information, "系统消息")
        With Me.SaveFileDialog1
            .Filter = "数据库备份文件(.Bak)|*.Bak"
            .FileName = HisVar.HisVar.WsyName & Me.ComboBox1.Text & Format(Now, "yyMMdd") & ".Bak"
            If .ShowDialog = DialogResult.Cancel Then
                Exit Sub
            End If
            .AddExtension = True
        End With

        Dim conn As New SqlConnection("Server=" & Me.TextBox1.Text & ";Database=master;User ID=" & Me.TextBox4.Text & ";Password=" & Me.TextBox3.Text & ";")
        Dim cmdBK As New SqlCommand()
        cmdBK.CommandType = CommandType.Text
        cmdBK.Connection = conn
        cmdBK.CommandText = "backup database  " & Me.ComboBox1.Text & " to disk='" & Me.SaveFileDialog1.FileName & "' with init"
        Try
            conn.Open()
            Me.Cursor = Cursors.WaitCursor
            cmdBK.ExecuteNonQuery()
            Me.Cursor = Cursors.Default()
            MsgBox("数据库备份完成", MsgBoxStyle.Information, "系统消息")
        Catch ex As Exception
            MessageBox.Show(ex.Message)
        Finally
            conn.Close()
            conn.Dispose()
        End Try

    End Sub

    Private Sub Button2_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Button2.Click
        Me.Close()
    End Sub
    Private Function getSystemParam(ByVal name As String) As String
        Dim configString As String = "configuration/userSettings/His2010.My.MySettings/setting[@name='" & name & "']/value"
        '请根据实际情况修改 
        Dim configNode As System.Xml.XmlNode = doc.SelectSingleNode(configString)
        If configNode IsNot Nothing Then
            Return configNode.InnerText
        End If
        Return ""
    End Function
    Private Sub DataBackUp_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        Data_Init()
        ' Dim path As String
        '  path = Application.StartupPath + "\app.config"
        doc.Load(configFileName)
        TextBox1.Text = My.Settings.DB_Ip 'getSystemParam("DB_Ip")
        Me.ComboBox1.Text = My.Settings.DB_Name ' getSystemParam("DB_Name")
        TextBox3.Text = My.Settings.DB_Pwd ' getSystemParam("DB_Pwd")
        TextBox4.Text = My.Settings.DB_Id ' getSystemParam("DB_Id")

    End Sub
    Private Sub Data_Init()
        Me.ComboBox1.Items.Clear()
        If My_Dataset.Tables("数据库列表") IsNot Nothing Then My_Dataset.Tables("数据库列表").Clear()
        Dim My_Adapter As SqlDataAdapter = New SqlDataAdapter("SELECT name FROM  master..sysdatabases  WHERE name like '%His%'", My_Cn)
        My_Adapter.Fill(My_Dataset, "数据库列表")
        Dim Row As DataRow

        For Each Row In My_Dataset.Tables("数据库列表").Rows
            Me.ComboBox1.Items.Add(Row.Item("name"))
        Next
    End Sub

    Private Sub Button3_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Button3.Click

        If MessageBox.Show("此操作会将数据库还原，同时关闭所有数据库连接，丢失原有数据，且该过程不可逆，请确认是否还原？", "提示", MessageBoxButtons.OKCancel, MessageBoxIcon.Information) = DialogResult.Cancel Then
            Exit Sub
        End If
        With Me.OpenFileDialog1
            .Filter = "数据库备份文件(.Bak)|*.Bak"
            .FileName = ""
            If .ShowDialog = DialogResult.Cancel Then
                Exit Sub
            End If
            .AddExtension = True
        End With

        P_Conn(False)



        Dim conn As New SqlConnection("Server=" & Me.TextBox1.Text & ";Database=master;User ID=" & Me.TextBox4.Text & ";Password=" & Me.TextBox3.Text & ";Trusted_Connection=False")
        conn.Open()
        'KILL DataBase Process
        Dim cmd As New SqlCommand("SELECT spid,filename FROM sysprocesses ,sysdatabases WHERE sysprocesses.dbid=sysdatabases.dbid AND sysdatabases.Name='" & Me.ComboBox1.Text & "'", conn)
        Dim V_Mdf_Path As String = ""
        Dim dr As SqlDataReader
        dr = cmd.ExecuteReader()
        Dim list As New ArrayList()
        While dr.Read()
            list.Add(dr.GetInt16(0))
            V_Mdf_Path = Mid(dr.Item("filename"), 1, dr.Item("filename").ToString.LastIndexOf("\") + 1)
        End While
        dr.Close()
        For i As Integer = 0 To list.Count - 1
            cmd = New SqlCommand("KILL " & list.Item(i), conn)

            cmd.ExecuteNonQuery()
        Next
        Dim cmdRT As New SqlCommand()
        cmdRT.CommandType = CommandType.Text
        cmdRT.Connection = conn
        cmdRT.CommandText = "restore database [" & ComboBox1.Text & "] from disk='" & Me.OpenFileDialog1.FileName & "' WITH  FILE = 1,  MOVE N'His_Data' TO N'" & V_Mdf_Path & ComboBox1.Text & ".MDF',  MOVE N'His_Log' TO N'" & V_Mdf_Path & ComboBox1.Text & "_1.LDF',  NOUNLOAD,  REPLACE,  STATS = 10"

        Try
            Me.Cursor = Cursors.WaitCursor
            cmdRT.ExecuteNonQuery()
            Me.Cursor = Cursors.Default()
            MessageBox.Show("数据库还原成功！")

            Application.Restart()
        Catch ex As Exception
            MessageBox.Show(ex.Message)
        Finally
            conn.Close()
        End Try


    End Sub

    Private Sub Button4_Click(sender As System.Object, e As System.EventArgs) Handles Button4.Click
        If ZTHisPublicFunction.DBUpdate.DBNeedUpdate() = True Then
            ZTHisPublicFunction.DBUpdate.DbUpdate(My.Settings.DB_Pwd, My.Settings.DB_Ip, My.Settings.DB_Name)
            MsgBox("数据库结构更新完成！", MsgBoxStyle.Information, "提示")
        Else
            MsgBox("已经是最新库结构！", MsgBoxStyle.Information, "提示")
        End If
    End Sub
    Public Shared Function ExeCommand(ByVal commandText As String, ByVal Sql As String) As String
        Dim p As New Process()
        p.StartInfo.FileName = "cmd.exe"
        p.StartInfo.UseShellExecute = False
        p.StartInfo.RedirectStandardInput = True
        p.StartInfo.RedirectStandardOutput = True
        p.StartInfo.RedirectStandardError = True
        p.StartInfo.CreateNoWindow = True
        Dim strOutput As String = Nothing
        Try
            p.Start()
            p.StandardInput.WriteLine(commandText)
            p.StandardInput.WriteLine("exit")
            strOutput = p.StandardOutput.ReadToEnd()
            p.WaitForExit()
            p.Close()
        Catch e As Exception
            strOutput = e.Message
        End Try
        Return strOutput
    End Function
    Public Sub Getdata()
        'If CheckDb(171227, "IF EXISTS (SELECT TABLE_NAME FROM INFORMATION_SCHEMA.tables WHERE TABLE_NAME = N'DRYB_ZYJS')print 'true' Else Print 'False'") = False Then
        '    Exit Sub
        'End If
        'If CheckDb(171228, "IF EXISTS (SELECT  top 1 * FROM syscolumns WHERE Name='IsYearEnd')print 'True'else Print 'False'") = False Then
        '    Exit Sub
        'End If
        'If CheckDb(180201, "IF EXISTS (SELECT  top 1 * FROM syscolumns WHERE Name='IsNewVersion')print 'True'else Print 'False'") = False Then
        '    Exit Sub
        'End If
        'If CheckDb(180202, "IF EXISTS (SELECT  top 1 * FROM syscolumns WHERE Name='HisCount')print 'True'else Print 'False'") = False Then
        '    Exit Sub
        'End If
        'If CheckDb(180203, "IF EXISTS (SELECT  top 1 * FROM syscolumns WHERE Name='IsEnable')print 'True'else Print 'False'") = False Then
        '    Exit Sub
        'End If
        'If CheckDb(180204, "IF EXISTS (SELECT  top 1 * FROM ZD_QXMODULE WHERE MODULE_CODE = '1812')print 'True' else Print 'False'") = False Then
        '    Exit Sub
        'End If
        'If CheckDb(180419, "IF EXISTS (SELECT  top 1 * FROM ZD_QXMODULE WHERE MODULE_CODE = '0808')print 'True' else Print 'False'") = False Then
        '    Exit Sub
        'End If
        'If CheckDb(180824, "IF EXISTS (SELECT  top 1 * FROM Zd_YyPara WHERE Para_Code = '29')print 'True' else Print 'False'") = False Then
        '    Exit Sub
        'End If
        'If CheckDb(180825, "IF EXISTS (SELECT  top 1 * FROM Zd_YyPara WHERE Para_Code = '30')print 'True' else Print 'False'") = False Then
        '    Exit Sub
        'End If
        'If CheckDb(180826, "IF EXISTS (SELECT  top 1 * FROM Zd_YyPara WHERE Para_Code = '31')print 'True' else Print 'False'") = False Then
        '    Exit Sub
        'End If
        '添加表
        'If CheckDb(171227, "IF EXISTS (SELECT TABLE_NAME FROM INFORMATION_SCHEMA.tables WHERE TABLE_NAME = N'DRYB_ZYJS')print 'true' Else Print 'False'") = False Then
        '    Exit Sub
        'End If
        '添加参数
        'If CheckDb(180204, "IF EXISTS (SELECT  top 1 * FROM ZD_QXMODULE WHERE MODULE_CODE = '1812')print 'True' else Print 'False'") = False Then
        '    Exit Sub
        'End If
        '添加列
        'ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(190712, "IF EXISTS (SELECT top 1 * FROM syscolumns WHERE id=OBJECT_ID('NMG_YB_SettlementOut') AND Name='Jzscbjqfbzlj' )print 'True'else Print 'False'")

        'ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(190718, "IF EXISTS (SELECT top 1 * FROM syscolumns WHERE id=OBJECT_ID('NMG_YB_SettlementOut') AND Name='Zmzyydejjzc' )print 'True'else Print 'False'")
        ''更新东软 编码长度   查询NMG_YB_ZLXM_DZ表中字医保编码长度是否等于100
        'ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(200118, "IF EXISTS (SELECT a.name,b.name,c.DATA_TYPE,b.max_length FROM sys.tables a join sys.columns b on b.object_id = a.object_id JOIN INFORMATION_SCHEMA.COLUMNS c on b.name=c.COLUMN_NAME and a.name=c.TABLE_NAME WHERE a.name='NMG_YB_ZLXM_DZ' AND b.name='Yb_Zlxm_Code' AND b.max_length='100')print 'True'else Print 'False'")

        'ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(200605, "IF EXISTS (SELECT  top 1 * FROM Zd_QxModule WHERE Module_Code = '2005')print 'True' else Print 'False'")
        ''更新折扣率和原价字段 Mz_Xm/Mz_Xm_Sum/Mz_Yp/Mz_Yp_Sum/Mz/Mz_Sum
        'ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(200603, "IF EXISTS (SELECT top 1 * FROM syscolumns WHERE id=OBJECT_ID('Mz_Sum') AND Name='Mz_Original_Money' )print 'True'else Print 'False'")


        ''更新中草药增加付数显示
        'ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(200617, "IF EXISTS (SELECT top 1 * FROM syscolumns WHERE id=OBJECT_ID('Mz_Yp') AND Name='Mz_DfSl' )print 'True'else Print 'False'")


        ''增病案首页等相关表
        'ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(201207, "IF EXISTS (SELECT TABLE_NAME FROM INFORMATION_SCHEMA.tables WHERE TABLE_NAME = N'BL_FIRSTPAGEXY')print 'true' Else Print 'False'")


        ''增Dict_Operation和Dict_Operation_Scale
        'ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(201208, "IF EXISTS (SELECT TABLE_NAME FROM INFORMATION_SCHEMA.tables WHERE TABLE_NAME = N'Dict_Operation')print 'true' Else Print 'False'")


        ''增医保审批结果查询表
        'ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(201224, "IF EXISTS (SELECT TABLE_NAME FROM INFORMATION_SCHEMA.tables WHERE TABLE_NAME = N'NMG_YB_Spxx')print 'true' Else Print 'False'")


        ''医保患者结算单样式
        'ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(210103, "IF EXISTS (SELECT  top 1 * FROM Zd_YyPara WHERE Para_Code = '32')print 'True' else Print 'False'")


        '检验科室限制
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(210112, "IF EXISTS (SELECT  top 1 * FROM Zd_YyPara WHERE Para_Code = '33')print 'True' else Print 'False'")


        '删除以前的新农合权限
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(210422, "IF not EXISTS (SELECT  top 1 * FROM ZD_QXMODULE WHERE MODULE_CODE = '9801' And Dl_Name='农合接口')print 'True' else Print 'False'")

        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(210425, "IF EXISTS (select st.name as 'TypeName',sc.name,sc.length from syscolumns sc,systypes st where sc.xtype=st.xtype and sc.id in(select id from sysobjects where xtype='U' and name='NMG_YB_Spxx')AND st.name = 'varchar' and sc.name = 'YYSFXMNM' and sc.length = '500')print 'True' else Print 'False'")

        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(210426, "IF EXISTS (SELECT  top 1 * FROM Zd_YyPara WHERE Para_Code = '36')print 'True' else Print 'False'")
        '配伍禁忌
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(210428, "IF EXISTS (SELECT TABLE_NAME FROM INFORMATION_SCHEMA.tables WHERE TABLE_NAME = N'Drug_Incompatibility1')print 'true' Else Print 'False'")
        'Jx_Code扩展成3位
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(210429, "IF EXISTS (SELECT a.name,b.name,c.DATA_TYPE,b.max_length FROM sys.tables a join sys.columns b on b.object_id = a.object_id JOIN INFORMATION_SCHEMA.COLUMNS c on b.name=c.COLUMN_NAME and a.name=c.TABLE_NAME WHERE a.name='Zd_Ml_Ypjx' AND b.name='Jx_Code' AND b.max_length='3')print 'True'else Print 'False'")
        'NMG_YB_SettlementOut增加Lb_Name列
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(210430, "IF EXISTS (SELECT top 1 * FROM syscolumns WHERE id=OBJECT_ID('NMG_YB_SettlementOut') AND Name='Lb_Name' )print 'True' else Print 'False'")
        '查询NMG_YB_Detail表中项目名称长度是否等于100
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(210501, "IF EXISTS (SELECT a.name,b.name,c.DATA_TYPE,b.max_length FROM sys.tables a join sys.columns b on b.object_id = a.object_id JOIN INFORMATION_SCHEMA.COLUMNS c on b.name=c.COLUMN_NAME and a.name=c.TABLE_NAME WHERE a.name='NMG_YB_Detail' AND b.name='Yy_Xm_Name' AND b.max_length='100')print 'True'else Print 'False'")
        '查询NMG_YB_ReadCardInfo表中Nation字段长度是否等于30
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(210507, "IF EXISTS (SELECT a.name,b.name,c.DATA_TYPE,b.max_length FROM sys.tables a join sys.columns b on b.object_id = a.object_id JOIN INFORMATION_SCHEMA.COLUMNS c on b.name=c.COLUMN_NAME and a.name=c.TABLE_NAME WHERE a.name='NMG_YB_ReadCardInfo' AND b.name='Nation' AND b.max_length='30')print 'True'else Print 'False'")
        'NMG_YB_Detail增加IsZF列
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(210506, "IF EXISTS (SELECT top 1 * FROM syscolumns WHERE id=OBJECT_ID('NMG_YB_Detail') AND Name='IsZF' )print 'True' else Print 'False'")
        'NMG_YB_SettlementOut增加Syyyf列
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(210513, "IF EXISTS (SELECT top 1 * FROM syscolumns WHERE id=OBJECT_ID('NMG_YB_SettlementOut') AND Name='Syyyf' )print 'True' else Print 'False'")
        '增加民族字典
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(210514, "IF EXISTS (SELECT TABLE_NAME FROM INFORMATION_SCHEMA.tables WHERE TABLE_NAME = N'Dict_Nation')print 'true' Else Print 'False'")
        'Mz_Gh，Mz_Gh_Sum 增加Ry_MinZu列
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(210515, "IF EXISTS (SELECT top 1 * FROM syscolumns WHERE id=OBJECT_ID('Mz_Gh') AND Name='Ry_MinZu' )print 'True'else Print 'False'")
        'Zd_YyPara增加挂号发票样式
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(210516, "IF EXISTS (SELECT  top 1 * FROM Zd_YyPara WHERE Para_Code = '31')print 'True' else Print 'False'")
        '删除以前的医改办权限
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(210603, "IF not EXISTS (SELECT  top 1 * FROM ZD_QXMODULE WHERE MODULE_CODE = '9701' And Dl_Name='医改办接口')print 'True' else Print 'False'")
        'NMG_YB_SettlementOut增加列
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(210623, "IF EXISTS (SELECT top 1 * FROM syscolumns WHERE id=OBJECT_ID('NMG_YB_SettlementOut') AND Name='Abzffzbz' )print 'True'else Print 'False'")
        '增加新医保权限
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(211008, "IF EXISTS (SELECT  top 1 * FROM Zd_QxMenu1 WHERE Menu_Code='50')print 'True'else Print 'False'")
        '增加新医保表结构
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(211009, "IF EXISTS (SELECT TABLE_NAME FROM INFORMATION_SCHEMA.tables WHERE TABLE_NAME = N'Country_YB_Action')print 'true' Else Print 'False'")
        '增加新医保权限：门诊报销查询，住院报销查询
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(211010, "IF EXISTS (SELECT  top 1 * FROM Zd_QxModule WHERE Module_Code='5023')print 'True'else Print 'False'")
        '增加 Bl_TextCf 表结构
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(211026, "IF EXISTS (SELECT TABLE_NAME FROM INFORMATION_SCHEMA.tables WHERE TABLE_NAME = N'Bl_TextCf')print 'true' Else Print 'False'")
        '增加 Bl_Rytz 表结构
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(211027, "IF EXISTS (SELECT TABLE_NAME FROM INFORMATION_SCHEMA.tables WHERE TABLE_NAME = N'Bl_Rytz')print 'true' Else Print 'False'")
        '护士工作科室限制，原来的32改成35
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(211028, "IF EXISTS (SELECT  top 1 * FROM Zd_YyPara WHERE Para_Code = '32' and Para_Name='护士工作科室限制')print 'True' else Print 'False'")
        '增加医执人员信息对照表结构
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(211109, "IF EXISTS (SELECT TABLE_NAME FROM INFORMATION_SCHEMA.tables WHERE TABLE_NAME = N'Country_YBMLDZ_YZRY')print 'true' Else Print 'False'")
        'BL_FIRSTOPER的OPERATION_LEVEL_CD改成varchar(2)
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(211111, "IF EXISTS (select st.name as 'TypeName',sc.name,sc.length from syscolumns sc,systypes st where sc.xtype=st.xtype and sc.id in(select id from sysobjects where xtype='U' and name='BL_FIRSTOPER')AND st.name = 'varchar' and sc.name = 'OPERATION_LEVEL_CD' and sc.length = '2')print 'True' else Print 'False'")
        'zd_yyjsr中，将jsr_name字段扩展成20
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(211112, "IF EXISTS (SELECT a.name,b.name,c.DATA_TYPE,b.max_length FROM sys.tables a join sys.columns b on b.object_id = a.object_id JOIN INFORMATION_SCHEMA.COLUMNS c on b.name=c.COLUMN_NAME and a.name=c.TABLE_NAME WHERE a.name='Zd_YyJsr' AND b.name='Jsr_Name' AND b.max_length='20')print 'True'else Print 'False'")
        'Country_YB_CONFIG的fixmedins_name改成varchar(30)改成varchar(300)
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(211115, "IF EXISTS (select st.name as 'TypeName',sc.name,sc.length from syscolumns sc,systypes st where sc.xtype=st.xtype and sc.id in(select id from sysobjects where xtype='U' and name='Country_YB_CONFIG')AND st.name = 'varchar' and sc.name = 'fixmedins_name' and sc.length = '300')print 'True' else Print 'False'")
        '修改医执人员信息对照表主键改成His_Code
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(211116, "IF EXISTS (SELECT  TABLE_NAME,COLUMN_NAME  FROM  INFORMATION_SCHEMA.KEY_COLUMN_USAGE  WHERE  TABLE_NAME= 'Country_YBMLDZ_YZRY' AND COLUMN_NAME='His_Code')print 'True' else Print 'False'")
        '增加医执人员信息表结构
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(211117, "IF EXISTS (SELECT TABLE_NAME FROM INFORMATION_SCHEMA.tables WHERE TABLE_NAME = N'Country_YBML_YZRY')print 'true' Else Print 'False'")
        'Country_YBMLDZ_YZRY的prac_psn_code改成允许为null
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(211118, "IF EXISTS (select st.name as 'TypeName',sc.name,sc.length from syscolumns sc,systypes st where sc.xtype=st.xtype and sc.id in(select id from sysobjects where xtype='U' and name='Country_YBMLDZ_YZRY')AND st.name = 'varchar' and sc.name = 'prac_psn_code' and sc.length = '20' AND sc.isnullable=1)print 'true' Else Print 'False'")
        '增加行政区划字典表
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(211120, "IF EXISTS (SELECT TABLE_NAME FROM INFORMATION_SCHEMA.tables WHERE TABLE_NAME = N'Dict_Xzqh')print 'true' Else Print 'False'")
        '增加门诊报销明细和住院报销明细索引
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(211126, "IF EXISTS (select * from sysindexes where name='IX_Country_YB_ZYMX_Cf_Id')print 'true' Else Print 'False'")
        'Country_YBMLDZ_YZRY和Country_YBML_YZRY的prac_psn_cert字段改为varchar(100)
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(211203, "IF EXISTS (select st.name as 'TypeName',sc.name,sc.length from syscolumns sc,systypes st where sc.xtype=st.xtype and sc.id in(select id from sysobjects where xtype='U' and name='Country_YBML_YZRY')AND st.name = 'varchar' and sc.name = 'prac_psn_cert' and sc.length = '100')print 'True' else Print 'False'")
        '之前的行政区划脚本是乱码
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(211216, "IF EXISTS (SELECT * FROM Dict_Xzqh WHERE Xzqh_Name='北京市')print 'True' else Print 'False'")
        'Country_YBML_XMML的诊疗项目内涵改成varchar(1000)改成varchar(8000)
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(211217, "IF EXISTS (select st.name as 'TypeName',sc.name,sc.length from syscolumns sc,systypes st where sc.xtype=st.xtype and sc.id in(select id from sysobjects where xtype='U' and name='Country_YBML_XMML')AND st.name = 'varchar' and sc.name = '诊疗项目内涵' and sc.length = '8000')print 'True' else Print 'False'")
        'Zd_YyPara增加门诊日结是否删除未完成处方
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(220225, "IF EXISTS (SELECT  top 1 * FROM Zd_YyPara WHERE Para_Code = '37')print 'True' else Print 'False'")
        '插入Zd_Ml_Yp1XlRule
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(220305, "IF EXISTS (SELECT  top 1 * FROM Zd_Ml_Yp1XlRule)print 'True' else Print 'False'")
        '增加电子票据表结构
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(220317, "IF EXISTS (SELECT TABLE_NAME FROM INFORMATION_SCHEMA.tables WHERE TABLE_NAME = N'E_Invoice_APPENDIX')print 'true' Else Print 'False'")
        'Yk_Yf1增加Ck_Ly列
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(220320, "IF EXISTS (SELECT top 1 * FROM syscolumns WHERE id=OBJECT_ID('Yk_Yf1') AND Name='Ck_Ly' )print 'True' else Print 'False'")
        '增加电子票据查询权限
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(220324, "IF EXISTS (SELECT top 1 * FROM Zd_QxModule Where Module_Code ='5105' )print 'True' else Print 'False'")
        'Country_YB_BASEINFO_Idetinfo endtime改成DATETIME
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(220325, "IF EXISTS (select st.name as 'TypeName',sc.name,sc.length from syscolumns sc,systypes st where sc.xtype=st.xtype and sc.id in(select id from sysobjects where xtype='U' and name='Country_YB_BASEINFO_Idetinfo')AND st.name = 'datetime' and sc.name = 'endtime' )print 'True' else Print 'False'")
        'BL_FIRSTOPER增加手术和麻醉医生编码
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(220422, "IF EXISTS (SELECT top 1 * FROM syscolumns WHERE id=OBJECT_ID('BL_FIRSTOPER') AND Name='SURGEON_CODE' )print 'True'else Print 'False'")
        'Country_YB_ZYJZ的 geso_val DECIMAL(2,2)改成DECIMAL(2,0)
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(220425, "IF EXISTS (select st.name as 'TypeName',sc.name,sc.length from syscolumns sc,systypes st where sc.xtype=st.xtype and sc.id in(select id from sysobjects where xtype='U' and name='Country_YB_ZYJZ')AND st.name = 'decimal' and sc.name = 'geso_val' AND sc.xprec='2' AND sc.scale='0')print 'True' else Print 'False'")
        'V_Yp,V_YpKc增加Xl_Code,Xl_Name 二级和乡镇语句不一样
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(220426, "IF EXISTS (SELECT top 1 * FROM syscolumns WHERE id=OBJECT_ID('V_Yp') AND Name='Xl_Code' )print 'True' else Print 'False'")
        '增加BL_FIRSTBLOOD
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(220428, "IF EXISTS (SELECT TABLE_NAME FROM INFORMATION_SCHEMA.tables WHERE TABLE_NAME = N'BL_FIRSTBLOOD')print 'true' Else Print 'False'")
        'Country_YB_ZYZD 增加 adm_cond_type 列
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(220513, "IF EXISTS (SELECT top 1 * FROM syscolumns WHERE id=OBJECT_ID('Country_YB_ZYZD') AND Name='adm_cond_type' )print 'True'else Print 'False'")
        '增加Dict_Blood
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(220514, "IF EXISTS (SELECT TABLE_NAME FROM INFORMATION_SCHEMA.tables WHERE TABLE_NAME = N'Dict_Blood')print 'true' Else Print 'False'")
        'Country_YB_JsListUpload 增加 prfs 列
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(220517, "IF EXISTS (SELECT top 1 * FROM syscolumns WHERE id=OBJECT_ID('Country_YB_JsListUpload') AND Name='prfs' )print 'True'else Print 'False'")
        'Country_YB_JsListUpload 增加 patn_rlts ipt_med_type 列
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(220520, "IF EXISTS (SELECT top 1 * FROM syscolumns WHERE id=OBJECT_ID('Country_YB_JsListUpload') AND Name='ipt_med_type' )print 'True'else Print 'False'")
        'Country_YB_ZYJS Country_YB_MZJS 增加 Zf_setl_id Zf_medins_setl_id 列
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(220622, "IF EXISTS (SELECT top 1 * FROM syscolumns WHERE id=OBJECT_ID('Country_YB_ZYJS') AND Name='Zf_setl_id' )print 'True'else Print 'False'")
        'Country_YB_SpPc 增加 Up_DTime列
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(220627, "IF EXISTS (SELECT top 1 * FROM syscolumns WHERE id=OBJECT_ID('Country_YB_SpPc') AND Name='Up_DTime' )print 'True'else Print 'False'")
        '合并医保门诊对账和医保住院对账
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(220714, "IF EXISTS (SELECT  top 1 * FROM Zd_QxModule WHERE Module_Code='5005' AND Xl_Name='医保结算对账')print 'True'else Print 'False'")
        'Country_YBMLDZ_KS 增加 YbKs_Code 列
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(220727, "IF EXISTS (SELECT top 1 * FROM syscolumns WHERE id=OBJECT_ID('Country_YBMLDZ_KS') AND Name='YbKs_Code' )print 'True'else Print 'False'")
        '增加Dict_Occupation
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(220728, "IF EXISTS (SELECT TABLE_NAME FROM INFORMATION_SCHEMA.tables WHERE TABLE_NAME = N'Dict_Occupation')print 'true' Else Print 'False'")
        'BL_FIRSTPAGEXY BL_FIRSTPAGEZY 的NATIONALITY_CODE改成char(3)
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(220801, "IF EXISTS (select st.name as 'TypeName',sc.name,sc.length from syscolumns sc,systypes st where sc.xtype=st.xtype and sc.id in(select id from sysobjects where xtype='U' and name='BL_FIRSTPAGEXY')AND st.name = 'char' and sc.name = 'NATIONALITY_CODE' and sc.length = '3')print 'True' else Print 'False'")
        '更新BL_FIRSTPAGEXY BL_FIRSTPAGEZY 的NATIONALITY_CODE 变成3位
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(220802, "IF EXISTS (select top 1 * from [BL_FIRSTPAGEXY] where NATIONALITY_CODE is not null and len(ltrim(rtrim(NATIONALITY_CODE)))<3) or EXISTS  (select top 1 * from [BL_FIRSTPAGEZY] where NATIONALITY_CODE is not null and len(ltrim(rtrim(NATIONALITY_CODE)))<3)print 'False' else Print 'True'")
        '增加备案附件上传表Country_YB_BaFjUp
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(220807, "IF EXISTS (SELECT TABLE_NAME FROM INFORMATION_SCHEMA.tables WHERE TABLE_NAME = N'Country_YB_BaFjUp')print 'true' Else Print 'False'")
        'E_Invoice_Mz的Mz_Code改成varchar(20)以适应MzGh_Code
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(220814, "IF EXISTS (select st.name as 'TypeName',sc.name,sc.length from syscolumns sc,systypes st where sc.xtype=st.xtype and sc.id in(select id from sysobjects where xtype='U' and name='E_Invoice_Mz')AND st.name = 'varchar' and sc.name = 'Mz_Code' and sc.length = '20')print 'True' else Print 'False'")
        'Pat_Master_Index的Pat_Address改成varchar(200)
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(220822, "IF EXISTS (select st.name as 'TypeName',sc.name,sc.length from syscolumns sc,systypes st where sc.xtype=st.xtype and sc.id in(select id from sysobjects where xtype='U' and name='Pat_Master_Index')AND st.name = 'varchar' and sc.name = 'Pat_Address' and sc.length = '200')print 'True' else Print 'False'")
        '增加手术字典权限 通辽乡镇特有
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(220901, "IF EXISTS (SELECT top 1 * FROM Zd_QxModule Where Module_Code ='0509' )print 'True' else Print 'False'")
        '修改BL_FIRSTOUTDIAG的IN_CONDITION_CD
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(220909, "IF EXISTS (SELECT 1 FROM BL_FIRSTOUTDIAG WHERE IN_CONDITION_CD<>CASE DIAG_RESULT_CODE WHEN '有'THEN '1'WHEN '临床未确定'THEN '2'WHEN '情况不明'THEN '3'WHEN '无'THEN '4' END )print 'False' else Print 'True'")
        'Zd_YyPara增加住院处方样式
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(221026, "IF EXISTS (SELECT  top 1 * FROM Zd_YyPara WHERE Para_Code = '41')print 'True' else Print 'False'")
        'Country_YB_BASEINFO_Insuinfo psn_insu_date改成DATETIME
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(221029, "IF EXISTS (select st.name as 'TypeName',sc.name,sc.length from syscolumns sc,systypes st where sc.xtype=st.xtype and sc.id in(select id from sysobjects where xtype='U' and name='Country_YB_BASEINFO_Insuinfo')AND st.name = 'datetime' and sc.name = 'psn_insu_date' )print 'True' else Print 'False'")
        'Country_YB_MZGH Country_YB_ZYJZ 增加 Xzqh_Code列
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(221101, "IF EXISTS (SELECT top 1 * FROM syscolumns WHERE id=OBJECT_ID('Country_YB_MZGH') AND Name='Xzqh_Code' )print 'True'else Print 'False'")
        '通辽乡镇有的医院没有Dict_Relationship
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(221103, "IF EXISTS (SELECT TABLE_NAME FROM INFORMATION_SCHEMA.tables WHERE TABLE_NAME = N'Dict_Relationship')print 'true' Else Print 'False'")
        'Country_YB_JsListUpload 增加 stas_type 列
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(221104, "IF EXISTS (SELECT top 1 * FROM syscolumns WHERE id=OBJECT_ID('Country_YB_JsListUpload') AND Name='stas_type' )print 'True'else Print 'False'")
        'Bl_Cfyp Bl_Cfxm 增加 tf_id 列
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(221222, "IF EXISTS (SELECT top 1 * FROM syscolumns WHERE id=OBJECT_ID('Bl_Cfyp') AND Name='tf_id' )print 'True'else Print 'False'")
        'Country_YB_MZJS Country_YB_ZYJZ 增加 trumFlag rel_ttp_flag otpErReFlFlag 列
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(230106, "IF EXISTS (SELECT top 1 * FROM syscolumns WHERE id=OBJECT_ID('Country_YB_MZJS') AND Name='trumFlag' )print 'True'else Print 'False'")
        'E_Invoice_Config 增加 placeCode 列
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(230227, "IF EXISTS (SELECT top 1 * FROM syscolumns WHERE id=OBJECT_ID('E_Invoice_Config') AND Name='placeCode' )print 'True'else Print 'False'")
        'Zd_Yy 增加 YLJGDM 列
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(230228, "IF EXISTS (SELECT top 1 * FROM syscolumns WHERE id=OBJECT_ID('Zd_Yy') AND Name='YLJGDM' )print 'True'else Print 'False'")
        '病案首页手术增加Dict_Anst_Way
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(230323, "IF EXISTS (SELECT TABLE_NAME FROM INFORMATION_SCHEMA.tables WHERE TABLE_NAME = N'Dict_Anst_Way')print 'true' Else Print 'False'")
        'Country_YBML_YPML_XY 国家医保药品目录备注 改成 VARCHAR(MAX)
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(230417, "IF EXISTS (select st.name as 'TypeName',sc.name,sc.length from syscolumns sc,systypes st where sc.xtype=st.xtype and sc.id in(select id from sysobjects where xtype='U' and name='Country_YBML_YPML_XY')AND st.name = 'varchar' and sc.name = '国家医保药品目录备注' and sc.length = '-1')print 'True' else Print 'False'")
        '增加Country_YBML_Info
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(230429, "IF EXISTS (SELECT TABLE_NAME FROM INFORMATION_SCHEMA.tables WHERE TABLE_NAME = N'Country_YBML_Info')print 'true' Else Print 'False'")
        'Country_YBML_XMML 诊疗项目说明 改成 VARCHAR(MAX)
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(230512, "IF EXISTS (select st.name as 'TypeName',sc.name,sc.length from syscolumns sc,systypes st where sc.xtype=st.xtype and sc.id in(select id from sysobjects where xtype='U' and name='Country_YBML_XMML')AND st.name = 'varchar' and sc.name = '诊疗项目说明' and sc.length = '-1')print 'True' else Print 'False'")
        'Zd_YyYs 增加 Medical_Type 列
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(230513, "IF EXISTS (SELECT top 1 * FROM syscolumns WHERE id=OBJECT_ID('Zd_YyYs') AND Name='Medical_Type' )print 'True'else Print 'False'")
        'Country_YB_MZJS Country_YB_MZGH Country_YB_MZJZ Mz_Code 改成 VARCHAR(20)
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(230515, "IF EXISTS (select st.name as 'TypeName',sc.name,sc.length from syscolumns sc,systypes st where sc.xtype=st.xtype and sc.id in(select id from sysobjects where xtype='U' and name='Country_YB_MZJS')AND st.name = 'varchar' and sc.name = 'Mz_Code' and sc.length = '20')print 'True' else Print 'False'")
        '增加Mz_Gh_PayMents_Money
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(230519, "IF EXISTS (SELECT TABLE_NAME FROM INFORMATION_SCHEMA.tables WHERE TABLE_NAME = N'Mz_Gh_PayMents_Money')print 'true' Else Print 'False'")
        'Emr_ZhiKong2 Mx_Name Kf_PzNr  改成 VARCHAR(500)
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(230531, "IF EXISTS (select st.name as 'TypeName',sc.name,sc.length from syscolumns sc,systypes st where sc.xtype=st.xtype and sc.id in(select id from sysobjects where xtype='U' and name='Emr_ZhiKong2')AND st.name = 'varchar' and sc.name = 'Mx_Name' and sc.length = '500')print 'True' else Print 'False'")
        '增加Country_YB_JiLinQs
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(230615, "IF EXISTS (SELECT TABLE_NAME FROM INFORMATION_SCHEMA.tables WHERE TABLE_NAME = N'Country_YB_JiLinQs')print 'true' Else Print 'False'")
        '增加医保清算申请权限
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(230616, "IF EXISTS (SELECT top 1 * FROM Zd_QxModule Where Module_Code ='5026' )print 'True' else Print 'False'")
        '修复医保挂号时，存入Country_YB_MZJZ的Mz_Code错误的Bug
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(230706, "IF EXISTS (SELECT  1 FROM Country_YB_MZJZ WHERE ipt_otp_no LIKE'GH%'AND Mz_Code<>ipt_otp_no)print 'False' else Print 'True'")
        'Zd_Ml_Xm3 增加 IsEnable 列
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(230707, "IF EXISTS (SELECT top 1 * FROM syscolumns WHERE id=OBJECT_ID('Zd_Ml_Xm3') AND Name='IsEnable' )print 'True'else Print 'False'")
        '增加门诊收费查询
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(230717, "IF EXISTS (SELECT top 1 * FROM Zd_QxModule Where Module_Code ='0906' )print 'True' else Print 'False'")
        '增加 Country_YB_Admdvs
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(230801, "IF EXISTS (SELECT TABLE_NAME FROM INFORMATION_SCHEMA.tables WHERE TABLE_NAME = N'Country_YB_Admdvs')print 'true' Else Print 'False'")
        '增加 Country_YB_ReversalData
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(230818, "IF EXISTS (SELECT TABLE_NAME FROM INFORMATION_SCHEMA.tables WHERE TABLE_NAME = N'Country_YB_ReversalData')print 'true' Else Print 'False'")
        'V_Yp,V_YpKc增加字段
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(230828, "IF EXISTS (SELECT top 1 * FROM syscolumns WHERE id=OBJECT_ID('V_Yp') AND Name='Administration_Code' )print 'True' else Print 'False'")
        'Dict_Perform_Freq,Dict_Administration增加Zjm,导入执行频率
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(230901, "IF EXISTS (SELECT top 1 * FROM syscolumns WHERE id=OBJECT_ID('Dict_Perform_Freq') AND Name='Zjm' )print 'True' else Print 'False'")
        'Zd_YyPara增加门诊收费后自动发药
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(230911, "IF EXISTS (SELECT  top 1 * FROM Zd_YyPara WHERE Para_Code = '42')print 'True' else Print 'False'")
        'Zd_Templet1 增加 Templet_NewDj 列
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(230914, "IF EXISTS (SELECT top 1 * FROM syscolumns WHERE id=OBJECT_ID('Zd_Templet1') AND Name='Templet_NewDj' )print 'True'else Print 'False'")
        '删除以前的唐山医保权限
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(230919, "IF not EXISTS (SELECT  top 1 * FROM ZD_QXMODULE WHERE MODULE_CODE = '1803' And Dl_Name='医保接口')print 'True' else Print 'False'")
        '单价改成6位小数
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(231008, "IF EXISTS (select st.name as 'TypeName',sc.name,sc.length from syscolumns sc,systypes st where sc.xtype=st.xtype and sc.id in(select id from sysobjects where xtype='U' and name='Bl_Cfyp')AND st.name = 'numeric' and sc.name = 'Cf_Dj' and sc.prec=18 AND sc.scale=6 )print 'True' else Print 'False'")
        '增加 Mz_Template Mz_TemplateMx
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(231018, "IF EXISTS (SELECT TABLE_NAME FROM INFORMATION_SCHEMA.tables WHERE TABLE_NAME = N'Mz_Template')print 'true' Else Print 'False'")
        '通辽乡镇特有，bl增加Bx_BlCode，Bl_BxMoney，Ry_State，RyJsr_Code
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(231019, "IF EXISTS (SELECT top 1 * FROM syscolumns WHERE id=OBJECT_ID('Bl') AND Name='Bx_BlCode' )print 'True'else Print 'False'")
        'Mz Mz_Sum 增加 Ry_MinZu 列
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(231020, "IF EXISTS (SELECT top 1 * FROM syscolumns WHERE id=OBJECT_ID('Mz') AND Name='Ry_MinZu' )print 'True'else Print 'False'")
        '通辽乡镇特有，bl增加Pat_Source
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(231021, "IF EXISTS (SELECT top 1 * FROM syscolumns WHERE id=OBJECT_ID('Bl') AND Name='Pat_Source' )print 'True'else Print 'False'")
        '增加 Country_YB_MZZF Country_YB_ZYZF
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(231026, "IF EXISTS (SELECT TABLE_NAME FROM INFORMATION_SCHEMA.tables WHERE TABLE_NAME = N'Country_YB_MZZF')print 'true' Else Print 'False'")
        'Mz Mz_Sum 增加 Lr_JsrCode 列
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(231031, "IF EXISTS (SELECT top 1 * FROM syscolumns WHERE id=OBJECT_ID('Mz') AND Name='Lr_JsrCode' )print 'True'else Print 'False'")
        'Country_YB_APPENDIX 的 value 改成varchar(100)
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(231106, "IF EXISTS (select st.name as 'TypeName',sc.name,sc.length from syscolumns sc,systypes st where sc.xtype=st.xtype and sc.id in(select id from sysobjects where xtype='U' and name='Country_YB_APPENDIX')AND st.name = 'varchar' and sc.name = 'value' and sc.length = '100')print 'True' else Print 'False'")
        '通辽乡镇特有，Zd_YyPara增加药品入库销售价加成 药品入库批发价加成
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(231107, "IF EXISTS (SELECT  top 1 * FROM Zd_YyPara WHERE Para_Code = '38')print 'True' else Print 'False'")
        'Zd_YyPara增加通过采购价自动计算销售批发价
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(231108, "IF EXISTS (SELECT  top 1 * FROM Zd_YyPara WHERE Para_Code = '44')print 'True' else Print 'False'")
        'Zd_YyPara增加通过门诊收费抹零方式
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(231109, "IF EXISTS (SELECT  top 1 * FROM Zd_YyPara WHERE Para_Code = '45')print 'True' else Print 'False'")
        '增加 门诊输液三张表
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(231120, "IF EXISTS (SELECT TABLE_NAME FROM INFORMATION_SCHEMA.tables WHERE TABLE_NAME = N'Mz_Infusion')print 'true' Else Print 'False'")
        'Mz_Xm,Mz_Xm_Sum,Bl_Cfxm 的 Xm_Ks Xm_Ys 改成varchar(50)
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(231201, "IF EXISTS (select st.name as 'TypeName',sc.name,sc.length from syscolumns sc,systypes st where sc.xtype=st.xtype and sc.id in(select id from sysobjects where xtype='U' and name='Mz_Xm')AND st.name = 'varchar' and sc.name = 'Xm_Ks' and sc.length = '50')print 'True' else Print 'False'")
        '通辽乡镇特有 长期医嘱增加列
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(231209, "IF EXISTS (SELECT top 1 * FROM syscolumns WHERE id=OBJECT_ID('Auto_Czd') AND Name='Mx_Pd' )print 'True'else Print 'False'")
        '通辽乡镇特有 临时医嘱增加列
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(231210, "IF EXISTS (SELECT top 1 * FROM syscolumns WHERE id=OBJECT_ID('Bl_Cfyp') AND Name='Administration_Name' )print 'True'else Print 'False'")
        'Zd_Ml_Yp4 增加 KcEnable 列
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(231212, "IF EXISTS (SELECT top 1 * FROM syscolumns WHERE id=OBJECT_ID('Zd_Ml_Yp4') AND Name='KcEnable' )print 'True'else Print 'False'")
        'Auto_Cfyp 增加 Administration_Code 列
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(231219, "IF EXISTS (SELECT top 1 * FROM syscolumns WHERE id=OBJECT_ID('Auto_Cfyp') AND Name='Administration_Code' )print 'True'else Print 'False'")
        'Bl_Cfyp 增加 Administration_Code 列
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(231220, "IF EXISTS (SELECT top 1 * FROM syscolumns WHERE id=OBJECT_ID('Bl_Cfyp') AND Name='Administration_Code' )print 'True'else Print 'False'")
        '权限名称，医嘱录入改成临时医嘱
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(231221, "IF EXISTS (SELECT  top 1 * FROM ZD_QXMODULE WHERE Module_Code='0303' AND Xl_Name='临时医嘱' )print 'True' else Print 'False'")
        '增加 病人换床表
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(231222, "IF EXISTS (SELECT TABLE_NAME FROM INFORMATION_SCHEMA.tables WHERE TABLE_NAME = N'Bl_ChangeBed')print 'true' Else Print 'False'")
        'Zd_YyPara增加门诊医保挂号是否同时打印医保结算单
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(240105, "IF EXISTS (SELECT  top 1 * FROM Zd_YyPara WHERE Para_Code = '46')print 'True' else Print 'False'")
        '增加 住院诊断表Bl_Diag
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(240108, "IF EXISTS (SELECT TABLE_NAME FROM INFORMATION_SCHEMA.tables WHERE TABLE_NAME = N'Bl_Diag')print 'true' Else Print 'False'")
        'Auto_Cfxm 增加 Templet_Code 列 增加住院医嘱权限，删除临时医嘱查询和长期医嘱查询
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(240117, "IF EXISTS (SELECT top 1 * FROM syscolumns WHERE id=OBJECT_ID('Auto_Cfxm') AND Name='Templet_Code' )print 'True'else Print 'False'")
        'Bl_Cfxm 的Xm_Wc null改成否
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(240121, " IF Not EXISTS (SELECT top 1 * FROM Bl_Cfxm Where Xm_Wc is NULL )print 'True' else Print 'False'")
        '增加 管理职务相关表，权限，字段
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(240122, "IF EXISTS (SELECT TABLE_NAME FROM INFORMATION_SCHEMA.tables WHERE TABLE_NAME = N'Dict_Manage_Title')print 'true' Else Print 'False'")
        '增加 Country_YBML_PricUplmt
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(240131, "IF EXISTS (SELECT TABLE_NAME FROM INFORMATION_SCHEMA.tables WHERE TABLE_NAME = N'Country_YBML_PricUplmt')print 'true' Else Print 'False'")
        'Zd_YyPara增加输液卡样式
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(240204, "IF EXISTS (SELECT  top 1 * FROM Zd_YyPara WHERE Para_Code = '47')print 'True' else Print 'False'")
        'Emr_DataField增加民族
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(240205, "IF EXISTS (SELECT  top 1 * FROM Emr_DataField WHERE DataField = 'Ry_MinZu')print 'True' else Print 'False'")
        'Country_YB_APPENDIX 的 name 改成varchar(200)
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(240207, "IF EXISTS (select st.name as 'TypeName',sc.name,sc.length from syscolumns sc,systypes st where sc.xtype=st.xtype and sc.id in(select id from sysobjects where xtype='U' and name='Country_YB_APPENDIX')AND st.name = 'varchar' and sc.name = 'name' and sc.length = '200')print 'True' else Print 'False'")
        '增加 Mz_Diag
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(240208, "IF EXISTS (SELECT TABLE_NAME FROM INFORMATION_SCHEMA.tables WHERE TABLE_NAME = N'Mz_Diag')print 'true' Else Print 'False'")
        '增加 Mz_BlTemplate
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(240226, "IF EXISTS (SELECT TABLE_NAME FROM INFORMATION_SCHEMA.tables WHERE TABLE_NAME = N'Mz_BlTemplate')print 'true' Else Print 'False'")
        '增加 电子处方相关表
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(240227, "IF EXISTS (SELECT TABLE_NAME FROM INFORMATION_SCHEMA.tables WHERE TABLE_NAME = N'ERx_Config')print 'true' Else Print 'False'")
        'Mz_TemplateMx 增加 Administration_Code 列
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(240311, "IF EXISTS (SELECT top 1 * FROM syscolumns WHERE id=OBJECT_ID('Mz_TemplateMx') AND Name='Administration_Code' )print 'True'else Print 'False'")
        'Zd_YyPara增加门诊修改价格
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(240312, "IF EXISTS (SELECT  top 1 * FROM Zd_YyPara WHERE Para_Code = '48')print 'True' else Print 'False'")
        'Zd_YyPara增加住院修改价格
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(240318, "IF EXISTS (SELECT  top 1 * FROM Zd_YyPara WHERE Para_Code = '49')print 'True' else Print 'False'")
        'Zd_YyPara增加启用诊疗项目审核（有的医院没有这个）
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(240319, "IF EXISTS (SELECT  top 1 * FROM Zd_YyPara WHERE Para_Code = '25')print 'True' else Print 'False'")
        'Zd_YyPara增加药房发药显示库存数（有的医院没有这个）
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(240320, "IF EXISTS (SELECT  top 1 * FROM Zd_YyPara WHERE Para_Code = '40')print 'True' else Print 'False'")
        'Emr_DataField增加联系方式
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(240322, "IF EXISTS (SELECT  top 1 * FROM Emr_DataField WHERE DataField = 'Ry_Tele')print 'True' else Print 'False'")
        'Dict_Operation 增加 Zxy_Mark 列
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(240323, "IF EXISTS (SELECT top 1 * FROM syscolumns WHERE id=OBJECT_ID('Dict_Operation') AND Name='Zxy_Mark' )print 'True'else Print 'False'")
        'Mz 增加 MzCfZxType 列
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(240324, "IF EXISTS (SELECT top 1 * FROM syscolumns WHERE id=OBJECT_ID('Mz') AND Name='MzCfZxType' )print 'True'else Print 'False'")
        'ERx_Upload 增加 psn_no 列
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(240326, "IF EXISTS (SELECT top 1 * FROM syscolumns WHERE id=OBJECT_ID('ERx_Upload') AND Name='psn_no' )print 'True'else Print 'False'")
        'Country_YBML_Info 增加 memo
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(240329, "IF EXISTS (SELECT top 1 * FROM syscolumns WHERE id=OBJECT_ID('Country_YBML_Info') AND Name='memo' )print 'True'else Print 'False'")
        '进销存6张表增加reponse_msg
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(240407, "IF EXISTS (SELECT top 1 * FROM syscolumns WHERE id=OBJECT_ID('Country_YB_SpCg') AND Name='reponse_msg' )print 'True'else Print 'False'")
        '增加 Zd_Ml_Xm3Wc
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(240412, "IF EXISTS (SELECT TABLE_NAME FROM INFORMATION_SCHEMA.tables WHERE TABLE_NAME = N'Zd_Ml_Xm3Wc')print 'true' Else Print 'False'")
        'Zd_Ml_Yp3 增加 YbHcCode 列
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(240416, "IF EXISTS (SELECT top 1 * FROM syscolumns WHERE id=OBJECT_ID('Zd_Ml_Yp3') AND Name='YbHcCode' )print 'True'else Print 'False'")
        'Bl_Diag Diag_Time改成DATETIME
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(240417, "IF EXISTS (select st.name as 'TypeName',sc.name,sc.length from syscolumns sc,systypes st where sc.xtype=st.xtype and sc.id in(select id from sysobjects where xtype='U' and name='Bl_Diag')AND st.name = 'datetime' and sc.name = 'Diag_Time' )print 'True' else Print 'False'")
        '重置提醒
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(240422, "IF EXISTS (SELECT COUNT(1) FROM dbo.Zd_Msg1 WHERE Msg_Code BETWEEN '001' AND '009' HAVING COUNT(1)=9)print 'True' else Print 'False'")
        '增加 Mz_BlTemplateComb
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(240429, "IF EXISTS (SELECT TABLE_NAME FROM INFORMATION_SCHEMA.tables WHERE TABLE_NAME = N'Mz_BlTemplateComb')print 'true' Else Print 'False'")
        '增加 自定义报表三张表
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(240506, "IF EXISTS (SELECT TABLE_NAME FROM INFORMATION_SCHEMA.tables WHERE TABLE_NAME = N'SysRpt')print 'true' Else Print 'False'")
        'Zd_YyPara门诊开处方限制经手人显示
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(240511, "IF EXISTS (SELECT  top 1 * FROM Zd_YyPara WHERE Para_Code = '50')print 'True' else Print 'False'")
        'Yk_Yf2 增加 Mx_CgDw,Mx_Cfbl,Ck_YfSl,Mx_XsDw
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(240515, "IF EXISTS (SELECT top 1 * FROM syscolumns WHERE id=OBJECT_ID('Yk_Yf2') AND Name='Mx_CgDw' )print 'True'else Print 'False'")
        'Yk_Yf2 更新 Mx_CgDw,Mx_Cfbl,Ck_YfSl,Mx_XsDw
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(240516, "IF EXISTS (SELECT top 1 * FROM syscolumns WHERE id=OBJECT_ID('Yk_Yf2') AND Name='Mx_CgDw' )EXEC('IF Not EXISTS (SELECT top 1 * FROM Yk_Yf2 Where Mx_Cfbl is NULL )print ''True'' else Print ''False''')ELSE Print 'False'")
        'Zd_YyPara门诊限制使用提醒
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(240529, "IF EXISTS (SELECT  top 1 * FROM Zd_YyPara WHERE Para_Code = '51')print 'True' else Print 'False'")
        '增加 自定义报表门诊详单和科室支领
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(240613, "IF EXISTS (SELECT  top 1 * FROM SysRpt WHERE RptCode in ('0004','0005'))print 'True' else Print 'False'")
        'Country_YB_SpXsTh 增加 mdtrt_sn
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(240619, "IF EXISTS (SELECT top 1 * FROM syscolumns WHERE id=OBJECT_ID('Country_YB_SpXsTh') AND Name='mdtrt_sn' )print 'True'else Print 'False'")
        'Zd_YyPara住院天数计算方式
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(240621, "IF EXISTS (SELECT  top 1 * FROM Zd_YyPara WHERE Para_Code = '52')print 'True' else Print 'False'")
        'Mz 增加 Ry_Age_Day 列
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(240626, "IF EXISTS (SELECT top 1 * FROM syscolumns WHERE id=OBJECT_ID('Mz') AND Name='Ry_Age_Day' )print 'True'else Print 'False'")
        'Dzbl Dzbl_Sum 增加 MzGh_Code 列
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(240709, "IF EXISTS (SELECT top 1 * FROM syscolumns WHERE id=OBJECT_ID('Dzbl') AND Name='MzGh_Code' )print 'True'else Print 'False'")
        'Country_YB_SpXs 增加 mdtrt_setl_type
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(240716, "IF EXISTS (SELECT top 1 * FROM syscolumns WHERE id=OBJECT_ID('Country_YB_SpXs') AND Name='mdtrt_setl_type' )print 'True'else Print 'False'")
        'Country_YB_SpXs 增加 jxc_code
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(240720, "IF EXISTS (SELECT top 1 * FROM syscolumns WHERE id=OBJECT_ID('Country_YB_SpXs') AND Name='jxc_code' )print 'True'else Print 'False'")
        '增加追溯码三张表 Country_YB_SpKc_Drugtracinfo
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(240721, "IF EXISTS (SELECT TABLE_NAME FROM INFORMATION_SCHEMA.tables WHERE TABLE_NAME = N'Country_YB_SpKc_Drugtracinfo')print 'true' Else Print 'False'")
        'Zd_YyPara挂号有效天数
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(240819, "IF EXISTS (SELECT  top 1 * FROM Zd_YyPara WHERE Para_Code = '04')print 'True' else Print 'False'")
        '增加证件类别相关表
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(240821, "IF EXISTS (SELECT TABLE_NAME FROM INFORMATION_SCHEMA.tables WHERE TABLE_NAME = N'Dict_Common')print 'true' Else Print 'False'")
        'Materials_Dict的Materials_Name,Materials_Py,Materials_Wb改成varchar(200)
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(240904, "IF EXISTS (SELECT a.name,b.name,c.DATA_TYPE,b.max_length FROM sys.tables a join sys.columns b on b.object_id = a.object_id JOIN INFORMATION_SCHEMA.COLUMNS c on b.name=c.COLUMN_NAME and a.name=c.TABLE_NAME WHERE a.name='Materials_Dict' AND b.name='Materials_Name' AND b.max_length='200')print 'True'else Print 'False'")
        'Country_YB_SpKc 增加 manu_lotnum
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(240927, "IF EXISTS (SELECT top 1 * FROM syscolumns WHERE id=OBJECT_ID('Country_YB_SpKc') AND Name='manu_lotnum' )print 'True'else Print 'False'")
        'Zd_Yy 增加 HosStamp
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(241031, "IF EXISTS (SELECT top 1 * FROM syscolumns WHERE id=OBJECT_ID('Zd_Yy') AND Name='HosStamp' )print 'True'else Print 'False'")
        '增加 自定义报表药房入库，药品销售统计表，科室收入统计表
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(241111, "IF EXISTS (SELECT  top 1 * FROM SysRpt WHERE RptCode in ('0006','0007','0008','0009'))print 'True' else Print 'False'")
        'Country_YB_BASEINFO_Idetinfo 的 psn_idet_type psn_type_lv 改成varchar(10)
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(241118, "IF EXISTS (select st.name as 'TypeName',sc.name,sc.length from syscolumns sc,systypes st where sc.xtype=st.xtype and sc.id in(select id from sysobjects where xtype='U' and name='Country_YB_BASEINFO_Idetinfo')AND st.name = 'varchar' and sc.name = 'psn_idet_type' and sc.length = '10')print 'True' else Print 'False'")
        'Zd_Identity 增加 LinkYb_Code
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(241121, "IF EXISTS (SELECT top 1 * FROM syscolumns WHERE id=OBJECT_ID('Zd_Identity') AND Name='LinkYb_Code' )print 'True'else Print 'False'")
        'DzbL 增加 Hz_MedicalHistory VARCHAR(1000),Hz_AllergyHistory VARCHAR(1000),Hz_Treatment VARCHAR(1000)
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(241125, "IF EXISTS (SELECT top 1 * FROM syscolumns WHERE id=OBJECT_ID('DzbL') AND Name='Hz_MedicalHistory' )print 'True'else Print 'False'")
        'Zd_Ml_Xm3 增加 IsOrder
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(241129, "IF EXISTS (SELECT top 1 * FROM syscolumns WHERE id=OBJECT_ID('Zd_Ml_Xm3') AND Name='IsOrder' )print 'True'else Print 'False'")
        '增加新医保权限：报销明细查询
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(241216, "IF EXISTS (SELECT  top 1 * FROM Zd_QxModule WHERE Module_Code='5031')print 'True'else Print 'False'")
        'Zd_YyPara增加挂号是否允许重复挂号
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(241217, "IF EXISTS (SELECT  top 1 * FROM Zd_YyPara WHERE Para_Code = '54')print 'True' else Print 'False'")
        'ERx_Upload 增加 insuplc_admdvs
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(241218, "IF EXISTS (SELECT top 1 * FROM syscolumns WHERE id=OBJECT_ID('ERx_Upload') AND Name='insuplc_admdvs' )print 'True'else Print 'False'")
        'Zd_YyPara增加出院是否检查床位费
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(250106, "IF EXISTS (SELECT  top 1 * FROM Zd_YyPara WHERE Para_Code = '55')print 'True' else Print 'False'")
        'Zd_YyPara增加是否允许医生修改门诊处方金额
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(250109, "IF EXISTS (SELECT  top 1 * FROM Zd_YyPara WHERE Para_Code = '56')print 'True' else Print 'False'")
        'Zd_Yy 增加 OutRtxHosStamp
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(250217, "IF EXISTS (SELECT top 1 * FROM syscolumns WHERE id=OBJECT_ID('Zd_Yy') AND Name='OutRtxHosStamp' )print 'True'else Print 'False'")
        '删除内蒙古医保权限
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(250225, "IF Not EXISTS (SELECT top 1 * FROM Zd_QxMenu1 WHERE Menu_Code='17' AND Menu_Name='内蒙古医保')print 'True'else Print 'False'")
        '增加诊疗调价模块
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(250226, "IF EXISTS (SELECT TABLE_NAME FROM INFORMATION_SCHEMA.tables WHERE TABLE_NAME = N'Tj_Xm1')print 'true' Else Print 'False'")
        'Bl_Jf增加Pay_Way_Code，CreateDate，CreateDate
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(250301, "IF EXISTS (SELECT top 1 * FROM syscolumns WHERE id=OBJECT_ID('Bl_Jf') AND Name='Pay_Way_Code' )print 'True'else Print 'False'")
        '删除健康卡权限
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(250302, "IF Not EXISTS (SELECT top 1 * FROM Zd_QxMenu1 WHERE Menu_Code='15' AND Menu_Name='健康卡管理')print 'True'else Print 'False'")
        'Country_YB_SpCg  Country_YB_SpCgTh增加 jxc_code
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(250304, "IF EXISTS (SELECT top 1 * FROM syscolumns WHERE id=OBJECT_ID('Country_YB_SpCg') AND Name='jxc_code' )print 'True'else Print 'False'")
        'Bl增加YbJsMoney
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(250315, "IF EXISTS (SELECT top 1 * FROM syscolumns WHERE id=OBJECT_ID('Bl') AND Name='YbJsMoney' )print 'True'else Print 'False'")
'        '更新Bl_Cf的CyJz_Code
'        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(250316, "IF Not EXISTS (SELECT top 1 * FROM Bl_Cf inner join bl on bl_cf.bl_code=bl.bl_code Where CyJz_Code is null and  Ry_CyJsr IS NOT NULL AND Bl.Jz_Code IS NOT NULL)print 'True'else Print 'False'")
        '新增API参数配置
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(250319, "IF EXISTS (SELECT TABLE_NAME FROM INFORMATION_SCHEMA.tables WHERE TABLE_NAME = N'ApiConfig')print 'true' Else Print 'False'")
        'Mz Mz_Sum 增加 Original_Mz_Code
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(250321, "IF EXISTS (SELECT top 1 * FROM syscolumns WHERE id=OBJECT_ID('Mz') AND Name='Original_Mz_Code' )print 'True'else Print 'False'")
        '增加一码付相关表
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(250416, "IF EXISTS (SELECT TABLE_NAME FROM INFORMATION_SCHEMA.tables WHERE TABLE_NAME = N'ThirdPartyPayment')print 'true' Else Print 'False'")
        '对接码上放心
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(250425, "IF EXISTS (SELECT top 1 * FROM syscolumns WHERE id=OBJECT_ID('Zd_Ml_Yp3') AND Name='Drug_Identification_Code' )print 'True'else Print 'False'")
        'Country_YB_MZJS_Detail 的 fund_pay_type 改成 varchar(20)
        ZTHisPublicFunction.DBUpdate.DicUpdateSql.Add(250529, "IF EXISTS (select st.name as 'TypeName',sc.name,sc.length from syscolumns sc,systypes st where sc.xtype=st.xtype and sc.id in(select id from sysobjects where xtype='U' and name='Country_YB_MZJS_Detail')AND st.name = 'varchar' and sc.name = 'fund_pay_type' and sc.length = '20')print 'True' else Print 'False'")
    End Sub
End Class