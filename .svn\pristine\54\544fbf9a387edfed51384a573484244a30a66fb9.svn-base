﻿Imports System.Data.SqlClient
Imports Stimulsoft.Report

Public Class Yf_Jz
    Dim My_Adapter As New SqlDataAdapter
    Dim My_Dataset As New DataSet
    Dim V_Yf_Code As String
    Dim V_Yf_Name As String
    Dim V_Jsr_Code As String

    Public Sub New(ByVal Yf_Code As String, ByVal Yf_Name As String, ByVal Jsr_Code As String)
        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()
        V_Yf_Code = Yf_Code
        V_Yf_Name = Yf_Name
        V_Jsr_Code = Jsr_Code
        ' 在 InitializeComponent() 调用之后添加任何初始化。
    End Sub

    Private Sub C1Button1_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles C1Button1.Click
 

        Dim Str As String = "select ks_Code,ks_Name,Mz_Cfs,Cf_Cfs,Mz_Cfs+Cf_Cfs Z_Cfs,Mz_Money,Cf_Money,Mz_Money+Cf_Money Z_Money from (Select ks_Code,ks_Name,(SELECT Isnull(Count(*),0) FROM Mz WHERE Mz.Yy_Code='" & HisVar.HisVar.WsyCode & "' and Yf_Code='" & V_Yf_Code & "' and Mz.ks_Code=Zd_YyKs.ks_Code And Mz_FyQr_date>= '" & DateTimePicker1.Value & "' And Mz_FyQr_date<='" & DateTimePicker3.Value & "'    And Mz_Code Not In (Select Mz_Code From Mz_Ty Where Mz_Ty.Yy_Code='" & HisVar.HisVar.WsyCode & "' and Yf_Code='" & V_Yf_Code & "' and Ty_Qr='1')) " & _
   "+(SELECT Isnull(Count(*),0) FROM Mz_Sum WHERE  Yf_Code='" & V_Yf_Code & "' and Mz_Sum.ks_Code=Zd_YyKs.ks_Code And Mz_FyQr_date>= '" & DateTimePicker1.Value & "' And Mz_FyQr_date<='" & DateTimePicker3.Value & "'    And Mz_Code Not In (Select Mz_Code From Mz_Ty_Sum Where Mz_Ty_Sum.Yy_Code='" & HisVar.HisVar.WsyCode & "' and Yf_Code='" & V_Yf_Code & "' and Ty_Qr='1')) Mz_Cfs,(SELECT Isnull(Count(*),0) FROM Bl_Cf WHERE Bl_Cf.Yy_Code='" & HisVar.HisVar.WsyCode & "' and Yf_Code='" & V_Yf_Code & "' and Bl_Cf.ks_Code=Zd_YyKs.ks_Code And Cf_Qr_Date >= '" & DateTimePicker1.Value & "' And Cf_Qr_Date<='" & DateTimePicker3.Value & "'   ) AS Cf_Cfs," _
   & " (SELECT Isnull(SUM(Mz_Yp.Mz_Money), 0) FROM Mz_Yp,Mz WHERE  Yf_Code='" & V_Yf_Code & "' and Mz.ks_Code=Zd_YyKs.ks_Code And Mz_Yp.Mz_Code = Mz.Mz_Code And Mz_FyQr_date>= '" & DateTimePicker1.Value & "' And Mz_FyQr_date<='" & DateTimePicker3.Value & "'    And Mz_Ph+Mz_Yp.Mz_Code Not In (Select Mz_Ph+Mz_Code From Mz_Ty where Mz_Ty.Yy_Code='" & HisVar.HisVar.WsyCode & "' and Yf_Code='" & V_Yf_Code & "')) " & _
   " +(SELECT Isnull(SUM(Mz_Yp_Sum.Mz_Money), 0) FROM Mz_Yp_Sum,Mz_Sum WHERE  Yf_Code='" & V_Yf_Code & "' and Mz_Sum.ks_Code=Zd_YyKs.ks_Code And Mz_Yp_Sum.Mz_Code = Mz_Sum.Mz_Code And Mz_FyQr_date>= '" & DateTimePicker1.Value & "' And Mz_FyQr_date<='" & DateTimePicker3.Value & "'    And Mz_Ph+Mz_Yp_Sum.Mz_Code Not In (Select Mz_Ph+Mz_Code From Mz_Ty_Sum where Mz_Ty_Sum.Yy_Code='" & HisVar.HisVar.WsyCode & "' and Yf_Code='" & V_Yf_Code & "'))Mz_Money,(SELECT Isnull(SUM(Bl_CfYp.Cf_Money), 0) FROM Bl_CfYp,Bl_Cf WHERE Bl_Cf.Yy_Code='" & HisVar.HisVar.WsyCode & "' and Yf_Code='" & V_Yf_Code & "' and " & _
   "Bl_Cf.ks_Code=Zd_YyKs.ks_Code And Bl_CfYp.Cf_Code = Bl_Cf.Cf_Code And Cf_Qr_Date >= '" & DateTimePicker1.Value & "' And Cf_Qr_Date<='" & DateTimePicker3.Value & "'   ) AS Cf_Money from Zd_YyKs where Yy_Code='" & HisVar.HisVar.WsyCode & "' )a "

        Dim My_Dst As New DataSet
        Dim Stirpt As New StiReport
        HisVar.HisVar.Sqldal.QueryDt(My_Dst, Str, "药房日结", True)
        Stirpt.Load(".\Rpt\药房日结.mrt")
        Stirpt.ReportName = "药房日结按药材结账"
        Stirpt.RegData(My_Dst)
        Stirpt.Compile()
        Stirpt("标题") = "药房日结按药材结账(" & V_Yf_Name & ")"
        Stirpt("日结时间") = "门诊、住院开单日期：" & DateTimePicker1.Value & "至" & DateTimePicker3.Value
        Stirpt("打印经手人") = "打印经手人:" & HisVar.HisVar.JsrName
        Stirpt("打印日期") = Now
        Stirpt("操作员") = HisVar.HisVar.JsrName
        'Stirpt.Design()
        Stirpt.Show()
    End Sub



    Private Sub Yf_Jz_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        DateTimePicker1.Value = Format(Now, "yyyy-MM-dd 00:00:00")
        DateTimePicker3.Value = Format(Now, "yyyy-MM-dd 23:59:59")
    End Sub



    Private Sub C1Button3_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles C1Button3.Click


        Dim Str As String
        Str = "select ks_Code,ks_Name,Mz_Cfs,Cf_Cfs,Mz_Cfs+Cf_Cfs Z_Cfs,Mz_Money,Cf_Money,Mz_Money+Cf_Money Z_Money from (Select ks_Code,ks_Name,(SELECT Isnull(Count(*),0) FROM Mz WHERE Mz.Yy_Code='" & HisVar.HisVar.WsyCode & "' and Yf_Code='" & V_Yf_Code & "' and Mz.ks_Code=Zd_YyKs.ks_Code And Mz_FyQr_date>= '" & DateTimePicker1.Value & "' And Mz_FyQr_date<='" & DateTimePicker3.Value & "'  And Mz_Code Not In (Select Mz_Code From Mz_Ty Where Mz_Ty.Yy_Code='" & HisVar.HisVar.WsyCode & "' and Yf_Code='" & V_Yf_Code & "' and Ty_Qr='1')) " & _
   "+(SELECT Isnull(Count(*),0) FROM Mz_Sum WHERE  Yf_Code='" & V_Yf_Code & "' and Mz_Sum.ks_Code=Zd_YyKs.ks_Code And Mz_FyQr_date>= '" & DateTimePicker1.Value & "' And Mz_FyQr_date<='" & DateTimePicker3.Value & "'  And Mz_Code Not In (Select Mz_Code From Mz_Ty_Sum Where Mz_Ty_Sum.Yy_Code='" & HisVar.HisVar.WsyCode & "' and Yf_Code='" & V_Yf_Code & "' and Ty_Qr='1')) Mz_Cfs,(SELECT Isnull(Count(*),0) FROM Bl_Cf WHERE Bl_Cf.Yy_Code='" & HisVar.HisVar.WsyCode & "' and Yf_Code='" & V_Yf_Code & "' and Bl_Cf.ks_Code=Zd_YyKs.ks_Code And Cf_Qr_Date >= '" & DateTimePicker1.Value & "' And Cf_Qr_Date<='" & DateTimePicker3.Value & "' ) AS Cf_Cfs," _
   & " (SELECT Isnull(SUM(Mz_Xm.Mz_Money), 0) FROM Mz_Xm,Mz WHERE  Yf_Code='" & V_Yf_Code & "' and Mz.ks_Code=Zd_YyKs.ks_Code And Mz_Xm.Mz_Code = Mz.Mz_Code And Mz_FyQr_date>= '" & DateTimePicker1.Value & "' And Mz_FyQr_date<='" & DateTimePicker3.Value & "' And Mz_Ph+Mz_Xm.Mz_Code Not In (Select Mz_Ph+Mz_Code From Mz_Ty where Mz_Ty.Yy_Code='" & HisVar.HisVar.WsyCode & "' and Yf_Code='" & V_Yf_Code & "')) " & _
   " +(SELECT Isnull(SUM(Mz_Xm_Sum.Mz_Money), 0) FROM Mz_Xm_Sum,Mz_Sum WHERE  Yf_Code='" & V_Yf_Code & "' and Mz_Sum.ks_Code=Zd_YyKs.ks_Code And Mz_Xm_Sum.Mz_Code = Mz_Sum.Mz_Code And Mz_FyQr_date>= '" & DateTimePicker1.Value & "' And Mz_FyQr_date<='" & DateTimePicker3.Value & "'  And Mz_Ph+Mz_Xm_Sum.Mz_Code Not In (Select Mz_Ph+Mz_Code From Mz_Ty_Sum where Mz_Ty_Sum.Yy_Code='" & HisVar.HisVar.WsyCode & "' and Yf_Code='" & V_Yf_Code & "'))Mz_Money,(SELECT Isnull(SUM(Bl_CfXm.Cf_Money), 0) FROM Bl_CfXm,Bl_Cf WHERE Bl_Cf.Yy_Code='" & HisVar.HisVar.WsyCode & "' and Yf_Code='" & V_Yf_Code & "' and " & _
   "Bl_Cf.ks_Code=Zd_YyKs.ks_Code And Bl_CfXm.Cf_Code = Bl_Cf.Cf_Code And Cf_Qr_Date >= '" & DateTimePicker1.Value & "' And Cf_Qr_Date<='" & DateTimePicker3.Value & "' ) AS Cf_Money from Zd_YyKs where Yy_Code='" & HisVar.HisVar.WsyCode & "' )a "

        Dim My_Dst As New DataSet
        Dim Stirpt As New StiReport
        HisVar.HisVar.Sqldal.QueryDt(My_Dst, Str, "药房日结", True)
        Stirpt.Load(".\Rpt\药房日结.mrt")
        Stirpt.ReportName = "药房日结按诊疗结账"
        Stirpt.RegData(My_Dst)
        Stirpt.Compile()
        Stirpt("标题") = "药房日结按诊疗结账(" & V_Yf_Name & ")"
        Stirpt("日结时间") = "门诊、住院开单日期：" & DateTimePicker1.Value & "至" & DateTimePicker3.Value
        Stirpt("打印经手人") = "打印经手人:" & HisVar.HisVar.JsrName
        Stirpt("打印日期") = Now
        Stirpt("操作员") = "全部"
        'Stirpt.Design()
        Stirpt.Show()
    End Sub

    Private Sub C1Button2_Click(sender As Object, e As EventArgs) Handles C1Button2.Click
        Dim Rpt1 As New Ar_YfYp_jZ
        Dim Rpt2 As New Ar_YfYp_jZ
        Dim Rpt0 As New Ar_YfYp_Jz0
        Dim Str As String
        Dim V_ColId As Integer
        Dim V_TbRowCount As Integer
        Dim V_Newrow As DataRow

        Dim JbMoney As Double
        Dim FjbMoney As Double
        Dim WMoney As Double

        If My_Dataset.Tables("处方皮明细2") IsNot Nothing Then My_Dataset.Tables("处方皮明细2").Clear()
        If My_Dataset.Tables("处方皮明细1") IsNot Nothing Then My_Dataset.Tables("处方皮明细1").Clear()

        Str = "select ''V_Id,1 as Lb,Yp_Name,Sum(Mz_Sl) as Sl,Mz_Dj,IsJb1,Sum(Mz_Money) AS Yp_Money From (select Yp_Name+'/'+Mx_Gg+'/'+Mx_Cd AS Yp_Name,Sum(Mz_Sl) AS Mz_Sl,Mz_Dj,IsJb,'国家基本药物' as IsJb1,Sum(Mz_Yp.Mz_Money) AS Mz_Money from Mz,Mz_Yp,V_Ypkc where Mz.Mz_Code=Mz_Yp.Mz_Code And Mz_Yp.Xx_Code=V_Ypkc.Xx_Code And Mz.Yy_Code='" & HisVar.HisVar.WsyCode & "' And Mz_FyQr_Date Between '" & DateTimePicker1.Value & "' And '" & DateTimePicker3.Value & "' and Yf_Code='" & HisVar.HisVar.YfCode & "'  And Mz.Mz_Code Not In (Select Mz_Code From Mz_Ty Where Mz_Ty.Yy_Code='" &  HisVar.HisVar.WsyCode & "' and Yf_Code='" & HisVar.HisVar.YfCode & "' and Ty_Qr='1')  And IsJb='国家基本药物' And Dl_Code<>'04' Group By Yp_Code,Yp_Name,Mx_Gg,Mx_Cd,Mz_Dj,IsJb union all " & _
          "select Yp_Name+'/'+Mx_Gg+'/'+Mx_Cd AS Yp_Name,Sum(Mz_Sl) AS Mz_Sl,Mz_Dj,IsJb,'国家基本药物' as IsJb1,Sum(Mz_Yp_Sum.Mz_Money) AS Mz_Money from Mz_Sum,Mz_Yp_Sum,V_Ypkc where Mz_Sum.Mz_Code=Mz_Yp_Sum.Mz_Code And Mz_Yp_Sum.Xx_Code=V_Ypkc.Xx_Code And Mz_Sum.Yy_Code='" &  HisVar.HisVar.WsyCode & "' And Mz_FyQr_Date Between '" & DateTimePicker1.Value & "' And '" & DateTimePicker3.Value & "' and Yf_Code='" & HisVar.HisVar.YfCode & "'  And Mz_Sum.Mz_Code Not In (Select Mz_Code From Mz_Ty_Sum Where Mz_Ty_Sum.Yy_Code='" &  HisVar.HisVar.WsyCode & "' and Yf_Code='" & HisVar.HisVar.YfCode & "' and Ty_Qr='1') And IsJb='国家基本药物' And Dl_Code<>'04'  Group By Yp_Code,Yp_Name,Mx_Gg,Mx_Cd,Mz_Dj,IsJb union all " & _
          "select Yp_Name+'/'+Mx_Gg+'/'+Mx_Cd AS Yp_Name,Sum(Cf_Sl) AS Cf_Sl,Cf_Dj,IsJb,'国家基本药物' as IsJb1,Sum(Bl_Cfyp.Cf_Money) AS Cf_Money from Bl_Cf,Bl_Cfyp,V_Ypkc where Bl_Cf.Cf_Code=Bl_Cfyp.Cf_Code And Bl_Cfyp.Xx_Code=V_Ypkc.Xx_Code And Bl_Cf.Yy_Code='" &  HisVar.HisVar.WsyCode & "' And Cf_Qr_Date Between '" & DateTimePicker1.Value & "' And '" & DateTimePicker3.Value & "' and Yf_Code='" & HisVar.HisVar.YfCode & "' And IsJb='国家基本药物' And Dl_Code<>'04' Group By Yp_Code,Yp_Name,Mx_Gg,Mx_Cd,Cf_Dj,IsJb,Isjb)a group By Yp_Name,Mz_Dj,IsJb1"
        Dim My_Adapter As SqlDataAdapter = New SqlDataAdapter(Str, My_Cn)
        My_Adapter.Fill(My_Dataset, "处方皮明细1")

        If My_Dataset.Tables("处方皮明细1").Rows.Count Mod 18 <> 0 Then
            For V_TbRowCount = 1 To 18 - (My_Dataset.Tables("处方皮明细1").Rows.Count Mod 18)
                V_Newrow = My_Dataset.Tables("处方皮明细1").NewRow
                With V_Newrow
                    .Item("Lb") = 1
                    .Item("Yp_Name") = DBNull.Value
                    .Item("IsJb1") = DBNull.Value
                    .Item("Mz_Dj") = DBNull.Value
                    .Item("Sl") = DBNull.Value
                    .Item("Yp_Money") = DBNull.Value

                End With
                My_Dataset.Tables("处方皮明细1").Rows.Add(V_Newrow)
                V_Newrow.AcceptChanges()
            Next
        End If

        V_ColId = Edit_Col1(My_Dataset.Tables("处方皮明细1"), 0)
        JbMoney = IIf(My_Dataset.Tables("处方皮明细1").Compute("Sum(Yp_Money)", "") Is DBNull.Value, 0, My_Dataset.Tables("处方皮明细1").Compute("Sum(Yp_Money)", ""))
        Str = "select ''V_Id, 2 as Lb,Yp_Name,Sum(Mz_Sl) as Sl,Mz_Dj, IsJb1,Sum(Mz_Money) AS Yp_Money From (select Yp_Name+'/'+Mx_Gg+'/'+Mx_Cd AS Yp_Name,Sum(Mz_Sl) AS Mz_Sl,Mz_Dj,case IsJb when '其他' then '其他'  when '省增补基本药物' then '省增补基本药物' else isnull(isjb,'非基本药物') end as IsJb1,Sum(Mz_Yp.Mz_Money) AS Mz_Money from Mz,Mz_Yp,V_Ypkc where Mz.Mz_Code=Mz_Yp.Mz_Code And Mz_Yp.Xx_Code=V_Ypkc.Xx_Code And Mz.Yy_Code='" &  HisVar.HisVar.WsyCode & "' And Mz_FyQr_Date Between '" & DateTimePicker1.Value & "' And '" & DateTimePicker3.Value & "' and Yf_Code='" & HisVar.HisVar.YfCode & "'  And Mz.Mz_Code Not In (Select Mz_Code From Mz_Ty Where Mz_Ty.Yy_Code='" &  HisVar.HisVar.WsyCode & "' and Yf_Code='" & HisVar.HisVar.YfCode & "' and Ty_Qr='1')  And (ISNULL(IsJb, '非基本药物')<>'国家基本药物') And Dl_Code<>'04'  Group By Yp_Code,Yp_Name,Mx_Gg,Mx_Cd,Mz_Dj,IsJb union all " & _
  "select Yp_Name+'/'+Mx_Gg+'/'+Mx_Cd AS Yp_Name,Sum(Mz_Sl) AS Mz_Sl,Mz_Dj,case IsJb when '其他' then '其他'  when '省增补基本药物' then '地方基本药品' else isnull(isjb,'非基本药物') end as IsJb1,Sum(Mz_Yp_Sum.Mz_Money) AS Mz_Money from Mz_Sum,Mz_Yp_Sum,V_Ypkc where Mz_Sum.Mz_Code=Mz_Yp_Sum.Mz_Code And Mz_Yp_Sum.Xx_Code=V_Ypkc.Xx_Code And Mz_Sum.Yy_Code='" &  HisVar.HisVar.WsyCode & "' And Mz_FyQr_Date Between '" & DateTimePicker1.Value & "' And '" & DateTimePicker3.Value & "' and Yf_Code='" & HisVar.HisVar.YfCode & "'  And Mz_Sum.Mz_Code Not In (Select Mz_Code From Mz_Ty_Sum Where Mz_Ty_Sum.Yy_Code='" &  HisVar.HisVar.WsyCode & "' and Yf_Code='" & HisVar.HisVar.YfCode & "' and Ty_Qr='1') And (ISNULL(IsJb, '非基本药物')<>'国家基本药物') And Dl_Code<>'04'  Group By Yp_Code,Yp_Name,Mx_Gg,Mx_Cd,Mz_Dj,IsJb union all " & _
  "select Yp_Name+'/'+Mx_Gg+'/'+Mx_Cd AS Yp_Name,Sum(Cf_Sl) AS Cf_Sl,Cf_Dj,case IsJb when '其他' then '其他'  when '省增补基本药物' then '地方基本药品' else isnull(isjb,'非基本药物') end as IsJb1,Sum(Bl_Cfyp.Cf_Money) AS Cf_Money from Bl_Cf,Bl_Cfyp,V_Ypkc where Bl_Cf.Cf_Code=Bl_Cfyp.Cf_Code And Bl_Cfyp.Xx_Code=V_Ypkc.Xx_Code And Bl_Cf.Yy_Code='" &  HisVar.HisVar.WsyCode & "' And Cf_Qr_Date Between '" & DateTimePicker1.Value & "' And '" & DateTimePicker3.Value & "' and Yf_Code='" & HisVar.HisVar.YfCode & "' And (ISNULL(IsJb, '非基本药物')<>'国家基本药物') And Dl_Code<>'04' Group By Yp_Code,Yp_Name,Mx_Gg,Mx_Cd,Cf_Dj,IsJb)a group By Yp_Name,Mz_Dj,IsJb1"
        My_Adapter = New SqlDataAdapter(Str, My_Cn)
        My_Adapter.Fill(My_Dataset, "处方皮明细1")
        If My_Dataset.Tables("处方皮明细1").Rows.Count Mod 18 <> 0 Then
            For V_TbRowCount = 1 To 18 - (My_Dataset.Tables("处方皮明细1").Rows.Count Mod 18)
                V_Newrow = My_Dataset.Tables("处方皮明细1").NewRow
                With V_Newrow
                    .Item("Lb") = 2
                    .Item("Yp_Name") = DBNull.Value
                    .Item("IsJb1") = DBNull.Value
                    .Item("Mz_Dj") = DBNull.Value
                    .Item("Sl") = DBNull.Value
                    .Item("Yp_Money") = DBNull.Value
                End With
                My_Dataset.Tables("处方皮明细1").Rows.Add(V_Newrow)
                V_Newrow.AcceptChanges()
            Next
        End If
        V_ColId = Edit_Col1(My_Dataset.Tables("处方皮明细1"), V_ColId + 1)
        FjbMoney = IIf(My_Dataset.Tables("处方皮明细1").Compute("Sum(Yp_Money)", "") Is DBNull.Value, 0, My_Dataset.Tables("处方皮明细1").Compute("Sum(Yp_Money)", "")) - JbMoney
        With Rpt1
            .PageSettings.PaperKind = Printing.PaperKind.Custom
            .PageSettings.Orientation = DataDynamics.ActiveReports.Document.PageOrientation.Portrait
            .PageSettings.Margins.Left = 0
            .PageSettings.Margins.Right = 0
            .PageSettings.Margins.Top = 0.1
            .PageSettings.Margins.Bottom = 0.1
            .DataSource = My_Dataset.Tables("处方皮明细1")
            .Label2.Text = "处   方   皮"
            .TextBox12.Text = "统计日期：" + DateTimePicker1.Value + "至" + DateTimePicker3.Value
            .Label1.Text = "打印时间：" & Format(Now, "yyyy-MM-dd HH:mm:ss")
        End With



        Str = "select ''V_Id,1 as Lb,Yp_Name,Sum(Mz_Sl) as Sl,Mz_Dj,''IsJb1,Sum(Mz_Money) AS Yp_Money From (select Yp_Name+'/'+Mx_Gg+'/'+Mx_Cd AS Yp_Name,Sum(Mz_Sl) AS Mz_Sl,Mz_Dj,IsJb,Sum(Mz_Yp.Mz_Money) AS Mz_Money from Mz,Mz_Yp,V_Ypkc where Mz.Mz_Code=Mz_Yp.Mz_Code And Mz_Yp.Xx_Code=V_Ypkc.Xx_Code And Mz.Yy_Code='" &  HisVar.HisVar.WsyCode & "' And Mz_FyQr_Date Between '" & DateTimePicker1.Value & "' And '" & DateTimePicker3.Value & "' and Yf_Code='" & HisVar.HisVar.YfCode & "'  And Mz.Mz_Code Not In (Select Mz_Code From Mz_Ty Where Mz_Ty.Yy_Code='" &  HisVar.HisVar.WsyCode & "' and Yf_Code='" & HisVar.HisVar.YfCode & "' and Ty_Qr='1')  And  Dl_Code='04' Group By Yp_Code,Yp_Name,Mx_Gg,Mx_Cd,Mz_Dj,IsJb union all " & _
         "select Yp_Name+'/'+Mx_Gg+'/'+Mx_Cd AS Yp_Name,Sum(Mz_Sl) AS Mz_Sl,Mz_Dj,IsJb,Sum(Mz_Yp_Sum.Mz_Money) AS Mz_Money from Mz_Sum,Mz_Yp_Sum,V_Ypkc where Mz_Sum.Mz_Code=Mz_Yp_Sum.Mz_Code And Mz_Yp_Sum.Xx_Code=V_Ypkc.Xx_Code And Mz_Sum.Yy_Code='" &  HisVar.HisVar.WsyCode & "' And Mz_FyQr_Date Between '" & DateTimePicker1.Value & "' And '" & DateTimePicker3.Value & "' and Yf_Code='" & HisVar.HisVar.YfCode & "'  And Mz_Sum.Mz_Code Not In (Select Mz_Code From Mz_Ty_Sum Where Mz_Ty_Sum.Yy_Code='" &  HisVar.HisVar.WsyCode & "' and Yf_Code='" & HisVar.HisVar.YfCode & "' and Ty_Qr='1') And  Dl_Code='04'  Group By Yp_Code,Yp_Name,Mx_Gg,Mx_Cd,Mz_Dj,IsJb union all " & _
         "select Yp_Name+'/'+Mx_Gg+'/'+Mx_Cd AS Yp_Name,Sum(Cf_Sl) AS Cf_Sl,Cf_Dj,IsJb,Sum(Bl_Cfyp.Cf_Money) AS Cf_Money from Bl_Cf,Bl_Cfyp,V_Ypkc where Bl_Cf.Cf_Code=Bl_Cfyp.Cf_Code And Bl_Cfyp.Xx_Code=V_Ypkc.Xx_Code And Bl_Cf.Yy_Code='" &  HisVar.HisVar.WsyCode & "' And Cf_Qr_Date Between '" & DateTimePicker1.Value & "' And '" & DateTimePicker3.Value & "' and Yf_Code='" & HisVar.HisVar.YfCode & "' And  Dl_Code='04' Group By Yp_Code,Yp_Name,Mx_Gg,Mx_Cd,Cf_Dj,IsJb)a group By Yp_Name,Mz_Dj,IsJb"

        My_Adapter = New SqlDataAdapter(Str, My_Cn)
        My_Adapter.Fill(My_Dataset, "处方皮明细2")

        Str = "select  1 as Lb,''V_Id,Yp_Name,Sum(Mz_Sl) as Sl,Mz_Dj,''IsJb1,Sum(Mz_Money) AS Yp_Money From (select Xm_Name AS Yp_Name,Sum(Mz_Sl) AS Mz_Sl,Mz_Dj,Sum(Mz_Xm.Mz_Money) AS Mz_Money from Mz,Mz_Xm,Zd_Ml_Xm3 where Mz.Mz_Code=Mz_Xm.Mz_Code And Mz_Xm.Xm_Code=Zd_Ml_Xm3.Xm_Code And  Mz.Yy_Code='" &  HisVar.HisVar.WsyCode & "' And Mz_FyQr_Date Between '" & DateTimePicker1.Value & "' And '" & DateTimePicker3.Value & "' and Yf_Code='" & HisVar.HisVar.YfCode & "'  And Mz.Mz_Code Not In (Select Mz_Code From Mz_Ty Where Mz_Ty.Yy_Code='" &  HisVar.HisVar.WsyCode & "' and Yf_Code='" & HisVar.HisVar.YfCode & "' and Ty_Qr='1')   Group By Xm_Name,Mz_Dj union all " & _
         "select Xm_Name as Yp_Name,Sum(Mz_Sl) AS Mz_Sl,Mz_Dj,Sum(Mz_Xm_Sum.Mz_Money) AS Mz_Money from Mz_Sum,Mz_Xm_Sum,Zd_Ml_Xm3 where Mz_Sum.Mz_Code=Mz_Xm_Sum.Mz_Code And Mz_Xm_Sum.Xm_Code=Zd_Ml_Xm3.Xm_Code And Mz_Sum.Yy_Code='" &  HisVar.HisVar.WsyCode & "' And Mz_FyQr_Date Between '" & DateTimePicker1.Value & "' And '" & DateTimePicker3.Value & "' and Yf_Code='" & HisVar.HisVar.YfCode & "'  And Mz_Sum.Mz_Code Not In (Select Mz_Code From Mz_Ty_Sum Where Mz_Ty_Sum.Yy_Code='" &  HisVar.HisVar.WsyCode & "' and Yf_Code='" & HisVar.HisVar.YfCode & "' and Ty_Qr='1')   Group By Xm_Name,Mz_Dj union all " & _
         "select Xm_Name as Yp_Name,Sum(Cf_Sl) AS Cf_Sl,Cf_Dj,Sum(Bl_CfXm.Cf_Money) AS Cf_Money from Bl_Cf,Bl_CfXm,Zd_Ml_Xm3 where Bl_Cf.Cf_Code=Bl_CfXm.Cf_Code And Bl_CfXm.Xm_Code=Zd_Ml_Xm3.Xm_Code and Bl_Cf.Yy_Code='" &  HisVar.HisVar.WsyCode & "' And Cf_Qr_Date Between '" & DateTimePicker1.Value & "' And '" & DateTimePicker3.Value & "' and Yf_Code='" & HisVar.HisVar.YfCode & "'  Group By Xm_Name,Cf_Dj)a group By Yp_Name,Mz_Dj"
        My_Adapter = New SqlDataAdapter(Str, My_Cn)
        My_Adapter.Fill(My_Dataset, "处方皮明细2")

        If My_Dataset.Tables("处方皮明细2").Rows.Count Mod 18 <> 0 Then
            For V_TbRowCount = 1 To 18 - (My_Dataset.Tables("处方皮明细2").Rows.Count Mod 18)
                V_Newrow = My_Dataset.Tables("处方皮明细2").NewRow
                With V_Newrow
                    .Item("Lb") = 1
                    .Item("Yp_Name") = DBNull.Value
                    .Item("IsJb1") = DBNull.Value
                    .Item("Mz_Dj") = DBNull.Value
                    .Item("Sl") = DBNull.Value
                    .Item("Yp_Money") = DBNull.Value
                End With
                My_Dataset.Tables("处方皮明细2").Rows.Add(V_Newrow)
                V_Newrow.AcceptChanges()
            Next
        End If
        V_ColId = Edit_Col1(My_Dataset.Tables("处方皮明细2"), 0)

        WMoney = IIf(My_Dataset.Tables("处方皮明细2").Compute("Sum(Yp_Money)", "") Is DBNull.Value, 0, My_Dataset.Tables("处方皮明细2").Compute("Sum(Yp_Money)", ""))


        With Rpt2
            .PageSettings.PaperKind = Printing.PaperKind.Custom
            .PageSettings.Orientation = DataDynamics.ActiveReports.Document.PageOrientation.Portrait
            .PageSettings.Margins.Left = 0
            .PageSettings.Margins.Right = 0
            .PageSettings.Margins.Top = 0.1
            .PageSettings.Margins.Bottom = 0.1
            .DataSource = My_Dataset.Tables("处方皮明细2")
            .Label2.Text = "处   方   皮"
            .TextBox12.Text = "统计日期：" + DateTimePicker1.Value + "至" + DateTimePicker3.Value
            .Label1.Text = "打印时间：" & Format(Now, "yyyy-MM-dd HH:mm:ss")
        End With

        Rpt0.SubReport1.Report = Rpt1
        Rpt0.SubReport1.Visible = True

        Rpt0.SubReport2.Report = Rpt2
        Rpt0.SubReport2.Visible = True

        With Rpt0
            .PageSettings.PaperKind = Printing.PaperKind.A4
            .PageSettings.Orientation = DataDynamics.ActiveReports.Document.PageOrientation.Portrait
            .PageSettings.Margins.Left = 0
            .PageSettings.Margins.Right = 0
            .PageSettings.Margins.Top = 0.176
            .PageSettings.Margins.Bottom = 0.176
        End With

        Rpt0.Run()

        Dim vform As New PublicForm.Pr_Cfj(Me.Name, "打印出发皮", Nothing, Nothing)
        vform.Viewer1.Document = Rpt0.Document
        vform.ShowDialog()
    End Sub

    private Function Edit_Col1(ByVal My_Table As DataTable, ByVal V_Id As Integer) As Integer '处方皮

        Dim My_Row As DataRow
        Dim i As Integer = 0
        For Each My_Row In My_Table.Rows
            If My_Row.RowState = DataRowState.Modified Then Continue For
            With My_Row
                .BeginEdit()
                .Item(0) = i \ 18 + V_Id
                .EndEdit()
            End With
            i = i + 1
        Next

        Edit_Col1 = i \ 18 + V_Id


    End Function
End Class