﻿
namespace ERX
{
    partial class ErxQuery
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.tabControlEx1 = new CustomControl.TabControlEx();
            this.tabPage1 = new System.Windows.Forms.TabPage();
            this.myGrid1 = new CustomControl.MyGrid();
            this.tabPage2 = new System.Windows.Forms.TabPage();
            this.tabPage3 = new System.Windows.Forms.TabPage();
            this.myGrid3 = new CustomControl.MyGrid();
            this.tabPage4 = new System.Windows.Forms.TabPage();
            this.myGrid4 = new CustomControl.MyGrid();
            this.myGrid2 = new CustomControl.MyGrid();
            this.tabControlEx1.SuspendLayout();
            this.tabPage1.SuspendLayout();
            this.tabPage2.SuspendLayout();
            this.tabPage3.SuspendLayout();
            this.tabPage4.SuspendLayout();
            this.SuspendLayout();
            // 
            // tabControlEx1
            // 
            this.tabControlEx1.ArrowColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(79)))), ((int)(((byte)(125)))));
            this.tabControlEx1.Controls.Add(this.tabPage1);
            this.tabControlEx1.Controls.Add(this.tabPage2);
            this.tabControlEx1.Controls.Add(this.tabPage3);
            this.tabControlEx1.Controls.Add(this.tabPage4);
            this.tabControlEx1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tabControlEx1.Font = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.tabControlEx1.Location = new System.Drawing.Point(0, 0);
            this.tabControlEx1.Name = "tabControlEx1";
            this.tabControlEx1.SelectedIndex = 0;
            this.tabControlEx1.Size = new System.Drawing.Size(800, 450);
            this.tabControlEx1.TabIndex = 0;
            // 
            // tabPage1
            // 
            this.tabPage1.Controls.Add(this.myGrid1);
            this.tabPage1.Location = new System.Drawing.Point(4, 26);
            this.tabPage1.Name = "tabPage1";
            this.tabPage1.Padding = new System.Windows.Forms.Padding(3);
            this.tabPage1.Size = new System.Drawing.Size(792, 420);
            this.tabPage1.TabIndex = 0;
            this.tabPage1.Text = "处方信息";
            this.tabPage1.UseVisualStyleBackColor = true;
            // 
            // myGrid1
            // 
            this.myGrid1.AllowColMove = true;
            this.myGrid1.AllowFilter = true;
            this.myGrid1.CanCustomCol = false;
            this.myGrid1.Caption = "";
            this.myGrid1.ChildGrid = null;
            this.myGrid1.Col = 0;
            this.myGrid1.ColumnFooters = false;
            this.myGrid1.ColumnHeaders = true;
            this.myGrid1.DataMember = "";
            this.myGrid1.DataSource = null;
            this.myGrid1.DataView = C1.Win.C1TrueDBGrid.DataViewEnum.Normal;
            this.myGrid1.DirectionAfterEnter = C1.Win.C1TrueDBGrid.DirectionAfterEnterEnum.MoveRight;
            this.myGrid1.FetchRowStyles = false;
            this.myGrid1.FilterBar = false;
            this.myGrid1.GroupByAreaVisible = true;
            this.myGrid1.Location = new System.Drawing.Point(321, 135);
            this.myGrid1.Margin = new System.Windows.Forms.Padding(0);
            this.myGrid1.MarqueeStyle = C1.Win.C1TrueDBGrid.MarqueeEnum.DottedCellBorder;
            this.myGrid1.Name = "myGrid1";
            this.myGrid1.Size = new System.Drawing.Size(150, 150);
            this.myGrid1.TabAction = C1.Win.C1TrueDBGrid.TabActionEnum.ControlNavigation;
            this.myGrid1.TabIndex = 1;
            this.myGrid1.Xmlpath = null;
            // 
            // tabPage2
            // 
            this.tabPage2.Controls.Add(this.myGrid2);
            this.tabPage2.Location = new System.Drawing.Point(4, 26);
            this.tabPage2.Name = "tabPage2";
            this.tabPage2.Padding = new System.Windows.Forms.Padding(3);
            this.tabPage2.Size = new System.Drawing.Size(792, 420);
            this.tabPage2.TabIndex = 1;
            this.tabPage2.Text = "处方明细信息";
            this.tabPage2.UseVisualStyleBackColor = true;
            // 
            // tabPage3
            // 
            this.tabPage3.Controls.Add(this.myGrid3);
            this.tabPage3.Location = new System.Drawing.Point(4, 26);
            this.tabPage3.Name = "tabPage3";
            this.tabPage3.Padding = new System.Windows.Forms.Padding(3);
            this.tabPage3.Size = new System.Drawing.Size(792, 420);
            this.tabPage3.TabIndex = 2;
            this.tabPage3.Text = "就诊信息";
            this.tabPage3.UseVisualStyleBackColor = true;
            // 
            // myGrid3
            // 
            this.myGrid3.AllowColMove = true;
            this.myGrid3.AllowFilter = true;
            this.myGrid3.CanCustomCol = false;
            this.myGrid3.Caption = "";
            this.myGrid3.ChildGrid = null;
            this.myGrid3.Col = 0;
            this.myGrid3.ColumnFooters = false;
            this.myGrid3.ColumnHeaders = true;
            this.myGrid3.DataMember = "";
            this.myGrid3.DataSource = null;
            this.myGrid3.DataView = C1.Win.C1TrueDBGrid.DataViewEnum.Normal;
            this.myGrid3.DirectionAfterEnter = C1.Win.C1TrueDBGrid.DirectionAfterEnterEnum.MoveRight;
            this.myGrid3.FetchRowStyles = false;
            this.myGrid3.FilterBar = false;
            this.myGrid3.GroupByAreaVisible = true;
            this.myGrid3.Location = new System.Drawing.Point(309, 123);
            this.myGrid3.Margin = new System.Windows.Forms.Padding(0);
            this.myGrid3.MarqueeStyle = C1.Win.C1TrueDBGrid.MarqueeEnum.DottedCellBorder;
            this.myGrid3.Name = "myGrid3";
            this.myGrid3.Size = new System.Drawing.Size(175, 175);
            this.myGrid3.TabAction = C1.Win.C1TrueDBGrid.TabActionEnum.ControlNavigation;
            this.myGrid3.TabIndex = 2;
            this.myGrid3.Xmlpath = null;
            // 
            // tabPage4
            // 
            this.tabPage4.Controls.Add(this.myGrid4);
            this.tabPage4.Location = new System.Drawing.Point(4, 26);
            this.tabPage4.Name = "tabPage4";
            this.tabPage4.Padding = new System.Windows.Forms.Padding(3);
            this.tabPage4.Size = new System.Drawing.Size(792, 420);
            this.tabPage4.TabIndex = 3;
            this.tabPage4.Text = "诊断信息";
            this.tabPage4.UseVisualStyleBackColor = true;
            // 
            // myGrid4
            // 
            this.myGrid4.AllowColMove = true;
            this.myGrid4.AllowFilter = true;
            this.myGrid4.CanCustomCol = false;
            this.myGrid4.Caption = "";
            this.myGrid4.ChildGrid = null;
            this.myGrid4.Col = 0;
            this.myGrid4.ColumnFooters = false;
            this.myGrid4.ColumnHeaders = true;
            this.myGrid4.DataMember = "";
            this.myGrid4.DataSource = null;
            this.myGrid4.DataView = C1.Win.C1TrueDBGrid.DataViewEnum.Normal;
            this.myGrid4.DirectionAfterEnter = C1.Win.C1TrueDBGrid.DirectionAfterEnterEnum.MoveRight;
            this.myGrid4.FetchRowStyles = false;
            this.myGrid4.FilterBar = false;
            this.myGrid4.GroupByAreaVisible = true;
            this.myGrid4.Location = new System.Drawing.Point(294, 108);
            this.myGrid4.Margin = new System.Windows.Forms.Padding(0);
            this.myGrid4.MarqueeStyle = C1.Win.C1TrueDBGrid.MarqueeEnum.DottedCellBorder;
            this.myGrid4.Name = "myGrid4";
            this.myGrid4.Size = new System.Drawing.Size(204, 204);
            this.myGrid4.TabAction = C1.Win.C1TrueDBGrid.TabActionEnum.ControlNavigation;
            this.myGrid4.TabIndex = 3;
            this.myGrid4.Xmlpath = null;
            // 
            // myGrid2
            // 
            this.myGrid2.AllowColMove = true;
            this.myGrid2.AllowFilter = true;
            this.myGrid2.CanCustomCol = false;
            this.myGrid2.Caption = "";
            this.myGrid2.ChildGrid = null;
            this.myGrid2.Col = 0;
            this.myGrid2.ColumnFooters = false;
            this.myGrid2.ColumnHeaders = true;
            this.myGrid2.DataMember = "";
            this.myGrid2.DataSource = null;
            this.myGrid2.DataView = C1.Win.C1TrueDBGrid.DataViewEnum.Normal;
            this.myGrid2.DirectionAfterEnter = C1.Win.C1TrueDBGrid.DirectionAfterEnterEnum.MoveRight;
            this.myGrid2.FetchRowStyles = false;
            this.myGrid2.FilterBar = false;
            this.myGrid2.GroupByAreaVisible = true;
            this.myGrid2.Location = new System.Drawing.Point(277, 91);
            this.myGrid2.Margin = new System.Windows.Forms.Padding(0);
            this.myGrid2.MarqueeStyle = C1.Win.C1TrueDBGrid.MarqueeEnum.DottedCellBorder;
            this.myGrid2.Name = "myGrid2";
            this.myGrid2.Size = new System.Drawing.Size(238, 238);
            this.myGrid2.TabAction = C1.Win.C1TrueDBGrid.TabActionEnum.ControlNavigation;
            this.myGrid2.TabIndex = 4;
            this.myGrid2.Xmlpath = null;
            // 
            // ErxQuery
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(800, 450);
            this.Controls.Add(this.tabControlEx1);
            this.Name = "ErxQuery";
            this.Text = "电子处方信息查询";
            this.Load += new System.EventHandler(this.ErxQuery_Load);
            this.tabControlEx1.ResumeLayout(false);
            this.tabPage1.ResumeLayout(false);
            this.tabPage2.ResumeLayout(false);
            this.tabPage3.ResumeLayout(false);
            this.tabPage4.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private CustomControl.TabControlEx tabControlEx1;
        private System.Windows.Forms.TabPage tabPage1;
        private System.Windows.Forms.TabPage tabPage2;
        private System.Windows.Forms.TabPage tabPage3;
        private System.Windows.Forms.TabPage tabPage4;
        private CustomControl.MyGrid myGrid1;
        private CustomControl.MyGrid myGrid3;
        private CustomControl.MyGrid myGrid4;
        private CustomControl.MyGrid myGrid2;
    }
}