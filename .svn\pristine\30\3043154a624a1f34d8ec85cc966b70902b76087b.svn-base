﻿Imports System.Data.SqlClient
Imports HisControl
Imports BaseClass

Public Class YkYf_TkPf3

#Region "传参"
    Dim My_DataSet As New DataSet
    Dim Rform As BaseForm
    Dim Rinsert As Boolean
    'Dim Rdate As Date
    Dim Rrow As DataRow
    Dim RZbtb As DataTable
    Dim Rtdbgrid As C1.Win.C1TrueDBGrid.C1TrueDBGrid
    Dim Rcode As String
    Dim Rrc As C_RowChange
    Dim Form_Lb As String
#End Region

    Public Sub New(ByVal tform As BaseForm, ByVal tinsert As Boolean, ByVal trow As DataRow, ByVal tZbtb As DataTable, ByVal ttdbgrid As C1.Win.C1TrueDBGrid.C1TrueDBGrid, ByVal tcode As String, ByRef trc As C_RowChange, ByVal Rform_lb As String)
        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()
        Rform = tform
        Rinsert = tinsert
        Rrow = trow
        RZbtb = tZbtb
        Rtdbgrid = ttdbgrid
        Rcode = tcode
        Rrc = trc
        Form_Lb = Rform_lb
        ' 在 InitializeComponent() 调用之后添加任何初始化。

    End Sub

    Protected Overrides Sub OnKeyDown(ByVal e As System.Windows.Forms.KeyEventArgs)
        MyBase.OnKeyDown(e)
        If e.Control = True And e.KeyCode = Keys.S Then
            Comm1.Select()
            Call Comm_Click(Comm1, Nothing)
        End If
    End Sub

    Private Sub Yk_TkPf3_FormClosed(ByVal sender As Object, ByVal e As System.Windows.Forms.FormClosedEventArgs) Handles Me.FormClosed
        RemoveHandler Rrc.RowChanged, New BaseClass.RowChangedHandler(AddressOf Data_Show)
        Me.My_DataSet.Dispose()
        Rtdbgrid.Select()
    End Sub

    Private Sub Yk_TkPf3_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        AddHandler Rrc.RowChanged, New BaseClass.RowChangedHandler(AddressOf Data_Show)
        Call Form_Init()

        If Rinsert = True Then Call Data_Clear() Else Call Data_Show(Rrow)

    End Sub

#Region "窗体__事件"

    Private Sub Form_Init()

        '按扭初始化
        Call P_Comm(Me.Comm1)
        Call P_Comm(Me.Comm2)

        With Me.C1Numeric1
            .Value = 0
            .FormatType = C1.Win.C1Input.FormatTypeEnum.CustomFormat
            .CustomFormat = "###,###,###0.####"
            .VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
            .ShowContextMenu = False
            .EmptyAsNull = True
        End With

        With Me.C1Numeric2
            .Value = 0
            .FormatType = C1.Win.C1Input.FormatTypeEnum.CustomFormat
            .CustomFormat = "###,###,###0.00####"
            .VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
            .ShowContextMenu = False
            .EmptyAsNull = True
        End With

        With Me.C1Numeric4
            .Value = 0
            .FormatType = C1.Win.C1Input.FormatTypeEnum.CustomFormat
            .CustomFormat = "###,###,###0.00##"
            .VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
            .ShowContextMenu = False
            .EmptyAsNull = True
        End With

        With Me.C1NumericEdit1
            .Value = 0
            .FormatType = C1.Win.C1Input.FormatTypeEnum.CustomFormat
            .CustomFormat = "###,###,###0.####"
            .VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
            .ShowContextMenu = False
            .EmptyAsNull = True
        End With

        With Me.C1NumericEdit2
            .Value = 0
            .FormatType = C1.Win.C1Input.FormatTypeEnum.CustomFormat
            .CustomFormat = "###,###,###0.00####"
            .VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
            .ShowContextMenu = False
            .EmptyAsNull = True
        End With



        With Me.C1DateEdit1
            .Value = DBNull.Value
            .DateTimeInput = True     '显示日期的开关
            .AutoChangePosition = False
            .CaseSensitive = True
            .VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
            .InitialSelection = C1.Win.C1Input.InitialSelectionEnum.CaretAtStart
            .VisibleButtons = C1.Win.C1Input.DropDownControlButtonFlags.None
            .FormatType = C1.Win.C1Input.FormatTypeEnum.CustomFormat
            .EditFormat.FormatType = C1.Win.C1Input.FormatTypeEnum.CustomFormat
            .EditFormat.CustomFormat = "yyyy-MM-dd"

            .DisplayFormat.FormatType = C1.Win.C1Input.FormatTypeEnum.CustomFormat
            .DisplayFormat.CustomFormat = "yyyy-MM-dd"

            .MaskInfo.AutoTabWhenFilled = True
            .AcceptsTab = True
            .EmptyAsNull = True
            .ValueIsDbNull = True
            With .ErrorInfo
                .BeepOnError = True
                .CanLoseFocus = False
                .ErrorAction = C1.Win.C1Input.ErrorActionEnum.None
                .ErrorProvider = Me.ErrorProvider1
            End With
        End With


        If Form_Lb = "药库退供应商" Then
            HisVar.HisVar.Sqldal.QueryDt(My_DataSet, "Select  Yp_Jc,Yp_Name,Jx_Name,Mx_Gyzz,Mx_Gg,Mx_Cd,Mx_Code,Jx_Code,Mx_Cgdw,Mx_Xsdw,Dl_Code,Yp_Code,Yk_Sl,Yk_Cgj,Yp_Ph,Yp_Yxq,IsJb,Xx_Code  From V_YpKc Where Yy_Code='" & HisVar.HisVar.WsyCode & "' And Yk_Sl>0 and Yp_Yxq>='" & Format(Now, "yyyy-MM-dd") & "'Order by Yp_Jc", "药品字典", True)
        ElseIf Form_Lb = "药房退供应商" Then
            HisVar.HisVar.Sqldal.QueryDt(My_DataSet, "Select  Yp_Jc,Yp_Name,Jx_Name,Mx_Gyzz,Mx_Gg,Mx_Cd,Mx_Code,Jx_Code,Mx_Cgdw,Mx_Xsdw,Dl_Code,Yp_Code,Yf_Sl" & Mid(HisVar.HisVar.YfCode, 6) & ",Yk_Cgj/Mx_Cfbl as Yf_Cgj,Yp_Ph,Yp_Yxq,IsJb,Xx_Code  From V_YpKc Where Yy_Code='" & HisVar.HisVar.WsyCode & "' And Yf_Sl" & Mid(HisVar.HisVar.YfCode, 6) & ">0 and Yp_Yxq>='" & Format(Now, "yyyy-MM-dd") & "'Order by Yp_Jc", "药品字典", True)
        End If


        '药品__字典C1Combo实例化

        Dim My_Combo As BaseClass.C_Combo2 = New BaseClass.C_Combo2(C1Combo1, My_DataSet.Tables("药品字典").DefaultView, "Yp_Name", "Xx_Code", 820)
        With My_Combo
            .Init_TDBCombo()
            .Init_Colum("Yp_Jc", "简称", 80, "左")
            .Init_Colum("Yp_Name", "名称", 150, "左")
            .Init_Colum("Jx_Name", "剂型", 70, "左")
            .Init_Colum("Mx_Gyzz", "批准文号", 80, "左")
            .Init_Colum("Mx_Gg", "规格", 65, "左")
            .Init_Colum("Mx_Cd", "产地", 90, "左")
            If Form_Lb = "药库退供应商" Then
                .Init_Colum("Yk_Sl", "库存", 55, "右")
                .Init_Colum("Yk_Cgj", " 采购价", 60, "右")
            ElseIf Form_Lb = "药房退供应商" Then
                .Init_Colum("Yf_Sl" & Mid(HisVar.HisVar.YfCode, 6), "库存", 55, "右")
                .Init_Colum("Yf_Cgj", " 采购价", 60, "右")
            End If
            .Init_Colum("Yp_Ph", "批号", 60, "右")
            .Init_Colum("Yp_Yxq", "有效期", 60, "左")
            .Init_Colum("IsJb", "基本药品", 60, "中")
            .Init_Colum("Xx_Code", "", 0, "中")
            .Init_Colum("Mx_Code", "", 0, "中")
            .Init_Colum("Jx_Code", "", 0, "中")
            .Init_Colum("Mx_Cgdw", "", 0, "中")
            .Init_Colum("Mx_Xsdw", "", 0, "中")
            .Init_Colum("Dl_Code", "", 0, "中")
            .Init_Colum("Yp_Code", "", 0, "中")
            .MaxDropDownItems(15)
            .SelectedIndex(-1)
        End With
        With C1Combo1
            If Form_Lb = "药库退供应商" Then
                .Columns("Yk_Sl").NumberFormat = "0.###"
                .Columns("Yk_Cgj").NumberFormat = "0.00####"
            ElseIf Form_Lb = "药房退供应商" Then
                .Columns("Yf_Sl" & Mid(HisVar.HisVar.YfCode, 6)).NumberFormat = "0.###"
                .Columns("Yf_Cgj").NumberFormat = "0.00####"
            End If
           
            .Columns("Yp_Yxq").NumberFormat = "yyyy-MM-dd"
        End With
        C1Combo1.AutoSelect = False
        C1Combo1.AutoCompletion = False
        C1Combo1.Splits(0).DisplayColumns(3).Visible = False
        '药品字典视图

        My_DataSet.Tables("药品字典").DefaultView.Sort = "Yp_Jc Asc "

    End Sub

#End Region

#Region "其它项目"

    Private Sub C1Numeric_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles C1TextBox1.KeyPress, C1TextBox2.KeyPress, C1TextBox3.KeyPress, C1TextBox7.KeyPress, C1Numeric1.KeyPress, C1Numeric2.KeyPress, C1Numeric4.KeyPress, C1NumericEdit1.KeyPress, C1DateEdit1.KeyPress, C1NumericEdit2.KeyPress
        If e.KeyChar <> Chr(Keys.Return) Then Exit Sub
        e.Handled = True
        System.Windows.Forms.SendKeys.Send("{Tab}")
    End Sub

    Private Sub C1Numeric1_Validated(ByVal sender As Object, ByVal e As System.EventArgs) Handles C1Numeric1.Validated, C1NumericEdit2.Validated
        If C1Numeric1.Value Is DBNull.Value Then C1Numeric1.Value = 0
        If C1NumericEdit2.Value Is DBNull.Value Then C1NumericEdit2.Value = 0

        C1Numeric4.Value = C1Numeric1.Value * C1NumericEdit2.Value
    End Sub

#End Region

#Region "控件__动作"



    Private Sub Comm_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Comm1.Click, Comm2.Click
        Select Case sender.tag
            Case "保存"

                If C1Combo1.SelectedValue = "" Then
                    Beep()
                    MsgBox("药品编码不能为空!", MsgBoxStyle.OkOnly + MsgBoxStyle.Exclamation, "提示:")
                    C1Combo1.Select()
                    Exit Sub
                End If


                If C1Numeric1.Text = "" Then
                    Beep()
                    MsgBox("退库数量有误!", MsgBoxStyle.OkOnly + MsgBoxStyle.Exclamation, "提示:")
                    C1Numeric1.Select()
                    Exit Sub
                End If

                If C1Numeric1.Value > C1NumericEdit1.Value Then
                    Beep()
                    MsgBox("退库数量不能大于库存数量!", MsgBoxStyle.OkOnly + MsgBoxStyle.Exclamation, "提示:")
                    C1Numeric1.Select()
                    Exit Sub
                End If

                If C1Numeric1.Value <= 0 Then
                    Beep()
                    MsgBox("请输入正确的退库数量!", MsgBoxStyle.OkOnly + MsgBoxStyle.Exclamation, "提示:")
                    C1Numeric1.Select()
                    Exit Sub
                End If

                C1Numeric4.Value = C1Numeric1.Value * C1NumericEdit2.Value

                Call Save_Add()
                Rform.F_Sum()
            Case "取消"
                C1Combo1.SelectedValue = -1
                C1Combo1.Text = ""
                Rtdbgrid.Focus()
                Me.Close()
        End Select
    End Sub

    Private Sub C1Move_Click(ByVal sender As Object, ByVal e As C1.Win.C1Command.ClickEventArgs) Handles Move2.Click, Move3.Click, Move5.Click
        If sender.text = "新增" Then
            Rinsert = True
            Call Data_Clear()
        Else
            Rinsert = False
            Select Case sender.text
                Case "上移"
                    Rtdbgrid.MovePrevious()
                Case "下移"
                    Rtdbgrid.MoveNext()
            End Select

        End If

    End Sub


#Region "C1Combo1"

    Private Sub C1Combo1_RowChange(ByVal sender As Object, ByVal e As System.EventArgs) Handles C1Combo1.RowChange

        If C1Combo1.Text = "" Then

        Else
            If C1Combo1.WillChangeToValue = "" Then

            Else
                C1TextBox1.Text = C1Combo1.Columns("Mx_Gyzz").Text & ""
                Label5.Text = C1Combo1.Columns("Jx_Name").Value & ""
                C1TextBox3.Text = C1Combo1.Columns("Mx_Gg").Text & ""
                C1TextBox2.Text = C1Combo1.Columns("Mx_Cd").Text & ""
                If Form_Lb = "药库退供应商" Then
                    Label17.Text = C1Combo1.Columns("Mx_Cgdw").Text & ""
                    Label1.Text = C1Combo1.Columns("Mx_Cgdw").Text & ""
                    Label15.Text = "/" & C1Combo1.Columns("Mx_Cgdw").Text & ""
                    Label18.Text = "/" & C1Combo1.Columns("Mx_Cgdw").Text & ""
                    C1Numeric2.Value = C1Combo1.Columns("Yk_Cgj").Value & ""
                    C1NumericEdit1.Value = C1Combo1.Columns("Yk_Sl").Value & ""
                ElseIf Form_Lb = "药房退供应商" Then
                    Label17.Text = C1Combo1.Columns("Mx_Xsdw").Text & ""
                    Label1.Text = C1Combo1.Columns("Mx_Xsdw").Text & ""
                    Label15.Text = "/" & C1Combo1.Columns("Mx_Xsdw").Text & ""
                    Label18.Text = "/" & C1Combo1.Columns("Mx_Xsdw").Text & ""
                    C1Numeric2.Value = C1Combo1.Columns("Yf_Cgj").Value & ""
                    C1NumericEdit1.Value = C1Combo1.Columns("Yf_Sl" & Mid(HisVar.HisVar.YfCode, 6)).Value & ""
                End If
                Label4.Text = C1Combo1.Columns("Yp_Ph").Value & ""
                C1DateEdit1.Value = C1Combo1.Columns("Yp_Yxq").Value & ""
                C1NumericEdit2.Value = C1Numeric2.Value '退库单价默认为采购价
            End If
        End If
    End Sub


    Private Sub C1Combo1_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles C1Combo1.KeyUp

        If (e.KeyValue > 47 And e.KeyValue < 58) Or (e.KeyValue > 96 And e.KeyValue < 123) Or (e.KeyValue > 64 And e.KeyValue < 91) Or (e.KeyValue = 8) Or (e.KeyValue = 45) Or (e.KeyValue = 46) Then
            Dim s As String = C1Combo1.Text

            If C1Combo1.Text = "" Then
                C1Combo1.DataSource.RowFilter = ""
            Else
                C1Combo1.DataSource.RowFilter = "Yp_Jc like '*" & C1Combo1.Text & "*'"
            End If


            If (e.KeyValue = 8) Then

                If C1Combo1.Text <> "" Then
                    C1Combo1.DroppedDown = False
                    C1Combo1.DroppedDown = True

                Else
                    C1Combo1.DroppedDown = False
                End If
            End If
            C1Combo1.Text = s
            C1Combo1.SelectionStart = C1Combo1.Text.Length
            C1Combo1.SelectionLength = 0
        End If

    End Sub

    Private Sub C1Combo1_KeyDown(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles C1Combo1.KeyDown
        If e.KeyValue = 13 Then
            If C1Combo1.WillChangeToIndex < 1 Then
                If (CType(C1Combo1.DataSource, DataView).Count) = 0 Then
                    MsgBox("药品: '" + Me.C1Combo1.Text + "' 不存在！", MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
                    System.Windows.Forms.SendKeys.Send("^{A}")
                    Exit Sub
                End If
                C1Combo1.SelectedIndex = -1
                C1Combo1.SelectedIndex = 0
            Else
                Dim index As Integer
                index = C1Combo1.WillChangeToIndex
                C1Combo1.SelectedIndex = -1
                C1Combo1.SelectedIndex = index
            End If
            e.Handled = True
            System.Windows.Forms.SendKeys.Send("{Tab}")

        End If
    End Sub

#End Region

#End Region

#Region "数据__编辑"

    Private Sub Data_Clear()
        Rinsert = True
        If Move5.Enabled = True Then Move5.Enabled = False
        C1Combo1.Enabled = True
        C1Combo1.SelectedValue = -1
        C1Combo1.Text = ""                                      '药品明细编码
        C1TextBox1.Text = ""
        C1TextBox2.Text = ""                                    '生产厂家
        C1TextBox3.Text = ""                                    '产品规格
        C1TextBox7.Text = ""                                    '备注
        '药品过滤
        Label4.Text = ""
        C1Numeric1.Value = 0                                    '采购数量
        C1Numeric2.Value = 0                                    '采购单价
        C1Numeric4.Value = 0                                    '采购金额
        C1NumericEdit1.Value = 0
        C1DateEdit1.Value = ""
        C1NumericEdit2.Value = 0
        C1TextBox2.Enabled = True
        C1TextBox3.Enabled = True
        C1Combo1.Select()
        Label5.Text = ""
        Call P_State(False)
    End Sub

    Private Sub Data_Show(ByVal tmp_Row As DataRow)
        C1Combo1.DataSource.RowFilter = ""
        Rinsert = False
        If Rtdbgrid.RowCount = 0 Then Exit Sub
        If Rtdbgrid.Row >= Rtdbgrid.RowCount Then Exit Sub

        If Move5.Enabled = False Then Move5.Enabled = True
        T_Label2.Text = CStr((Rtdbgrid.Row) + 1)
        Call P_State(False)
        Rrow = tmp_Row
        With Rrow
            C1Combo1.Enabled = False                            '药品名称
            C1Combo1.SelectedValue = Rrow.Item("Xx_Code") & ""
            C1TextBox2.Text = .Item("Mx_Cd") & ""             '生产单位
            C1TextBox3.Text = .Item("Mx_Gg") & ""               '药品规格
            C1TextBox7.Text = .Item("Tk_Memo") & ""             '备注
            C1Numeric1.Value = .Item("Tk_Sl")                   '采购数量
            C1NumericEdit2.Value = .Item("Tk_Dj") & ""
            C1Numeric4.Value = .Item("Tk_Money")                '采购金额
        End With
        C1Numeric1.Select()

    End Sub

    Private Sub Save_Add()
        Dim My_Tb As DataTable = RZbtb
        Dim My_NewRow As DataRow = My_Tb.NewRow
        With My_NewRow
            .BeginEdit()
            .Item("Yy_Code") = HisVar.HisVar.WsyCode
            .Item("Tk_Code") = Rcode                 '批发编码
            .Item("Xx_Code") = C1Combo1.SelectedValue                        '药品明细编码
            .Item("Tk_Sl") = C1Numeric1.Value                   '采购单价
            .Item("Tk_Dj") = C1NumericEdit2.Value
            .Item("Tk_Money") = C1Numeric4.Value                '采购金额
            .Item("Tk_Memo") = C1TextBox7.Text & ""             '备注

            .Item("Yp_Ph") = Label4.Text & ""
            .Item("Yp_Yxq") = C1DateEdit1.Value

            .Item("Jx_Name") = Label5.Text
            .Item("Dl_Code") = C1Combo1.Columns("Dl_Code").Value & ""
            .Item("Yp_Name") = C1Combo1.Text & ""
            .Item("Mx_Gg") = C1TextBox3.Text & ""               '药品规格
            .Item("Mx_Gyzz") = C1TextBox1.Text.ToUpper & ""     '批准文号
            .Item("Mx_Cd") = C1TextBox2.Text & ""             '生产单位
            .Item("Mx_CgDw") = C1Combo1.Columns("Mx_Cgdw").Text & ""             '销售单位
            .Item("IsJb") = C1Combo1.Columns("IsJb").Value & ""
            .EndEdit()
        End With


        '数据保存
        Dim Para(6) As SqlClient.SqlParameter
        Para(0) = New SqlParameter("@Yy_Code", SqlDbType.Char)
        Para(1) = New SqlParameter("@Tk_Sl", SqlDbType.Decimal)
        Para(2) = New SqlParameter("@Tk_Dj", SqlDbType.Decimal)
        Para(3) = New SqlParameter("@Tk_Money", SqlDbType.Decimal)
        Para(4) = New SqlParameter("@Tk_Memo", SqlDbType.VarChar)
        Para(5) = New SqlParameter("@Tk_Code", SqlDbType.Char)
        Para(6) = New SqlParameter("@Xx_Code", SqlDbType.VarChar)

        Para(0).Value = HisVar.HisVar.WsyCode
        Para(1).Value = My_NewRow.Item("Tk_Sl")
        Para(2).Value = My_NewRow.Item("Tk_Dj")
        Para(3).Value = My_NewRow.Item("Tk_Money")
        Para(4).Value = My_NewRow.Item("Tk_Memo")
        Para(5).Value = Rcode
        Para(6).Value = My_NewRow.Item("Xx_Code") & ""


        Try
            If Rinsert = True Then
                If Form_Lb = "药库退供应商" Then
                    HisVar.HisVar.Sqldal.ExecuteSql("Insert Into Yk_TkPf2(Yy_Code,Tk_Sl,Tk_Dj,Tk_Money,Tk_Memo,Tk_Code,Xx_Code)Values(@Yy_Code,@Tk_Sl,@Tk_Dj,@Tk_Money,@Tk_Memo,@Tk_Code,@Xx_Code)", Para)
                ElseIf Form_Lb = "药房退供应商" Then
                    HisVar.HisVar.Sqldal.ExecuteSql("Insert Into Yf_TkPf2(Yy_Code,Tk_Sl,Tk_Dj,Tk_Money,Tk_Memo,Tk_Code,Xx_Code)Values(@Yy_Code,@Tk_Sl,@Tk_Dj,@Tk_Money,@Tk_Memo,@Tk_Code,@Xx_Code)", Para)
                End If
                My_Tb.Rows.Add(My_NewRow)
                Rtdbgrid.MoveLast()
                My_NewRow.AcceptChanges()
                Call Data_Clear()
            Else
                If Form_Lb = "药库退供应商" Then
                    HisVar.HisVar.Sqldal.ExecuteSql("Update Yk_TkPf2 Set Yy_Code=@Yy_Code,Tk_Sl=@Tk_Sl,Tk_Dj=@Tk_Dj,Tk_Money=@Tk_Money,Tk_Memo=@Tk_Memo Where Tk_Code=@Tk_Code and Xx_Code=@Xx_Code", Para)
                ElseIf Form_Lb = "药房退供应商" Then
                    HisVar.HisVar.Sqldal.ExecuteSql("Update Yf_TkPf2 Set Yy_Code=@Yy_Code,Tk_Sl=@Tk_Sl,Tk_Dj=@Tk_Dj,Tk_Money=@Tk_Money,Tk_Memo=@Tk_Memo Where Tk_Code=@Tk_Code and Xx_Code=@Xx_Code", Para)
                End If
                Rrow.AcceptChanges()
                C1Numeric1.Select()
                MsgBox("数据修改成功,请继续其他操作!", MsgBoxStyle.Information, "提示")
                Me.Close()

            End If
          
        Catch ex As Exception
            MsgBox(ex.Message, MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
            C1Combo1.Select()
        End Try


    End Sub

    

#End Region

#Region "鼠标__外观"

    Private Sub P_Comm(ByVal Bt As Button)

        With Bt
            Select Case .Tag
                Case "保存"
                    .BackgroundImage = MyResources.C_Resources.getimage("命令_保存1")

                Case "取消"
                    .Location = New Point(Comm1.Left + Comm1.Width + 1, Comm1.Top)
                    .BackgroundImage = MyResources.C_Resources.getimage("命令_取消1")
                    .Width = Me.Comm1.Width
            End Select

            .Text = ""
            .FlatStyle = FlatStyle.Flat
            .FlatAppearance.BorderSize = 0
            .BackgroundImageLayout = ImageLayout.Center
        End With

    End Sub

    Private Sub Comm_MouseEnter(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Comm1.MouseEnter, Comm2.MouseEnter
        Select Case sender.tag
            Case "保存"
                Me.Comm1.BackgroundImage = MyResources.C_Resources.getimage("命令_保存2")
                Me.Comm1.Cursor = Cursors.Hand

            Case "取消"
                Me.Comm2.BackgroundImage = MyResources.C_Resources.getimage("命令_取消2")
                Me.Comm2.Cursor = Cursors.Hand

        End Select

    End Sub

    Private Sub Comm_MouseLeave(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Comm1.MouseLeave, Comm2.MouseLeave
        Select Case sender.tag

            Case "保存"
                Me.Comm1.BackgroundImage = MyResources.C_Resources.getimage("命令_保存1")
                Me.Comm1.Cursor = Cursors.Default

            Case "取消"
                Me.Comm2.BackgroundImage = MyResources.C_Resources.getimage("命令_取消1")
                Me.Comm2.Cursor = Cursors.Default

        End Select

    End Sub

    Private Sub Comm_MouseDown(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles Comm1.MouseDown, Comm2.MouseDown
        Select Case sender.tag
            Case "保存"
                Me.Comm1.BackgroundImage = MyResources.C_Resources.getimage("命令_保存3")

            Case "取消"
                Me.Comm2.BackgroundImage = MyResources.C_Resources.getimage("命令_取消3")
        End Select
    End Sub

    Private Sub Comm_MouseUp(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles Comm1.MouseUp, Comm2.MouseUp
        Select Case sender.tag
            Case "保存"
                Me.Comm1.BackgroundImage = MyResources.C_Resources.getimage("命令_保存2")

            Case "取消"
                Me.Comm2.BackgroundImage = MyResources.C_Resources.getimage("命令_取消2")
        End Select
    End Sub


#End Region

#Region "自定义函数"
    Private Sub P_State(ByVal V_Insert As Boolean)

        If V_Insert = False Then

            C1TextBox1.BackColor = SystemColors.Info
            C1TextBox1.ReadOnly = True
            C1TextBox1.TabStop = False

            C1TextBox3.BackColor = SystemColors.Info
            C1TextBox3.ReadOnly = True
            C1TextBox3.TabStop = False

            C1TextBox2.BackColor = SystemColors.Info
            C1TextBox2.ReadOnly = True
            C1TextBox2.TabStop = False


            C1DateEdit1.BackColor = SystemColors.Info
            C1DateEdit1.ReadOnly = True
            C1DateEdit1.TabStop = False



            C1Numeric2.BackColor = SystemColors.Info
            C1Numeric2.ReadOnly = True
            C1Numeric2.TabStop = False

            C1NumericEdit1.BackColor = SystemColors.Info
            C1NumericEdit1.ReadOnly = True
            C1NumericEdit1.TabStop = False

        Else


            C1TextBox1.BackColor = SystemColors.Window
            C1TextBox1.ReadOnly = False
            C1TextBox1.TabStop = True

            C1TextBox3.BackColor = SystemColors.Window
            C1TextBox3.ReadOnly = False
            C1TextBox3.TabStop = True

            C1TextBox2.BackColor = SystemColors.Window
            C1TextBox2.ReadOnly = False
            C1TextBox2.TabStop = True


            C1DateEdit1.BackColor = SystemColors.Window
            C1DateEdit1.ReadOnly = False
            C1DateEdit1.TabStop = True


            C1Numeric2.BackColor = SystemColors.Window
            C1Numeric2.ReadOnly = False
            C1Numeric2.TabStop = True

            C1NumericEdit1.BackColor = SystemColors.Window
            C1NumericEdit1.ReadOnly = False
            C1NumericEdit1.TabStop = True
        End If
    End Sub
#End Region

#Region "输入法设置"
    '中文
    Private Sub C1TextBox1_Enter(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles C1TextBox2.GotFocus, C1TextBox3.GotFocus, C1TextBox7.GotFocus
        InputLanguage.CurrentInputLanguage = HisVar.HisVar.InputCode
    End Sub

    Private Sub C1Numeric1_KeyDown(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles C1Combo1.GotFocus, C1NumericEdit1.GotFocus, C1Numeric2.GotFocus, C1Numeric1.GotFocus, C1Numeric4.GotFocus, C1NumericEdit2.GotFocus
        InputLanguage.CurrentInputLanguage = InputLanguage.InstalledInputLanguages(0)
    End Sub

#End Region


End Class