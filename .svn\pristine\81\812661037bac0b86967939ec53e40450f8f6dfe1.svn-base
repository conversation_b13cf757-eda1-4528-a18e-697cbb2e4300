﻿/**  版本信息模板在安装目录下，可自行修改。
* M_KC21.cs
*
* 功 能： N/A
* 类 名： M_KC21
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2016/8/24 08:44:38   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
namespace ModelOld
{
	/// <summary>
	/// M_KC21:实体类(属性说明自动提取数据库字段的描述信息)
	/// </summary>
	[Serializable]
	public partial class M_KC21
	{
		public M_KC21()
		{}
		#region Model
		private string _akc190;
		private string _aka130;
		private DateTime _akc192;
		private string _akc193;
		private DateTime? _akc194;
		private string _akc195;
		private string _akc196;
		private string _aae011;
		private DateTime _aae036;
		private decimal? _ckc126;
		private string _zkc271;
		private string _zkc272;
		private string _zkc274;
		private string _zkc275;
		private string _cka040;
		private string _cka041;
		private string _zhuzhi;
		private string _zhiye;
		private string _phone;
		private string _blh;
		private string _amc026;
		private decimal? _amc100;
		private string _amc001;
		private decimal? _amc013;
		private string _amc008;
		private string _aae050;
		private string _aae013;
		private string _akc120;
		private string _ry_bxlb;
		private string _isdk;
		private string _ry_ylcode;
		private string _jf_money;
		private string _zy_jsfs;
        private string _dj_code;
		/// <summary>
		/// 住院号（门诊号）
		/// </summary>
		public string AKC190
		{
			set{ _akc190=value;}
			get{return _akc190;}
		}
		/// <summary>
		/// 医疗类别
		/// </summary>
		public string AKA130
		{
			set{ _aka130=value;}
			get{return _aka130;}
		}
		/// <summary>
		/// 入院日期
		/// </summary>
		public DateTime AKC192
		{
			set{ _akc192=value;}
			get{return _akc192;}
		}
		/// <summary>
		/// 入院诊断疾病编码（门诊特殊病、住院时必录）
		/// </summary>
		public string AKC193
		{
			set{ _akc193=value;}
			get{return _akc193;}
		}
		/// <summary>
		/// 出院日期（出院结算时不能为空）
		/// </summary>
		public DateTime? AKC194
		{
			set{ _akc194=value;}
			get{return _akc194;}
		}
		/// <summary>
		/// 出院原因
		/// </summary>
		public string AKC195
		{
			set{ _akc195=value;}
			get{return _akc195;}
		}
		/// <summary>
		/// 出院疾病诊断编码（费用结算时门诊特殊病、住院必录）
		/// </summary>
		public string AKC196
		{
			set{ _akc196=value;}
			get{return _akc196;}
		}
		/// <summary>
		/// 经办人
		/// </summary>
		public string AAE011
		{
			set{ _aae011=value;}
			get{return _aae011;}
		}
		/// <summary>
		/// 经办日期
		/// </summary>
		public DateTime AAE036
		{
			set{ _aae036=value;}
			get{return _aae036;}
		}
		/// <summary>
		/// 结算标志
		/// </summary>
		public decimal? CKC126
		{
			set{ _ckc126=value;}
			get{return _ckc126;}
		}
		/// <summary>
		/// 医生姓名
		/// </summary>
		public string ZKC271
		{
			set{ _zkc271=value;}
			get{return _zkc271;}
		}
		/// <summary>
		/// 科室名称
		/// </summary>
		public string ZKC272
		{
			set{ _zkc272=value;}
			get{return _zkc272;}
		}
		/// <summary>
		/// 入院诊断疾病名称（门诊特殊病、住院时必录）
		/// </summary>
		public string ZKC274
		{
			set{ _zkc274=value;}
			get{return _zkc274;}
		}
		/// <summary>
		/// 出院疾病诊断名称（费用结算时门诊特殊病、住院必录）
		/// </summary>
		public string ZKC275
		{
			set{ _zkc275=value;}
			get{return _zkc275;}
		}
		/// <summary>
		/// 病房号
		/// </summary>
		public string CKA040
		{
			set{ _cka040=value;}
			get{return _cka040;}
		}
		/// <summary>
		/// 病床号
		/// </summary>
		public string CKA041
		{
			set{ _cka041=value;}
			get{return _cka041;}
		}
		/// <summary>
		/// 住址
		/// </summary>
		public string ZHUZHI
		{
			set{ _zhuzhi=value;}
			get{return _zhuzhi;}
		}
		/// <summary>
		/// 职业
		/// </summary>
		public string ZHIYE
		{
			set{ _zhiye=value;}
			get{return _zhiye;}
		}
		/// <summary>
		/// 患者联系电话
		/// </summary>
		public string PHONE
		{
			set{ _phone=value;}
			get{return _phone;}
		}
		/// <summary>
		/// 病历号
		/// </summary>
		public string BLH
		{
			set{ _blh=value;}
			get{return _blh;}
		}
		/// <summary>
		/// 生育类别（选择生育门诊、生育住院结算时必须录入）
		/// </summary>
		public string AMC026
		{
			set{ _amc026=value;}
			get{return _amc026;}
		}
		/// <summary>
		/// 孕周（选择生育医疗类别时，必须录入）
		/// </summary>
		public decimal? AMC100
		{
			set{ _amc100=value;}
			get{return _amc100;}
		}
		/// <summary>
		/// 准生证号（选择生育类别为1、2、3、4、5、13、15、16、17、18时必须录入）
		/// </summary>
		public string AMC001
		{
			set{ _amc001=value;}
			get{return _amc001;}
		}
		/// <summary>
		/// 胎儿数（选择生育医疗类别为1、5、15时，必须录入，最大为6）
		/// </summary>
		public decimal? AMC013
		{
			set{ _amc013=value;}
			get{return _amc013;}
		}
		/// <summary>
		/// 出生证编号
		/// </summary>
		public string AMC008
		{
			set{ _amc008=value;}
			get{return _amc008;}
		}
		/// <summary>
		/// 离休人员出院带药情况
		/// </summary>
		public string AAE050
		{
			set{ _aae050=value;}
			get{return _aae050;}
		}
		/// <summary>
		/// 备注
		/// </summary>
		public string AAE013
		{
			set{ _aae013=value;}
			get{return _aae013;}
		}
		/// <summary>
		/// 意外伤害标志
		/// </summary>
		public string AKC120
		{
			set{ _akc120=value;}
			get{return _akc120;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string Ry_Bxlb
		{
			set{ _ry_bxlb=value;}
			get{return _ry_bxlb;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string ISDK
		{
			set{ _isdk=value;}
			get{return _isdk;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string RY_YLCODE
		{
			set{ _ry_ylcode=value;}
			get{return _ry_ylcode;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string JF_MONEY
		{
			set{ _jf_money=value;}
			get{return _jf_money;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string ZY_JSFS
		{
			set{ _zy_jsfs=value;}
			get{return _zy_jsfs;}
		}
        /// <summary>
        /// 单据编码
        /// </summary>
        public string Dj_Code
        {
            set { _dj_code = value; }
            get { return _dj_code; }
        }
		#endregion Model

	}
}

